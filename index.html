<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المخازن هيمن كروب</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- مكتبات التصدير والطباعة -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <!-- خط عربي للـ PDF -->
    <script src="https://cdn.jsdelivr.net/npm/jspdf-arabic@1.0.0/dist/jspdf-arabic.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 5px solid;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
        }

        .stat-card.primary { border-color: #3498db; }
        .stat-card.success { border-color: #2ecc71; }
        .stat-card.warning { border-color: #f39c12; }
        .stat-card.danger { border-color: #e74c3c; }
        .stat-card.info { border-color: #17a2b8; }

        .stat-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .stat-card.primary .stat-icon { color: #3498db; }
        .stat-card.success .stat-icon { color: #2ecc71; }
        .stat-card.warning .stat-icon { color: #f39c12; }
        .stat-card.danger .stat-icon { color: #e74c3c; }
        .stat-card.info .stat-icon { color: #17a2b8; }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .panel h2 {
            color: #2c3e50;
            font-size: 1.8rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 600;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            font-family: 'Cairo', sans-serif;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .table-container {
            max-height: 400px;
            overflow-y: auto;
            border-radius: 10px;
            border: 2px solid #ecf0f1;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #ecf0f1;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
            position: sticky;
            top: 0;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .badge-success {
            background: #d4edda;
            color: #155724;
        }

        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }

        .badge-danger {
            background: #f8d7da;
            color: #721c24;
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .hidden {
            display: none;
        }

        /* Navigation Tabs */
        .nav-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 10px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow-x: auto;
            gap: 5px;
        }

        .tab-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 10px;
            background: transparent;
            color: #7f8c8d;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            font-family: 'Cairo', sans-serif;
        }

        .tab-btn:hover {
            background: rgba(52, 152, 219, 0.1);
            color: #3498db;
        }

        .tab-btn.active {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Report Cards */
        .report-card, .setting-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #3498db;
        }

        .report-card h3, .setting-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .report-results {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        /* Action Buttons */
        .action-btn {
            padding: 5px 10px;
            border: none;
            border-radius: 5px;
            font-size: 0.8rem;
            cursor: pointer;
            margin: 0 2px;
            transition: all 0.3s ease;
        }

        .action-btn.edit {
            background: #f39c12;
            color: white;
        }

        .action-btn.delete {
            background: #e74c3c;
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }

            .header h1 {
                font-size: 2rem;
            }

            .nav-tabs {
                flex-wrap: wrap;
            }

            .tab-btn {
                flex: 1;
                min-width: 120px;
            }
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* أنماط الطباعة */
        @media print {
            body * {
                visibility: hidden;
            }

            .print-area, .print-area * {
                visibility: visible;
            }

            .print-area {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                background: white;
                color: black;
                font-family: 'Cairo', sans-serif;
            }

            .print-header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #333;
                padding-bottom: 20px;
            }

            .print-logo {
                width: 60px;
                height: 60px;
                margin: 0 auto 10px;
            }

            .print-title {
                font-size: 24px;
                font-weight: bold;
                margin: 10px 0;
            }

            .print-company {
                font-size: 18px;
                color: #666;
            }

            .print-table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
            }

            .print-table th,
            .print-table td {
                border: 1px solid #333;
                padding: 8px;
                text-align: right;
                font-size: 12px;
            }

            .print-table th {
                background: #f0f0f0;
                font-weight: bold;
            }

            .print-footer {
                margin-top: 30px;
                text-align: center;
                font-size: 12px;
                color: #666;
            }
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <div>
                    <h1><i class="fas fa-warehouse"></i> نظام إدارة المخازن هيمن كروب</h1>
                </div>
                <div style="text-align: left;">
                    <!-- يمكنك وضع ملف الصورة في نفس المجلد وتغيير اسم الملف هنا -->
                    <img src="logo.jpg" alt="Heiman Group Logo" style="width: 80px; height: 80px; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); object-fit: cover;"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">

                    <!-- لوجو احتياطي إذا لم توجد الصورة -->
                    <div id="fallbackLogo" style="display: none; width: 80px; height: 80px; background: transparent; border-radius: 10px; display: flex; align-items: center; justify-content: center; color: #2c3e50; font-weight: bold; font-size: 12px; text-align: center;">
                        <!-- لا يوجد لوجو احتياطي -->
                    </div>

                    <div style="color: #2c3e50; font-weight: bold; margin-top: 5px;">هيمن كروب</div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card primary">
                <div class="stat-icon">
                    <i class="fas fa-boxes"></i>
                </div>
                <div class="stat-number" id="totalItems">0</div>
                <div class="stat-label">إجمالي المواد</div>
            </div>
            

            
            <div class="stat-card warning">
                <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-number" id="lowStockItems">0</div>
                <div class="stat-label">مواد منخفضة</div>
            </div>


            
            <div class="stat-card danger">
                <div class="stat-icon">
                    <i class="fas fa-exchange-alt"></i>
                </div>
                <div class="stat-number" id="todayTransactions">0</div>
                <div class="stat-label">معاملات اليوم</div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="nav-tabs">
            <button class="tab-btn active" onclick="showTab('dashboard')">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </button>
            <button class="tab-btn" onclick="showTab('transactions')">
                <i class="fas fa-exchange-alt"></i> المعاملات
            </button>
            <button class="tab-btn" onclick="showTab('inventory')">
                <i class="fas fa-boxes"></i> المخزون
            </button>

            <button class="tab-btn" onclick="showTab('reports')">
                <i class="fas fa-chart-bar"></i> التقارير
            </button>

            <button class="tab-btn" onclick="showTab('settings')">
                <i class="fas fa-cog"></i> الإعدادات
            </button>
        </div>

        <!-- Dashboard Tab -->
        <div id="dashboard" class="tab-content active">
            <div class="main-content">
                <!-- Add Item Form -->
                <div class="panel">
                    <h2>
                        <i class="fas fa-plus-circle"></i>
                        إضافة معاملة جديدة
                    </h2>
                
                <div id="alertContainer"></div>
                
                <form id="itemForm">
                    <div class="form-group">
                        <label for="itemName">اسم المادة *</label>
                        <input type="text" id="itemName" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="quantity">الكمية *</label>
                        <input type="number" id="quantity" class="form-control" step="0.01" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="unit">الوحدة *</label>
                        <input type="text" id="unit" class="form-control" placeholder="كيلو، متر، قطعة..." required>
                    </div>
                    
                    <div class="form-group">
                        <label for="transactionType">نوع المعاملة *</label>
                        <select id="transactionType" class="form-control" required>
                            <option value="">اختر نوع المعاملة</option>
                            <option value="وارد">وارد</option>
                            <option value="صادر">صادر</option>
                            <option value="تسوية">تسوية مخزون</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="transactionDate">تاريخ المعاملة *</label>
                        <input type="datetime-local" id="transactionDate" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="unitPrice">سعر الوحدة *</label>
                        <div style="display: flex; gap: 10px;">
                            <input type="number" id="unitPrice" class="form-control" step="0.01" min="0" required style="flex: 2;">
                            <select id="currency" class="form-control" style="flex: 1;">
                                <option value="IQD">د.ع</option>
                                <option value="USD">دولار</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="totalCost">التكلفة الإجمالية</label>
                        <input type="text" id="totalCost" class="form-control" readonly style="background: #f8f9fa; font-weight: bold;">
                        <small class="text-muted">يتم حسابها تلقائياً (الكمية × السعر)</small>
                    </div>

                    <div class="form-group">
                        <label>سعر الصرف الحالي</label>
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <input type="number" id="currentExchangeRate" class="form-control" readonly style="background: #e3f2fd; font-weight: bold; flex: 1;">
                            <button type="button" class="btn btn-info" onclick="showExchangeRateModal()" style="white-space: nowrap;">
                                <i class="fas fa-edit"></i> تعديل
                            </button>
                        </div>
                        <small class="text-muted">سعر الدولار الواحد بالدينار العراقي</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="warehouse">المخزن *</label>
                        <select id="warehouse" class="form-control" required>
                            <option value="">اختر المخزن</option>
                            <option value="المخزن الرئيسي">المخزن الرئيسي</option>
                            <option value="مخزن فرعي 1">مخزن فرعي 1</option>
                            <option value="مخزن فرعي 2">مخزن فرعي 2</option>
                            <option value="مخزن فرعي 3">مخزن فرعي 3</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="supplier">المورد/المستلم</label>
                        <input type="text" id="supplier" class="form-control" placeholder="اسم المورد أو المستلم">
                    </div>

                    <div class="form-group">
                        <label for="invoiceNumber">رقم الفاتورة</label>
                        <input type="text" id="invoiceNumber" class="form-control" placeholder="رقم الفاتورة أو المرجع">
                    </div>
                    
                    <div class="form-group">
                        <label for="notes">ملاحظات</label>
                        <textarea id="notes" class="form-control" rows="3"></textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-success" style="width: 100%;">
                        <i class="fas fa-save"></i>
                        حفظ المعاملة
                    </button>
                </form>
            </div>

            <!-- Inventory Table -->
            <div class="panel">
                <h2>
                    <i class="fas fa-list"></i>
                    المخزون الحالي
                    <button class="btn btn-primary" onclick="refreshData()" style="margin-right: auto; font-size: 0.9rem;">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                </h2>
                
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>المادة</th>
                                <th>الكمية</th>
                                <th>الوحدة</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody id="inventoryTableBody">
                            <tr>
                                <td colspan="4" style="text-align: center; color: #7f8c8d;">
                                    <i class="fas fa-box-open"></i>
                                    لا توجد مواد في المخزون
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

        <!-- Transactions Tab -->
        <div id="transactions" class="tab-content">
            <div class="panel">
                <h2>
                    <i class="fas fa-list"></i>
                    سجل المعاملات
                    <div style="margin-right: auto; display: flex; gap: 10px; flex-wrap: wrap;">
                        <input type="date" id="filterDateFrom" class="form-control" style="width: auto;">
                        <input type="date" id="filterDateTo" class="form-control" style="width: auto;">
                        <button class="btn btn-primary" onclick="filterTransactions()">
                            <i class="fas fa-filter"></i> تصفية
                        </button>
                        <button class="btn btn-success" onclick="printTransactions()">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                        <button class="btn btn-danger" onclick="exportTransactionsPDF()">
                            <i class="fas fa-file-pdf"></i> PDF
                        </button>
                        <button class="btn btn-warning" onclick="exportTransactionsExcel()">
                            <i class="fas fa-file-excel"></i> Excel
                        </button>
                    </div>
                </h2>

                <div class="table-container" style="max-height: 500px;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>المادة</th>
                                <th>النوع</th>
                                <th>الكمية</th>
                                <th>المخزن</th>
                                <th>المورد/المستلم</th>
                                <th>الفاتورة</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="transactionsTableBody">
                            <tr>
                                <td colspan="8" style="text-align: center; color: #7f8c8d;">
                                    <i class="fas fa-exchange-alt"></i>
                                    لا توجد معاملات
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Inventory Tab -->
        <div id="inventory" class="tab-content">
            <div class="panel">
                <h2>
                    <i class="fas fa-boxes"></i>
                    إدارة المخزون
                    <div style="margin-right: auto; display: flex; gap: 10px; flex-wrap: wrap;">
                        <input type="text" id="searchInventory" class="form-control" placeholder="البحث في المخزون..." style="width: 200px;">
                        <button class="btn btn-primary" onclick="searchInventory()">
                            <i class="fas fa-search"></i> بحث
                        </button>
                        <button class="btn btn-success" onclick="printInventory()">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                        <button class="btn btn-danger" onclick="exportInventoryPDF()">
                            <i class="fas fa-file-pdf"></i> PDF
                        </button>
                        <button class="btn btn-warning" onclick="exportInventoryExcel()">
                            <i class="fas fa-file-excel"></i> Excel
                        </button>
                    </div>
                </h2>

                <div class="table-container" style="max-height: 500px;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>المادة</th>
                                <th>الكمية الحالية</th>
                                <th>الوحدة</th>
                                <th>آخر حركة</th>
                                <th>الحالة</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="inventoryTableBodyDetailed">
                            <tr>
                                <td colspan="6" style="text-align: center; color: #7f8c8d;">
                                    <i class="fas fa-box-open"></i>
                                    لا توجد مواد في المخزون
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>





        <!-- Reports Tab -->
        <div id="reports" class="tab-content">
            <div class="panel">
                <h2>
                    <i class="fas fa-chart-bar"></i>
                    التقارير والإحصائيات
                </h2>

                <div class="reports-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">
                    <div class="report-card">
                        <h3><i class="fas fa-calendar-day"></i> تقرير يومي</h3>
                        <input type="date" id="dailyReportDate" class="form-control">
                        <select id="dailyReportFilter" class="form-control" style="margin-top: 10px;">
                            <option value="all">جميع المعاملات</option>
                            <option value="وارد">الوارد فقط</option>
                            <option value="صادر">الصادر فقط</option>
                            <option value="تسوية">التسوية فقط</option>
                            <option value="نقل">النقل فقط</option>
                        </select>
                        <div style="display: flex; gap: 5px; margin-top: 10px;">
                            <button class="btn btn-primary" onclick="generateDailyReport()">إنشاء</button>
                            <button class="btn btn-success" onclick="printDailyReport()"><i class="fas fa-print"></i></button>
                            <button class="btn btn-danger" onclick="exportDailyReportPDF()"><i class="fas fa-file-pdf"></i></button>
                        </div>
                    </div>

                    <div class="report-card">
                        <h3><i class="fas fa-calendar-week"></i> تقرير أسبوعي</h3>
                        <input type="week" id="weeklyReportDate" class="form-control">
                        <select id="weeklyReportFilter" class="form-control" style="margin-top: 10px;">
                            <option value="all">جميع المعاملات</option>
                            <option value="وارد">الوارد فقط</option>
                            <option value="صادر">الصادر فقط</option>
                            <option value="تسوية">التسوية فقط</option>
                            <option value="نقل">النقل فقط</option>
                        </select>
                        <div style="display: flex; gap: 5px; margin-top: 10px;">
                            <button class="btn btn-primary" onclick="generateWeeklyReport()">إنشاء</button>
                            <button class="btn btn-success" onclick="printWeeklyReport()"><i class="fas fa-print"></i></button>
                            <button class="btn btn-danger" onclick="exportWeeklyReportPDF()"><i class="fas fa-file-pdf"></i></button>
                        </div>
                    </div>

                    <div class="report-card">
                        <h3><i class="fas fa-calendar-alt"></i> تقرير شهري</h3>
                        <input type="month" id="monthlyReportDate" class="form-control">
                        <select id="monthlyReportFilter" class="form-control" style="margin-top: 10px;">
                            <option value="all">جميع المعاملات</option>
                            <option value="وارد">الوارد فقط</option>
                            <option value="صادر">الصادر فقط</option>
                            <option value="تسوية">التسوية فقط</option>
                            <option value="نقل">النقل فقط</option>
                        </select>
                        <div style="display: flex; gap: 5px; margin-top: 10px;">
                            <button class="btn btn-primary" onclick="generateMonthlyReport()">إنشاء</button>
                            <button class="btn btn-success" onclick="printMonthlyReport()"><i class="fas fa-print"></i></button>
                            <button class="btn btn-danger" onclick="exportMonthlyReportPDF()"><i class="fas fa-file-pdf"></i></button>
                        </div>
                    </div>

                    <div class="report-card">
                        <h3><i class="fas fa-warehouse"></i> تقرير المخزن</h3>
                        <select id="warehouseReportSelect" class="form-control">
                            <option value="">اختر المخزن</option>
                        </select>
                        <select id="warehouseReportFilter" class="form-control" style="margin-top: 10px;">
                            <option value="all">جميع المعاملات</option>
                            <option value="وارد">الوارد فقط</option>
                            <option value="صادر">الصادر فقط</option>
                            <option value="تسوية">التسوية فقط</option>
                            <option value="نقل">النقل فقط</option>
                        </select>
                        <div style="display: flex; gap: 5px; margin-top: 10px;">
                            <button class="btn btn-primary" onclick="generateWarehouseReport()">إنشاء</button>
                            <button class="btn btn-success" onclick="printWarehouseReport()"><i class="fas fa-print"></i></button>
                            <button class="btn btn-danger" onclick="exportWarehouseReportPDF()"><i class="fas fa-file-pdf"></i></button>
                        </div>
                    </div>
                </div>

                <div id="reportResults" class="report-results"></div>
            </div>
        </div>

        <!-- Settings Tab -->
        <div id="settings" class="tab-content">
            <div class="panel">
                <h2>
                    <i class="fas fa-cog"></i>
                    إعدادات النظام
                </h2>

                <div class="settings-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <div class="setting-card">
                        <h3><i class="fas fa-building"></i> معلومات الشركة</h3>
                        <div class="form-group">
                            <label>اسم الشركة</label>
                            <input type="text" id="companyName" class="form-control" value="هيمن كروب">
                        </div>

                        <div class="form-group">
                            <label>سعر صرف الدولار (د.ع)</label>
                            <input type="number" id="exchangeRate" class="form-control" value="1500" step="1" min="1000" max="2000">
                            <small class="text-muted">سعر الدولار الواحد بالدينار العراقي</small>
                        </div>
                        <div class="form-group">
                            <label>العنوان</label>
                            <input type="text" id="companyAddress" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>الهاتف</label>
                            <input type="text" id="companyPhone" class="form-control">
                        </div>
                        <button class="btn btn-success" onclick="saveCompanyInfo()">حفظ</button>
                    </div>

                    <div class="setting-card">
                        <h3><i class="fas fa-database"></i> إدارة البيانات</h3>
                        <button class="btn btn-success" onclick="exportAllData()" style="width: 100%; margin-bottom: 10px;">
                            <i class="fas fa-download"></i> تصدير جميع البيانات
                        </button>
                        <input type="file" id="importFile" accept=".json" style="display: none;" onchange="importData(this.files[0])">
                        <button class="btn btn-primary" onclick="document.getElementById('importFile').click()" style="width: 100%; margin-bottom: 10px;">
                            <i class="fas fa-upload"></i> استيراد البيانات
                        </button>
                        <button class="btn btn-warning" onclick="addSampleData()" style="width: 100%; margin-bottom: 10px;">
                            <i class="fas fa-plus"></i> إضافة بيانات تجريبية
                        </button>
                        <button class="btn btn-danger" onclick="clearAllData()" style="width: 100%;">
                            <i class="fas fa-trash"></i> مسح جميع البيانات
                        </button>
                    </div>
                </div>
            </div>
        </div>

    <!-- مودال تعديل سعر الصرف -->
    <div id="exchangeRateModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); min-width: 450px;">
            <h3 style="margin-bottom: 20px; color: #2c3e50;">تعديل سعر صرف الدولار</h3>

            <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                <div style="color: #856404; font-weight: bold; margin-bottom: 5px;">
                    <i class="fas fa-exclamation-triangle"></i> تنبيه مهم
                </div>
                <div style="color: #856404; font-size: 14px;">
                    تغيير سعر الصرف سيؤثر فقط على المعاملات الجديدة. المعاملات السابقة ستحتفظ بسعر الصرف وقت إجرائها.
                </div>
            </div>

            <div class="form-group">
                <label>سعر الدولار الواحد بالدينار العراقي:</label>
                <input type="number" id="newExchangeRate" class="form-control" step="1" min="1000" max="2000" style="font-size: 18px; text-align: center;" onkeypress="if(event.key==='Enter') updateExchangeRate()">
            </div>
            <div style="display: flex; gap: 10px; margin-top: 20px;">
                <button class="btn btn-success" onclick="updateExchangeRate()" style="flex: 1;">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <button class="btn btn-secondary" onclick="closeExchangeRateModal()" style="flex: 1;">
                    <i class="fas fa-times"></i> إلغاء
                </button>
            </div>
        </div>
    </div>

    <!-- مكتبات التصدير والطباعة -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
