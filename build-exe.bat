@echo off
chcp 65001 >nul
title بناء نظام إدارة المخزون - هيمن كروب
color 0A

echo.
echo ========================================
echo    نظام إدارة المخزون - هيمن كروب
echo        بناء ملف .exe تلقائي
echo ========================================
echo.

REM فحص Python
echo [1/6] فحص Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت!
    echo.
    echo يرجى تثبيت Python من: https://python.org
    echo تأكد من اختيار "Add Python to PATH" أثناء التثبيت
    echo.
    pause
    exit /b 1
)
echo ✅ Python متوفر

REM فحص pip
echo [2/6] فحص pip...
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ pip غير متوفر!
    pause
    exit /b 1
)
echo ✅ pip متوفر

REM تثبيت PyInstaller
echo [3/6] تثبيت PyInstaller...
pip install pyinstaller --quiet --disable-pip-version-check
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت PyInstaller!
    echo جرب تشغيل Command Prompt كمدير
    pause
    exit /b 1
)
echo ✅ PyInstaller جاهز

REM تنظيف الملفات القديمة
echo [4/6] تنظيف الملفات القديمة...
if exist "dist" rmdir /s /q "dist" >nul 2>&1
if exist "build" rmdir /s /q "build" >nul 2>&1
if exist "__pycache__" rmdir /s /q "__pycache__" >nul 2>&1
echo ✅ تم التنظيف

REM بناء التطبيق
echo [5/6] بناء التطبيق...
echo هذا قد يستغرق بضع دقائق...
echo.

REM استخدام PyInstaller مع الإعدادات المحسنة
pyinstaller --onefile --windowed --name "نظام إدارة المخزون - هيمن كروب" --add-data "index-professional.html;." --distpath "dist" app.py

if %errorlevel% neq 0 (
    echo ❌ فشل في بناء التطبيق!
    echo.
    echo تحقق من:
    echo 1. وجود ملف index-professional.html
    echo 2. صلاحيات الكتابة في المجلد
    echo 3. مساحة كافية على القرص
    pause
    exit /b 1
)

REM نسخ الملفات الإضافية
echo [6/6] نسخ الملفات الإضافية...
if exist "assets" (
    xcopy "assets" "dist\assets\" /E /I /Q >nul 2>&1
    echo ✅ تم نسخ مجلد assets
)

if exist "index-professional.html" (
    copy "index-professional.html" "dist\" >nul 2>&1
    echo ✅ تم نسخ ملف HTML
)

echo.
echo ========================================
echo ✅ تم إنشاء ملف .exe بنجاح!
echo ========================================
echo.

REM عرض معلومات الملف المنشأ
if exist "dist\نظام إدارة المخزون - هيمن كروب.exe" (
    echo 📁 مكان الملف: dist\نظام إدارة المخزون - هيمن كروب.exe
    
    REM حساب حجم الملف
    for %%A in ("dist\نظام إدارة المخزون - هيمن كروب.exe") do (
        set size=%%~zA
        set /a sizeMB=!size!/1024/1024
        echo 📊 حجم الملف: !sizeMB! ميجابايت تقريباً
    )
    
    echo.
    echo 🎉 التطبيق جاهز للاستخدام!
    echo.
    echo للتشغيل:
    echo انقر نقراً مزدوجاً على الملف في مجلد dist
    echo.
    
    echo فتح مجلد الملفات...
    start explorer "dist"
    
) else (
    echo ❌ لم يتم العثور على الملف المنشأ!
    echo تحقق من مجلد dist
)

echo.
echo ملاحظات مهمة:
echo • الملف يعمل بدون تثبيت Python
echo • يمكن نسخه إلى أي جهاز Windows
echo • البيانات تُحفظ في مجلد المستخدم
echo • لا يحتاج اتصال إنترنت
echo.
pause
