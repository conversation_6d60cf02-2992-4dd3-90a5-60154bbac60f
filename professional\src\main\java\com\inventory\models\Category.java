package com.inventory.models;

/**
 * نموذج فئة المواد - نظام إدارة المخزون الاحترافي
 */
public class Category {
    private int id;
    private String name;
    private String description;
    private String color; // لون الفئة في الواجهة
    private boolean isActive;
    
    // Constructors
    public Category() {
        this.isActive = true;
    }
    
    public Category(String name, String description, String color) {
        this();
        this.name = name;
        this.description = description;
        this.color = color;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getColor() {
        return color;
    }
    
    public void setColor(String color) {
        this.color = color;
    }
    
    public boolean isActive() {
        return isActive;
    }
    
    public void setActive(boolean active) {
        isActive = active;
    }
    
    @Override
    public String toString() {
        return name;
    }
}
