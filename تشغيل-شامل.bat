@echo off
chcp 65001 >nul
title نظام إدارة المخزون - هيمن كروب
color 0A

:menu
cls
echo.
echo ========================================
echo    نظام إدارة المخزون - هيمن كروب
echo ========================================
echo.
echo اختر طريقة التشغيل:
echo.
echo [1] إنشاء ملف .exe (مرة واحدة فقط)
echo [2] تشغيل ملف .exe (إذا كان موجود)
echo [3] تشغيل في المتصفح مباشرة
echo [4] تشغيل تطبيق Python (للمطورين)
echo [5] عرض دليل الاستخدام
echo [6] خروج
echo.
set /p choice="اختر رقم (1-6): "

if "%choice%"=="1" goto build_exe
if "%choice%"=="2" goto run_exe
if "%choice%"=="3" goto run_browser
if "%choice%"=="4" goto run_python
if "%choice%"=="5" goto show_guide
if "%choice%"=="6" goto exit
goto invalid

:build_exe
cls
echo.
echo إنشاء ملف .exe...
echo ==================
echo.
if exist "build-exe.bat" (
    call build-exe.bat
) else (
    echo ❌ ملف build-exe.bat غير موجود!
    pause
)
goto menu

:run_exe
cls
echo.
echo تشغيل ملف .exe...
echo ==================
echo.
if exist "dist\نظام إدارة المخزون - هيمن كروب.exe" (
    echo ✅ تشغيل التطبيق...
    start "" "dist\نظام إدارة المخزون - هيمن كروب.exe"
    echo تم تشغيل التطبيق بنجاح!
    timeout /t 3 >nul
) else (
    echo ❌ ملف .exe غير موجود!
    echo يرجى إنشاؤه أولاً (اختيار 1)
    pause
)
goto menu

:run_browser
cls
echo.
echo تشغيل في المتصفح...
echo ====================
echo.
if exist "index-professional.html" (
    echo ✅ فتح النظام في المتصفح...
    start "" "index-professional.html"
    echo تم فتح النظام بنجاح!
    timeout /t 3 >nul
) else (
    echo ❌ ملف index-professional.html غير موجود!
    pause
)
goto menu

:run_python
cls
echo.
echo تشغيل تطبيق Python...
echo ======================
echo.
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت!
    echo يرجى تثبيت Python من: https://python.org
    pause
    goto menu
)

if exist "app.py" (
    echo ✅ تشغيل التطبيق...
    python app.py
) else (
    echo ❌ ملف app.py غير موجود!
    pause
)
goto menu

:show_guide
cls
echo.
echo دليل الاستخدام
echo ===============
echo.
if exist "دليل-إنشاء-exe.txt" (
    type "دليل-إنشاء-exe.txt"
) else (
    echo الدليل الأساسي:
    echo ================
    echo.
    echo 1. لإنشاء ملف .exe:
    echo    - ثبت Python من python.org
    echo    - اختر الخيار [1] من القائمة
    echo    - انتظر اكتمال البناء
    echo.
    echo 2. لتشغيل النظام:
    echo    - اختر الخيار [2] لتشغيل .exe
    echo    - أو اختر [3] للمتصفح مباشرة
    echo.
    echo 3. الميزات:
    echo    - إدارة مخزون شاملة
    echo    - نظام مالي متقدم
    echo    - تقارير وطباعة احترافية
)
echo.
pause
goto menu

:invalid
echo.
echo ❌ اختيار غير صحيح!
echo يرجى اختيار رقم من 1 إلى 6
pause
goto menu

:exit
cls
echo.
echo ========================================
echo شكراً لاستخدام نظام إدارة المخزون
echo هيمن كروب - 2024
echo ========================================
echo.
timeout /t 2 >nul
exit

:end
