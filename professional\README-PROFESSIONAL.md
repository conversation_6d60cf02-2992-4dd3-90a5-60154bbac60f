# نظام إدارة المخزون الاحترافي v2.0

نظام شامل ومتقدم لإدارة المخزون مطور بـ Java مع ميزات احترافية متكاملة.

## 🌟 الميزات الاحترافية

### 🔐 نظام المستخدمين والصلاحيات
- **أدوار متعددة:** مدير النظام، مدير المخزون، موظف، مشاهد
- **تشفير متقدم:** تشفير كلمات المرور بـ SHA-256 مع Salt
- **جلسات آمنة:** إدارة جلسات المستخدمين مع انتهاء صلاحية تلقائي
- **سجل الأنشطة:** تتبع جميع العمليات والتغييرات

### 📊 إدارة المخزون المتقدمة
- **فئات المواد:** تصنيف المواد حسب الفئات مع ألوان مميزة
- **معلومات شاملة:** باركود، موقع التخزين، صور المواد
- **تنبيهات ذكية:** تنبيهات المخزون المنخفض والزائد
- **تتبع الأسعار:** تتبع آخر أسعار الشراء والبيع

### 💼 إدارة المعاملات المحسنة
- **أنواع متعددة:** وارد، صادر، تسوية، نقل
- **حالات المعاملات:** معلق، مؤكد، ملغي
- **أرقام مرجعية:** ربط المعاملات بالفواتير والمراجع
- **تتبع المستخدمين:** ربط كل معاملة بالمستخدم المسؤول

### 🎨 واجهة مستخدم حديثة
- **تصميم عصري:** واجهة نظيفة ومتجاوبة
- **ألوان متناسقة:** نظام ألوان احترافي
- **أيقونات ملونة:** تمييز بصري للوظائف المختلفة
- **دعم العربية:** واجهة كاملة باللغة العربية

### 📈 لوحة التحكم والإحصائيات
- **إحصائيات فورية:** عرض الأرقام المهمة في الوقت الفعلي
- **بطاقات ملونة:** عرض بصري جذاب للبيانات
- **سجل الأنشطة:** متابعة آخر العمليات
- **تحديث تلقائي:** تحديث البيانات تلقائياً

## 🚀 التشغيل السريع

### المتطلبات
- **Java 8 أو أحدث**
- **نظام Windows** (يمكن تشغيله على أنظمة أخرى مع تعديلات بسيطة)
- **اتصال بالإنترنت** (لتحميل المكتبات في المرة الأولى)

### خطوات التشغيل

1. **تجميع النظام:**
   ```
   compile-professional.bat
   ```

2. **تشغيل النظام:**
   ```
   run-professional.bat
   ```

3. **تسجيل الدخول:**
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`

## 📁 هيكل المشروع

```
professional/
├── src/main/java/com/inventory/
│   ├── models/                    # نماذج البيانات
│   │   ├── User.java             # نموذج المستخدم
│   │   ├── Category.java         # نموذج الفئة
│   │   ├── Item.java             # نموذج المادة المحسن
│   │   └── Transaction.java      # نموذج المعاملة المحسن
│   ├── database/                 # طبقة قاعدة البيانات
│   │   └── DatabaseManager.java # مدير قاعدة البيانات المتقدم
│   ├── utils/                    # الأدوات المساعدة
│   │   └── PasswordUtils.java    # أدوات تشفير كلمات المرور
│   ├── gui/                      # واجهة المستخدم
│   │   ├── LoginDialog.java      # نافذة تسجيل الدخول
│   │   └── MainFrame.java        # الواجهة الرئيسية
│   └── ProfessionalInventoryApp.java # التطبيق الرئيسي
├── lib/                          # المكتبات المطلوبة
├── out/                          # الملفات المجمعة
├── compile-professional.bat      # ملف التجميع
├── run-professional.bat          # ملف التشغيل المتقدم
└── run-professional-simple.bat   # ملف التشغيل البسيط
```

## 🔧 المكتبات المستخدمة

- **SQLite JDBC 3.44.1.0** - قاعدة البيانات
- **SLF4J 1.7.36** - نظام السجلات
- **FlatLaf 3.2.5** - واجهة مستخدم حديثة (اختياري)

## 👥 أدوار المستخدمين

### 🔴 مدير النظام (ADMIN)
- جميع الصلاحيات
- إدارة المستخدمين
- إعدادات النظام
- النسخ الاحتياطي

### 🟠 مدير المخزون (MANAGER)
- إدارة المواد والفئات
- إدارة المعاملات
- عرض التقارير
- إدارة المشاريع

### 🟡 موظف (EMPLOYEE)
- إضافة المعاملات
- عرض المخزون
- عرض التقارير الأساسية

### 🟢 مشاهد (VIEWER)
- عرض المخزون فقط
- عرض التقارير فقط

## 🛡️ الأمان والحماية

- **تشفير كلمات المرور:** SHA-256 مع Salt عشوائي
- **جلسات آمنة:** انتهاء صلاحية تلقائي للجلسات
- **سجل الأنشطة:** تتبع جميع العمليات
- **صلاحيات متدرجة:** تحكم دقيق في الوصول

## 📊 قاعدة البيانات

### الجداول الرئيسية:
- **users** - المستخدمون
- **categories** - فئات المواد
- **items** - المواد (محسن)
- **transactions** - المعاملات (محسن)
- **activity_log** - سجل الأنشطة

### الفهارس:
- فهارس محسنة للبحث السريع
- فهارس على الحقول المهمة
- تحسين الأداء للاستعلامات الكبيرة

## 🎯 الميزات القادمة

- [ ] تقارير PDF متقدمة
- [ ] رسوم بيانية تفاعلية
- [ ] نظام النسخ الاحتياطي التلقائي
- [ ] إشعارات سطح المكتب
- [ ] تصدير البيانات (Excel, CSV)
- [ ] واجهة ويب
- [ ] تطبيق الهاتف المحمول

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في تشغيل Java:**
   - تأكد من تثبيت Java 8 أو أحدث
   - تحقق من متغير PATH

2. **فشل تحميل المكتبات:**
   - تأكد من وجود اتصال بالإنترنت
   - شغل compile-professional.bat كمدير

3. **مشاكل في قاعدة البيانات:**
   - تأكد من عدم فتح الملف في برنامج آخر
   - احذف ملف professional_inventory.db لإعادة الإنشاء

4. **مشاكل في الواجهة:**
   - استخدم run-professional-simple.bat
   - تحقق من دعم النظام للخطوط العربية

## 📞 الدعم الفني

للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. تحقق من رسائل الخطأ في وحدة التحكم
3. تأكد من استيفاء جميع المتطلبات

## 📄 الترخيص

هذا النظام مطور لأغراض تعليمية وتجارية. جميع الحقوق محفوظة © 2024

---

**تم تطوير هذا النظام بأعلى معايير الجودة والأمان لضمان تجربة مستخدم احترافية متميزة.**
