@echo off
echo ========================================
echo نظام إدارة المخزون الاحترافي (الإصدار البسيط)
echo ========================================
echo.

REM التحقق من وجود الملفات المجمعة
if not exist "out\com\inventory\ProfessionalInventoryApp.class" (
    echo ❌ الملفات غير مجمعة!
    echo يرجى تشغيل compile-professional.bat أولاً
    pause
    exit /b 1
)

echo 🚀 تشغيل النظام...
echo.

REM تشغيل التطبيق بالطريقة البسيطة (للأنظمة القديمة)
java -Dfile.encoding=UTF-8 -cp "out;lib/*" com.inventory.ProfessionalInventoryApp

echo.
echo تم إغلاق النظام.
pause
