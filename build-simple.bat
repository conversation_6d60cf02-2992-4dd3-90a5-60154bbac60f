@echo off
echo ========================================
echo    إنشاء ملف .exe بسيط
echo ========================================
echo.

echo [1/3] فحص المتطلبات...

REM فحص Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت!
    echo.
    echo يرجى تثبيت Node.js من: https://nodejs.org
    echo ثم أعد تشغيل هذا الملف
    pause
    exit /b 1
)
echo ✅ Node.js متوفر

REM فحص npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متوفر!
    pause
    exit /b 1
)
echo ✅ npm متوفر

echo.
echo [2/3] تثبيت المتطلبات...
echo هذا قد يستغرق بضع دقائق في المرة الأولى...
echo.

call npm install --silent
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المتطلبات!
    echo.
    echo جرب الحلول التالية:
    echo 1. تأكد من اتصال الإنترنت
    echo 2. شغل Command Prompt كمدير
    echo 3. احذف مجلد node_modules وأعد المحاولة
    pause
    exit /b 1
)
echo ✅ تم تثبيت المتطلبات

echo.
echo [3/3] بناء التطبيق...
echo جاري إنشاء ملف .exe...
echo.

call npm run build-win
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء التطبيق!
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ تم إنشاء ملف .exe بنجاح!
echo ========================================
echo.
echo الملفات المنشأة:
echo 📁 dist\نظام إدارة المخزون Setup.exe
echo 📁 dist\win-unpacked\
echo.

if exist "dist" (
    echo فتح مجلد الملفات...
    start explorer dist
) else (
    echo ⚠️ مجلد dist غير موجود!
)

echo.
echo 🎉 البرنامج جاهز للاستخدام!
echo.
pause
