@echo off
chcp 65001 >nul
title نسخ احتياطي للنظام
color 0A

echo.
echo ========================================
echo    نسخ احتياطي لنظام إدارة المخزون
echo ========================================
echo.

REM إنشاء مجلد النسخ الاحتياطية
set backup_folder=backup_%date:~-4,4%-%date:~-10,2%-%date:~-7,2%_%time:~0,2%-%time:~3,2%
set backup_folder=%backup_folder: =0%

if not exist "backups" mkdir "backups"
mkdir "backups\%backup_folder%"

echo 💾 إنشاء نسخة احتياطية...
echo 📁 المجلد: backups\%backup_folder%
echo.

REM نسخ الملفات المهمة
echo [1/5] نسخ ملف النظام الأساسي...
if exist "index-professional.html" (
    copy "index-professional.html" "backups\%backup_folder%\" >nul
    echo ✅ تم نسخ index-professional.html
) else (
    echo ❌ ملف index-professional.html غير موجود
)

echo [2/5] نسخ ملف .exe...
if exist "dist\نظام-المخزون-هيمن-كروب.exe" (
    copy "dist\نظام-المخزون-هيمن-كروب.exe" "backups\%backup_folder%\" >nul
    echo ✅ تم نسخ ملف .exe
) else (
    echo ⚠️ ملف .exe غير موجود
)

echo [3/5] نسخ الأيقونات...
if exist "assets" (
    xcopy "assets" "backups\%backup_folder%\assets\" /E /I /Q >nul
    echo ✅ تم نسخ مجلد assets
) else (
    echo ⚠️ مجلد assets غير موجود
)

echo [4/5] نسخ ملفات التشغيل...
copy "*.bat" "backups\%backup_folder%\" >nul 2>&1
copy "*.txt" "backups\%backup_folder%\" >nul 2>&1
echo ✅ تم نسخ ملفات التشغيل والدليل

echo [5/5] إنشاء ملف معلومات النسخة...
echo نسخة احتياطية - نظام إدارة المخزون > "backups\%backup_folder%\معلومات-النسخة.txt"
echo ============================================== >> "backups\%backup_folder%\معلومات-النسخة.txt"
echo. >> "backups\%backup_folder%\معلومات-النسخة.txt"
echo تاريخ النسخ: %date% %time% >> "backups\%backup_folder%\معلومات-النسخة.txt"
echo الشركة: هيمن كروب >> "backups\%backup_folder%\معلومات-النسخة.txt"
echo النظام: إدارة المخزون الاحترافي >> "backups\%backup_folder%\معلومات-النسخة.txt"
echo النسخة: 3.0 >> "backups\%backup_folder%\معلومات-النسخة.txt"
echo. >> "backups\%backup_folder%\معلومات-النسخة.txt"
echo الملفات المنسوخة: >> "backups\%backup_folder%\معلومات-النسخة.txt"
dir "backups\%backup_folder%" /B >> "backups\%backup_folder%\معلومات-النسخة.txt"

echo ✅ تم إنشاء ملف المعلومات

echo.
echo ========================================
echo ✅ تم إنشاء النسخة الاحتياطية بنجاح!
echo ========================================
echo.
echo 📁 المكان: backups\%backup_folder%
echo 📊 الملفات المنسوخة:

dir "backups\%backup_folder%" /B

echo.
echo 💡 لاستعادة النسخة:
echo    انسخ الملفات من مجلد النسخة الاحتياطية
echo    إلى المجلد الرئيسي
echo.

echo 📁 فتح مجلد النسخ الاحتياطية...
start explorer "backups"

echo.
pause
