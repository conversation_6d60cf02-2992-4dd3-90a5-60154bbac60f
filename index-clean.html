<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المخزون - هيمن كروب</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-btn:hover {
            background: #e9ecef;
        }

        .tab-btn.active {
            background: white;
            border-bottom-color: #007bff;
            color: #007bff;
            font-weight: bold;
        }

        .tab-content {
            display: none;
            padding: 30px;
            min-height: 600px;
        }

        .tab-content.active {
            display: block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #dee2e6;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
        }

        .stat-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #007bff;
        }

        .stat-number {
            font-size: 1.8rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .panel {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }

        .panel h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #495057;
        }

        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .btn-success {
            background: #28a745;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }

        .table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .alert {
            padding: 15px;
            margin: 15px 0;
            border-radius: 6px;
            font-weight: bold;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .badge-success {
            background: #28a745;
            color: white;
        }

        .badge-warning {
            background: #ffc107;
            color: #212529;
        }

        .badge-danger {
            background: #dc3545;
            color: white;
        }

        .action-btn {
            padding: 6px 10px;
            margin: 2px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .action-btn.edit {
            background: #ffc107;
            color: #212529;
        }

        .action-btn.delete {
            background: #dc3545;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-warehouse"></i> نظام إدارة المخزون - هيمن كروب</h1>
            <p>نظام احترافي لإدارة المخازن والمعاملات</p>
        </div>

        <div class="tabs">
            <button class="tab-btn active" onclick="showTab('dashboard')">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </button>
            <button class="tab-btn" onclick="showTab('transactions')">
                <i class="fas fa-exchange-alt"></i> المعاملات
            </button>
            <button class="tab-btn" onclick="showTab('inventory')">
                <i class="fas fa-boxes"></i> المخزون
            </button>
            <button class="tab-btn" onclick="showTab('reports')">
                <i class="fas fa-chart-bar"></i> التقارير
            </button>
            <button class="tab-btn" onclick="showTab('settings')">
                <i class="fas fa-cog"></i> الإعدادات
            </button>
        </div>

        <div id="alertContainer"></div>

        <!-- لوحة التحكم -->
        <div id="dashboard" class="tab-content active">
            <div class="panel">
                <h2><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h2>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-boxes"></i></div>
                        <div class="stat-number" id="totalItems">0</div>
                        <div class="stat-label">إجمالي المواد</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-coins"></i></div>
                        <div class="stat-number" id="totalValue">0</div>
                        <div class="stat-label">القيمة الإجمالية (د.ع)</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-dollar-sign"></i></div>
                        <div class="stat-number" id="totalValueUSD">0</div>
                        <div class="stat-label">المجموع دولار</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-exclamation-triangle"></i></div>
                        <div class="stat-number" id="lowStockItems">0</div>
                        <div class="stat-label">مواد منخفضة</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-calendar-day"></i></div>
                        <div class="stat-number" id="todayTransactions">0</div>
                        <div class="stat-label">معاملات اليوم</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-exchange-alt"></i></div>
                        <div class="stat-number" id="exchangeRateDisplay">1500</div>
                        <div class="stat-label">سعر الدولار</div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <button class="btn btn-success" onclick="addSampleData()">
                        <i class="fas fa-plus"></i> إضافة بيانات تجريبية
                    </button>
                    <button class="btn" onclick="refreshData()">
                        <i class="fas fa-sync"></i> تحديث البيانات
                    </button>
                    <button class="btn btn-danger" onclick="clearAllData()">
                        <i class="fas fa-trash"></i> مسح جميع البيانات
                    </button>
                </div>
            </div>

            <!-- جدول المخزون السريع -->
            <div class="panel">
                <h2>
                    <i class="fas fa-list"></i>
                    المخزون الحالي
                    <button class="btn btn-primary" onclick="refreshData()" style="margin-right: auto; font-size: 0.9rem;">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                </h2>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>المادة</th>
                                <th>الكمية</th>
                                <th>الوحدة</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody id="inventoryTableBody">
                            <tr>
                                <td colspan="4" style="text-align: center; color: #7f8c8d;">
                                    <i class="fas fa-box-open"></i>
                                    لا توجد مواد في المخزون
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- المعاملات -->
        <div id="transactions" class="tab-content">
            <div class="panel">
                <h2><i class="fas fa-plus-circle"></i> إضافة معاملة جديدة</h2>

                <form id="transactionForm">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                        <div class="form-group">
                            <label for="itemName">اسم المادة *</label>
                            <input type="text" id="itemName" class="form-control" required placeholder="أدخل اسم المادة">
                        </div>

                        <div class="form-group">
                            <label for="transactionType">نوع المعاملة *</label>
                            <select id="transactionType" class="form-control" required>
                                <option value="">اختر نوع المعاملة</option>
                                <option value="وارد">وارد</option>
                                <option value="صادر">صادر</option>
                                <option value="تسوية">تسوية مخزون</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="quantity">الكمية *</label>
                            <input type="number" id="quantity" class="form-control" step="0.01" min="0" required>
                        </div>

                        <div class="form-group">
                            <label for="unit">الوحدة *</label>
                            <input type="text" id="unit" class="form-control" required placeholder="مثل: كيس، طن، قطعة">
                        </div>

                        <div class="form-group">
                            <label for="unitPrice">سعر الوحدة</label>
                            <input type="number" id="unitPrice" class="form-control" step="0.01" min="0">
                        </div>

                        <div class="form-group">
                            <label for="currency">العملة</label>
                            <select id="currency" class="form-control">
                                <option value="IQD">دينار عراقي</option>
                                <option value="USD">دولار أمريكي</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="warehouse">المخزن *</label>
                            <select id="warehouse" class="form-control" required>
                                <option value="">اختر المخزن</option>
                                <option value="المخزن الرئيسي">المخزن الرئيسي</option>
                                <option value="مخزن فرعي 1">مخزن فرعي 1</option>
                                <option value="مخزن فرعي 2">مخزن فرعي 2</option>
                                <option value="مخزن فرعي 3">مخزن فرعي 3</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="supplier">المورد/المستلم</label>
                            <input type="text" id="supplier" class="form-control" placeholder="اسم المورد أو المستلم">
                        </div>

                        <div class="form-group">
                            <label for="invoiceNumber">رقم الفاتورة</label>
                            <input type="text" id="invoiceNumber" class="form-control" placeholder="رقم الفاتورة">
                        </div>

                        <div class="form-group">
                            <label for="notes">ملاحظات</label>
                            <textarea id="notes" class="form-control" rows="2" placeholder="ملاحظات إضافية"></textarea>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> حفظ المعاملة
                        </button>
                        <button type="reset" class="btn">
                            <i class="fas fa-undo"></i> إعادة تعيين
                        </button>
                    </div>
                </form>
            </div>

            <!-- سجل المعاملات -->
            <div class="panel">
                <h2><i class="fas fa-list"></i> سجل المعاملات</h2>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>المادة</th>
                                <th>النوع</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>المجموع</th>
                                <th>المخزن</th>
                                <th>المورد/المستلم</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="transactionsTableBody">
                            <tr>
                                <td colspan="9" style="text-align: center; color: #7f8c8d;">
                                    <i class="fas fa-exchange-alt"></i>
                                    لا توجد معاملات
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- المخزون -->
        <div id="inventory" class="tab-content">
            <div class="panel">
                <h2><i class="fas fa-boxes"></i> المخزون الحالي</h2>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>المادة</th>
                                <th>الكمية</th>
                                <th>الوحدة</th>
                                <th>السعر</th>
                                <th>العملة</th>
                                <th>القيمة الإجمالية</th>
                                <th>المخزن</th>
                                <th>الحالة</th>
                                <th>آخر تحديث</th>
                            </tr>
                        </thead>
                        <tbody id="inventoryFullTableBody">
                            <tr>
                                <td colspan="9" style="text-align: center; color: #7f8c8d;">
                                    <i class="fas fa-box-open"></i>
                                    لا توجد مواد في المخزون
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- التقارير -->
        <div id="reports" class="tab-content">
            <div class="panel">
                <h2><i class="fas fa-chart-bar"></i> التقارير</h2>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <div>
                        <h4>تقرير يومي</h4>
                        <input type="date" id="dailyReportDate" class="form-control" style="margin-bottom: 10px;">
                        <button class="btn" onclick="generateDailyReport()">
                            <i class="fas fa-calendar-day"></i> عرض التقرير اليومي
                        </button>
                    </div>

                    <div>
                        <h4>تقرير أسبوعي</h4>
                        <input type="week" id="weeklyReportWeek" class="form-control" style="margin-bottom: 10px;">
                        <button class="btn" onclick="generateWeeklyReport()">
                            <i class="fas fa-calendar-week"></i> عرض التقرير الأسبوعي
                        </button>
                    </div>

                    <div>
                        <h4>تقرير شهري</h4>
                        <input type="month" id="monthlyReportMonth" class="form-control" style="margin-bottom: 10px;">
                        <button class="btn" onclick="generateMonthlyReport()">
                            <i class="fas fa-calendar-alt"></i> عرض التقرير الشهري
                        </button>
                    </div>

                    <div>
                        <h4>تقرير المخزن</h4>
                        <select id="warehouseReportSelect" class="form-control" style="margin-bottom: 10px;">
                            <option value="">اختر المخزن</option>
                            <option value="المخزن الرئيسي">المخزن الرئيسي</option>
                            <option value="مخزن فرعي 1">مخزن فرعي 1</option>
                            <option value="مخزن فرعي 2">مخزن فرعي 2</option>
                            <option value="مخزن فرعي 3">مخزن فرعي 3</option>
                        </select>
                        <button class="btn" onclick="generateWarehouseReport()">
                            <i class="fas fa-warehouse"></i> عرض تقرير المخزن
                        </button>
                    </div>
                </div>

                <div id="reportResults" style="margin-top: 30px;"></div>
            </div>
        </div>

        <!-- الإعدادات -->
        <div id="settings" class="tab-content">
            <div class="panel">
                <h2><i class="fas fa-cog"></i> إعدادات النظام</h2>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <div>
                        <h4>معلومات الشركة</h4>
                        <div class="form-group">
                            <label for="companyName">اسم الشركة</label>
                            <input type="text" id="companyName" class="form-control" value="هيمن كروب">
                        </div>
                        <div class="form-group">
                            <label for="companyAddress">عنوان الشركة</label>
                            <input type="text" id="companyAddress" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="companyPhone">هاتف الشركة</label>
                            <input type="text" id="companyPhone" class="form-control">
                        </div>
                    </div>

                    <div>
                        <h4>إعدادات العملة</h4>
                        <div class="form-group">
                            <label for="exchangeRate">سعر صرف الدولار (د.ع)</label>
                            <input type="number" id="exchangeRate" class="form-control" value="1500" step="0.01">
                        </div>
                        <button class="btn" onclick="updateExchangeRate()">
                            <i class="fas fa-sync"></i> تحديث سعر الصرف
                        </button>
                    </div>

                    <div>
                        <h4>إدارة البيانات</h4>
                        <button class="btn btn-success" onclick="addSampleData()">
                            <i class="fas fa-plus"></i> إضافة بيانات تجريبية
                        </button>
                        <button class="btn" onclick="exportData()">
                            <i class="fas fa-download"></i> تصدير البيانات
                        </button>
                        <button class="btn btn-danger" onclick="clearAllData()">
                            <i class="fas fa-trash"></i> مسح جميع البيانات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        // متغيرات النظام
        let inventorySystem = {
            items: [],
            transactions: [],
            exchangeRate: 1500,
            companyInfo: {
                name: 'هيمن كروب',
                address: '',
                phone: ''
            }
        };

        // تحميل البيانات عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            updateStats();
            renderInventoryTable();
            renderTransactionsTable();
            setCurrentDate();
        });

        // دالة تحميل البيانات من localStorage
        function loadData() {
            const savedData = localStorage.getItem('inventorySystemData');
            if (savedData) {
                inventorySystem = { ...inventorySystem, ...JSON.parse(savedData) };
            }
        }

        // دالة حفظ البيانات في localStorage
        function saveData() {
            localStorage.setItem('inventorySystemData', JSON.stringify(inventorySystem));
        }

        // دالة عرض التبويبات
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            const tabs = document.querySelectorAll('.tab-content');
            tabs.forEach(tab => tab.classList.remove('active'));

            // إزالة active من جميع الأزرار
            const buttons = document.querySelectorAll('.tab-btn');
            buttons.forEach(btn => btn.classList.remove('active'));

            // إظهار التبويب المحدد
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // دالة تحديث الإحصائيات
        function updateStats() {
            const totalItems = inventorySystem.items.length;
            const totalValue = inventorySystem.items.reduce((sum, item) => {
                const value = item.currency === 'USD' ? item.quantity * item.unitPrice * inventorySystem.exchangeRate : item.quantity * item.unitPrice;
                return sum + value;
            }, 0);
            const totalValueUSD = inventorySystem.items.reduce((sum, item) => {
                const value = item.currency === 'USD' ? item.quantity * item.unitPrice : (item.quantity * item.unitPrice) / inventorySystem.exchangeRate;
                return sum + value;
            }, 0);
            const lowStockItems = inventorySystem.items.filter(item => item.quantity <= (item.minStock || 10)).length;
            const todayTransactions = inventorySystem.transactions.filter(t => {
                const today = new Date().toDateString();
                const transactionDate = new Date(t.date).toDateString();
                return today === transactionDate;
            }).length;

            document.getElementById('totalItems').textContent = totalItems;
            document.getElementById('totalValue').textContent = Math.round(totalValue).toLocaleString();
            document.getElementById('totalValueUSD').textContent = Math.round(totalValueUSD).toLocaleString();
            document.getElementById('lowStockItems').textContent = lowStockItems;
            document.getElementById('todayTransactions').textContent = todayTransactions;
            document.getElementById('exchangeRateDisplay').textContent = inventorySystem.exchangeRate;
        }

        // دالة إضافة معاملة
        document.getElementById('transactionForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = {
                itemName: document.getElementById('itemName').value.trim(),
                transactionType: document.getElementById('transactionType').value,
                quantity: parseFloat(document.getElementById('quantity').value),
                unit: document.getElementById('unit').value.trim(),
                unitPrice: parseFloat(document.getElementById('unitPrice').value) || 0,
                currency: document.getElementById('currency').value,
                warehouse: document.getElementById('warehouse').value,
                supplier: document.getElementById('supplier').value.trim(),
                invoiceNumber: document.getElementById('invoiceNumber').value.trim(),
                notes: document.getElementById('notes').value.trim()
            };

            // التحقق من البيانات
            if (!formData.itemName || !formData.transactionType || !formData.quantity || !formData.unit || !formData.warehouse) {
                showAlert('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }

            // إنشاء المعاملة
            const transaction = {
                id: Date.now(),
                ...formData,
                totalPrice: formData.quantity * formData.unitPrice,
                date: new Date().toISOString(),
                exchangeRateAtTime: inventorySystem.exchangeRate
            };

            // إضافة المعاملة
            inventorySystem.transactions.push(transaction);

            // تحديث المخزون
            updateInventoryFromTransaction(transaction);

            // حفظ وتحديث
            saveData();
            updateStats();
            renderInventoryTable();
            renderTransactionsTable();

            // إعادة تعيين النموذج
            this.reset();

            showAlert('تم إضافة المعاملة بنجاح!', 'success');
        });

        // دالة تحديث المخزون من المعاملة
        function updateInventoryFromTransaction(transaction) {
            let existingItem = inventorySystem.items.find(item =>
                item.name === transaction.itemName && item.warehouse === transaction.warehouse
            );

            if (existingItem) {
                // تحديث المادة الموجودة
                if (transaction.transactionType === 'وارد') {
                    existingItem.quantity += transaction.quantity;
                    if (transaction.unitPrice > 0) {
                        existingItem.unitPrice = transaction.unitPrice;
                        existingItem.currency = transaction.currency;
                    }
                } else if (transaction.transactionType === 'صادر') {
                    existingItem.quantity -= transaction.quantity;
                } else if (transaction.transactionType === 'تسوية') {
                    existingItem.quantity = transaction.quantity;
                    if (transaction.unitPrice > 0) {
                        existingItem.unitPrice = transaction.unitPrice;
                        existingItem.currency = transaction.currency;
                    }
                }
                existingItem.lastUpdated = new Date().toISOString();
            } else {
                // إضافة مادة جديدة (فقط للوارد والتسوية)
                if (transaction.transactionType === 'وارد' || transaction.transactionType === 'تسوية') {
                    const newItem = {
                        id: Date.now(),
                        name: transaction.itemName,
                        quantity: transaction.quantity,
                        unit: transaction.unit,
                        unitPrice: transaction.unitPrice || 0,
                        currency: transaction.currency || 'IQD',
                        minStock: 10,
                        category: 'عام',
                        warehouse: transaction.warehouse,
                        created: new Date().toISOString(),
                        lastUpdated: new Date().toISOString()
                    };
                    inventorySystem.items.push(newItem);
                } else {
                    throw new Error('لا يمكن إجراء ' + transaction.transactionType + ' لمادة غير موجودة');
                }
            }
        }

        // دالة عرض الرسائل
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;

            alertContainer.appendChild(alert);

            setTimeout(() => {
                alert.remove();
            }, 3000);
        }

        // دالة عرض جدول المخزون
        function renderInventoryTable() {
            const tbody = document.getElementById('inventoryTableBody');
            const fullTbody = document.getElementById('inventoryFullTableBody');

            if (inventorySystem.items.length === 0) {
                tbody.innerHTML = '<tr><td colspan="4" style="text-align: center; color: #7f8c8d;"><i class="fas fa-box-open"></i> لا توجد مواد في المخزون</td></tr>';
                fullTbody.innerHTML = '<tr><td colspan="9" style="text-align: center; color: #7f8c8d;"><i class="fas fa-box-open"></i> لا توجد مواد في المخزون</td></tr>';
                return;
            }

            // جدول لوحة التحكم
            tbody.innerHTML = inventorySystem.items.slice(0, 5).map(item => {
                const status = item.quantity <= (item.minStock || 10) ? 'منخفض' : 'متوفر';
                const statusClass = status === 'منخفض' ? 'badge-danger' : 'badge-success';

                return `
                    <tr>
                        <td>${item.name}</td>
                        <td>${item.quantity}</td>
                        <td>${item.unit}</td>
                        <td><span class="badge ${statusClass}">${status}</span></td>
                    </tr>
                `;
            }).join('');

            // جدول المخزون الكامل
            fullTbody.innerHTML = inventorySystem.items.map(item => {
                const status = item.quantity <= (item.minStock || 10) ? 'منخفض' : 'متوفر';
                const statusClass = status === 'منخفض' ? 'badge-danger' : 'badge-success';
                const totalValue = item.quantity * item.unitPrice;

                return `
                    <tr>
                        <td>${item.name}</td>
                        <td>${item.quantity}</td>
                        <td>${item.unit}</td>
                        <td>${item.unitPrice}</td>
                        <td>${item.currency}</td>
                        <td>${totalValue.toLocaleString()}</td>
                        <td>${item.warehouse}</td>
                        <td><span class="badge ${statusClass}">${status}</span></td>
                        <td>${new Date(item.lastUpdated).toLocaleDateString('ar-EG')}</td>
                    </tr>
                `;
            }).join('');
        }

        // دالة عرض جدول المعاملات
        function renderTransactionsTable() {
            const tbody = document.getElementById('transactionsTableBody');

            if (inventorySystem.transactions.length === 0) {
                tbody.innerHTML = '<tr><td colspan="9" style="text-align: center; color: #7f8c8d;"><i class="fas fa-exchange-alt"></i> لا توجد معاملات</td></tr>';
                return;
            }

            tbody.innerHTML = inventorySystem.transactions.slice().reverse().map(transaction => {
                const typeClass = transaction.transactionType === 'وارد' ? 'badge-success' :
                                 transaction.transactionType === 'صادر' ? 'badge-danger' : 'badge-warning';

                return `
                    <tr>
                        <td>${new Date(transaction.date).toLocaleDateString('ar-EG')}</td>
                        <td>${transaction.itemName}</td>
                        <td><span class="badge ${typeClass}">${transaction.transactionType}</span></td>
                        <td>${transaction.quantity} ${transaction.unit}</td>
                        <td>${transaction.unitPrice} ${transaction.currency}</td>
                        <td>${transaction.totalPrice.toLocaleString()}</td>
                        <td>${transaction.warehouse}</td>
                        <td>${transaction.supplier || '-'}</td>
                        <td>
                            <button class="action-btn delete" onclick="deleteTransaction(${transaction.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // دالة حذف معاملة
        function deleteTransaction(id) {
            if (confirm('هل أنت متأكد من حذف هذه المعاملة؟')) {
                inventorySystem.transactions = inventorySystem.transactions.filter(t => t.id !== id);
                saveData();
                updateStats();
                renderTransactionsTable();
                showAlert('تم حذف المعاملة بنجاح!', 'success');
            }
        }

        // دالة تعيين التاريخ الحالي
        function setCurrentDate() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('dailyReportDate').value = today;
        }

        // دالة إضافة بيانات تجريبية
        function addSampleData() {
            const sampleItems = [
                { name: 'أسمنت', quantity: 100, unit: 'كيس', unitPrice: 8500, currency: 'IQD', warehouse: 'المخزن الرئيسي' },
                { name: 'حديد', quantity: 50, unit: 'طن', unitPrice: 800, currency: 'USD', warehouse: 'المخزن الرئيسي' },
                { name: 'رمل', quantity: 200, unit: 'متر مكعب', unitPrice: 15000, currency: 'IQD', warehouse: 'مخزن فرعي 1' },
                { name: 'حصى', quantity: 150, unit: 'متر مكعب', unitPrice: 20000, currency: 'IQD', warehouse: 'مخزن فرعي 2' }
            ];

            sampleItems.forEach(item => {
                const newItem = {
                    id: Date.now() + Math.random(),
                    ...item,
                    minStock: 10,
                    category: 'مواد بناء',
                    created: new Date().toISOString(),
                    lastUpdated: new Date().toISOString()
                };
                inventorySystem.items.push(newItem);

                // إضافة معاملة وارد
                const transaction = {
                    id: Date.now() + Math.random(),
                    itemName: item.name,
                    transactionType: 'وارد',
                    quantity: item.quantity,
                    unit: item.unit,
                    unitPrice: item.unitPrice,
                    currency: item.currency,
                    warehouse: item.warehouse,
                    supplier: 'مورد تجريبي',
                    invoiceNumber: 'INV-' + Math.floor(Math.random() * 1000),
                    notes: 'بيانات تجريبية',
                    totalPrice: item.quantity * item.unitPrice,
                    date: new Date().toISOString(),
                    exchangeRateAtTime: inventorySystem.exchangeRate
                };
                inventorySystem.transactions.push(transaction);
            });

            saveData();
            updateStats();
            renderInventoryTable();
            renderTransactionsTable();
            showAlert('تم إضافة البيانات التجريبية بنجاح!', 'success');
        }

        // دالة مسح جميع البيانات
        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                inventorySystem.items = [];
                inventorySystem.transactions = [];
                saveData();
                updateStats();
                renderInventoryTable();
                renderTransactionsTable();
                showAlert('تم مسح جميع البيانات!', 'success');
            }
        }

        // دالة تحديث البيانات
        function refreshData() {
            updateStats();
            renderInventoryTable();
            renderTransactionsTable();
            showAlert('تم تحديث البيانات!', 'success');
        }

        // دالة تحديث سعر الصرف
        function updateExchangeRate() {
            const newRate = parseFloat(document.getElementById('exchangeRate').value);
            if (newRate > 0) {
                inventorySystem.exchangeRate = newRate;
                saveData();
                updateStats();
                showAlert('تم تحديث سعر الصرف!', 'success');
            }
        }

        // دوال التقارير البسيطة
        function generateDailyReport() {
            const date = document.getElementById('dailyReportDate').value;
            if (!date) {
                showAlert('يرجى اختيار التاريخ', 'danger');
                return;
            }

            const transactions = inventorySystem.transactions.filter(t => {
                const transactionDate = new Date(t.date).toISOString().split('T')[0];
                return transactionDate === date;
            });

            displayReport('التقرير اليومي - ' + date, transactions);
        }

        function generateWeeklyReport() {
            showAlert('التقرير الأسبوعي قيد التطوير', 'warning');
        }

        function generateMonthlyReport() {
            showAlert('التقرير الشهري قيد التطوير', 'warning');
        }

        function generateWarehouseReport() {
            const warehouse = document.getElementById('warehouseReportSelect').value;
            if (!warehouse) {
                showAlert('يرجى اختيار المخزن', 'danger');
                return;
            }

            const transactions = inventorySystem.transactions.filter(t => t.warehouse === warehouse);
            const items = inventorySystem.items.filter(i => i.warehouse === warehouse);

            displayWarehouseReport('تقرير المخزن - ' + warehouse, transactions, items);
        }

        function displayReport(title, transactions) {
            const resultsDiv = document.getElementById('reportResults');

            if (transactions.length === 0) {
                resultsDiv.innerHTML = `<h3>${title}</h3><p>لا توجد معاملات في هذه الفترة</p>`;
                return;
            }

            const totalIncoming = transactions.filter(t => t.transactionType === 'وارد').reduce((sum, t) => sum + t.totalPrice, 0);
            const totalOutgoing = transactions.filter(t => t.transactionType === 'صادر').reduce((sum, t) => sum + t.totalPrice, 0);

            resultsDiv.innerHTML = `
                <h3>${title}</h3>
                <div class="stats-grid" style="grid-template-columns: repeat(3, 1fr); margin: 20px 0;">
                    <div class="stat-card">
                        <div class="stat-number">${transactions.length}</div>
                        <div class="stat-label">إجمالي المعاملات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${totalIncoming.toLocaleString()}</div>
                        <div class="stat-label">إجمالي الوارد</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${totalOutgoing.toLocaleString()}</div>
                        <div class="stat-label">إجمالي الصادر</div>
                    </div>
                </div>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>المادة</th>
                                <th>النوع</th>
                                <th>الكمية</th>
                                <th>المجموع</th>
                                <th>المخزن</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${transactions.map(t => `
                                <tr>
                                    <td>${new Date(t.date).toLocaleDateString('ar-EG')}</td>
                                    <td>${t.itemName}</td>
                                    <td>${t.transactionType}</td>
                                    <td>${t.quantity} ${t.unit}</td>
                                    <td>${t.totalPrice.toLocaleString()}</td>
                                    <td>${t.warehouse}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }

        function displayWarehouseReport(title, transactions, items) {
            const resultsDiv = document.getElementById('reportResults');

            const totalValue = items.reduce((sum, item) => {
                const value = item.currency === 'USD' ? item.quantity * item.unitPrice * inventorySystem.exchangeRate : item.quantity * item.unitPrice;
                return sum + value;
            }, 0);

            resultsDiv.innerHTML = `
                <h3>${title}</h3>
                <div class="stats-grid" style="grid-template-columns: repeat(3, 1fr); margin: 20px 0;">
                    <div class="stat-card">
                        <div class="stat-number">${items.length}</div>
                        <div class="stat-label">عدد المواد</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${transactions.length}</div>
                        <div class="stat-label">عدد المعاملات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${totalValue.toLocaleString()}</div>
                        <div class="stat-label">القيمة الإجمالية (د.ع)</div>
                    </div>
                </div>
                <h4>المواد الحالية:</h4>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>المادة</th>
                                <th>الكمية</th>
                                <th>الوحدة</th>
                                <th>السعر</th>
                                <th>القيمة الإجمالية</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${items.map(item => `
                                <tr>
                                    <td>${item.name}</td>
                                    <td>${item.quantity}</td>
                                    <td>${item.unit}</td>
                                    <td>${item.unitPrice} ${item.currency}</td>
                                    <td>${(item.quantity * item.unitPrice).toLocaleString()}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }

        // دالة تصدير البيانات
        function exportData() {
            const dataStr = JSON.stringify(inventorySystem, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'inventory-data-' + new Date().toISOString().split('T')[0] + '.json';
            link.click();
            showAlert('تم تصدير البيانات!', 'success');
        }
    </script>
</body>
</html>
