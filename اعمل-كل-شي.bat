@echo off
chcp 65001 >nul
title نظام إدارة المخزون - هيمن كروب (تلقائي)
color 0A

echo.
echo ========================================
echo    نظام إدارة المخزون - هيمن كروب
echo        إعداد تلقائي كامل
echo ========================================
echo.
echo 🤖 سأعمل كل شيء لك تلقائياً...
echo ⏳ فقط انتظر واتبع التعليمات
echo.
pause

REM الخطوة 1: فحص Python
echo.
echo [1/5] فحص Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ❌ Python غير مثبت!
    echo.
    echo 🔽 سأفتح لك موقع التحميل...
    echo 📋 اتبع هذه الخطوات:
    echo    1. اضغط Download Python
    echo    2. شغل الملف المحمل
    echo    3. ✅ اختر "Add Python to PATH"
    echo    4. اضغط Install Now
    echo    5. أعد تشغيل هذا الملف
    echo.
    start https://python.org/downloads/
    pause
    exit /b 1
)
echo ✅ Python موجود

REM الخطوة 2: تثبيت PyInstaller
echo [2/5] تثبيت PyInstaller...
pip install pyinstaller --quiet --disable-pip-version-check
if %errorlevel% neq 0 (
    echo ❌ فشل التثبيت - جرب تشغيل كمدير
    pause
    exit /b 1
)
echo ✅ PyInstaller جاهز

REM الخطوة 3: إنشاء التطبيق
echo [3/5] إنشاء ملف التطبيق...

REM إنشاء ملف Python بسيط
echo import tkinter as tk > simple_app.py
echo from tkinter import messagebox >> simple_app.py
echo import webbrowser >> simple_app.py
echo import os >> simple_app.py
echo import http.server >> simple_app.py
echo import socketserver >> simple_app.py
echo import threading >> simple_app.py
echo import socket >> simple_app.py
echo. >> simple_app.py
echo def find_port(): >> simple_app.py
echo     with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s: >> simple_app.py
echo         s.bind(('', 0)) >> simple_app.py
echo         return s.getsockname()[1] >> simple_app.py
echo. >> simple_app.py
echo def start_server(port): >> simple_app.py
echo     os.chdir(os.path.dirname(os.path.abspath(__file__))) >> simple_app.py
echo     handler = http.server.SimpleHTTPRequestHandler >> simple_app.py
echo     httpd = socketserver.TCPServer(("", port), handler) >> simple_app.py
echo     threading.Thread(target=httpd.serve_forever, daemon=True).start() >> simple_app.py
echo     return httpd >> simple_app.py
echo. >> simple_app.py
echo def open_system(): >> simple_app.py
echo     webbrowser.open(f"http://localhost:{port}/index-professional.html") >> simple_app.py
echo. >> simple_app.py
echo def main(): >> simple_app.py
echo     global port >> simple_app.py
echo     port = find_port() >> simple_app.py
echo     start_server(port) >> simple_app.py
echo. >> simple_app.py
echo     root = tk.Tk() >> simple_app.py
echo     root.title("نظام إدارة المخزون - هيمن كروب") >> simple_app.py
echo     root.geometry("500x300") >> simple_app.py
echo     root.configure(bg='#f0f0f0') >> simple_app.py
echo. >> simple_app.py
echo     title = tk.Label(root, text="نظام إدارة المخزون", font=("Arial", 20, "bold"), bg='#f0f0f0') >> simple_app.py
echo     title.pack(pady=20) >> simple_app.py
echo. >> simple_app.py
echo     subtitle = tk.Label(root, text="هيمن كروب", font=("Arial", 14), bg='#f0f0f0') >> simple_app.py
echo     subtitle.pack(pady=5) >> simple_app.py
echo. >> simple_app.py
echo     info = tk.Label(root, text=f"الخادم يعمل على: http://localhost:{port}", bg='#f0f0f0') >> simple_app.py
echo     info.pack(pady=10) >> simple_app.py
echo. >> simple_app.py
echo     btn = tk.Button(root, text="🚀 فتح نظام المخزون", command=open_system, >> simple_app.py
echo                     font=("Arial", 12, "bold"), bg='#4CAF50', fg='white', >> simple_app.py
echo                     padx=20, pady=10) >> simple_app.py
echo     btn.pack(pady=20) >> simple_app.py
echo. >> simple_app.py
echo     exit_btn = tk.Button(root, text="خروج", command=root.quit, >> simple_app.py
echo                          font=("Arial", 10), bg='#f44336', fg='white', >> simple_app.py
echo                          padx=15, pady=5) >> simple_app.py
echo     exit_btn.pack(pady=10) >> simple_app.py
echo. >> simple_app.py
echo     root.mainloop() >> simple_app.py
echo. >> simple_app.py
echo if __name__ == "__main__": >> simple_app.py
echo     main() >> simple_app.py

echo ✅ ملف التطبيق جاهز

REM الخطوة 4: بناء exe
echo [4/5] بناء ملف .exe...
echo ⏳ هذا يستغرق 2-5 دقائق...

pyinstaller --onefile --windowed --name "نظام-المخزون-هيمن-كروب" --add-data "index-professional.html;." simple_app.py >nul 2>&1

if %errorlevel% neq 0 (
    echo ❌ فشل البناء!
    echo جرب تشغيل كمدير أو تحقق من المساحة
    pause
    exit /b 1
)

REM الخطوة 5: نسخ الملفات
echo [5/5] تجهيز الملفات...

if exist "assets" (
    xcopy "assets" "dist\assets\" /E /I /Q >nul 2>&1
)

copy "index-professional.html" "dist\" >nul 2>&1

REM تنظيف الملفات المؤقتة
del simple_app.py >nul 2>&1
rmdir /s /q build >nul 2>&1
rmdir /s /q __pycache__ >nul 2>&1
del *.spec >nul 2>&1

echo.
echo ========================================
echo 🎉 تم! ملف .exe جاهز!
echo ========================================
echo.

if exist "dist\نظام-المخزون-هيمن-كروب.exe" (
    echo ✅ الملف: dist\نظام-المخزون-هيمن-كروب.exe
    echo 📊 الحجم: حوالي 15-20 ميجا
    echo.
    echo 🚀 للاستخدام:
    echo    1. انقر نقراً مزدوجاً على الملف
    echo    2. اضغط "فتح نظام المخزون"
    echo    3. استمتع بالنظام!
    echo.
    echo 📁 فتح مجلد الملف...
    start explorer "dist"
    echo.
    echo 🎯 هل تريد تشغيل النظام الآن؟
    set /p run="اكتب y للتشغيل أو أي شيء للخروج: "
    if /i "%run%"=="y" (
        start "" "dist\نظام-المخزون-هيمن-كروب.exe"
        echo ✅ تم تشغيل النظام!
    )
) else (
    echo ❌ لم يتم إنشاء الملف!
    echo تحقق من الأخطاء أعلاه
)

echo.
echo 🎉 انتهى! شكراً لك
pause
