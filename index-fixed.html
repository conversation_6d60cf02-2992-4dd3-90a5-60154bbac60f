<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المخزون - هيمن كروب</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-btn:hover {
            background: #e9ecef;
        }

        .tab-btn.active {
            background: white;
            border-bottom-color: #007bff;
            color: #007bff;
            font-weight: bold;
        }

        .tab-content {
            display: none;
            padding: 30px;
            min-height: 600px;
        }

        .tab-content.active {
            display: block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #dee2e6;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
        }

        .stat-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #007bff;
        }

        .stat-number {
            font-size: 1.8rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .btn-success {
            background: #28a745;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .alert {
            padding: 15px;
            margin: 15px 0;
            border-radius: 6px;
            font-weight: bold;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #495057;
        }

        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }

        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }

        .table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }

        .table tbody tr:hover {
            background: #f8f9fa;
        }

        .action-btn {
            padding: 6px 10px;
            margin: 2px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .action-btn.edit {
            background: #ffc107;
            color: #212529;
        }

        .action-btn.delete {
            background: #dc3545;
            color: white;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .badge-success {
            background: #28a745;
            color: white;
        }

        .badge-warning {
            background: #ffc107;
            color: #212529;
        }

        .badge-danger {
            background: #dc3545;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-warehouse"></i> نظام إدارة المخزون - هيمن كروب</h1>
            <p>نظام احترافي لإدارة المخازن والمعاملات</p>
        </div>

        <div class="tabs">
            <button class="tab-btn active" onclick="showTab('dashboard')">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </button>
            <button class="tab-btn" onclick="showTab('inventory')">
                <i class="fas fa-boxes"></i> المخزون
            </button>
            <button class="tab-btn" onclick="showTab('transactions')">
                <i class="fas fa-exchange-alt"></i> المعاملات
            </button>
            <button class="tab-btn" onclick="showTab('reports')">
                <i class="fas fa-chart-bar"></i> التقارير
            </button>
        </div>

        <div id="alertContainer"></div>

        <!-- لوحة التحكم -->
        <div id="dashboard" class="tab-content active">
            <h2><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h2>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-boxes"></i></div>
                    <div class="stat-number" id="totalItems">0</div>
                    <div class="stat-label">إجمالي المواد</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-coins"></i></div>
                    <div class="stat-number" id="totalValue">0</div>
                    <div class="stat-label">القيمة الإجمالية (د.ع)</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-dollar-sign"></i></div>
                    <div class="stat-number" id="totalValueUSD">0</div>
                    <div class="stat-label">المجموع دولار</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-exclamation-triangle"></i></div>
                    <div class="stat-number" id="lowStockItems">0</div>
                    <div class="stat-label">مواد منخفضة</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-calendar-day"></i></div>
                    <div class="stat-number" id="todayTransactions">0</div>
                    <div class="stat-label">معاملات اليوم</div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-exchange-alt"></i></div>
                    <div class="stat-number" id="exchangeRateDisplay">1500</div>
                    <div class="stat-label">سعر الدولار</div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <button class="btn btn-success" onclick="addSampleDataFixed()">
                    <i class="fas fa-plus"></i> إضافة بيانات تجريبية
                </button>
                <button class="btn" onclick="refreshDataFixed()">
                    <i class="fas fa-sync"></i> تحديث البيانات
                </button>
                <button class="btn btn-danger" onclick="clearAllDataFixed()">
                    <i class="fas fa-trash"></i> مسح جميع البيانات
                </button>
            </div>
        </div>

        <!-- المخزون -->
        <div id="inventory" class="tab-content">
            <h2><i class="fas fa-boxes"></i> إدارة المخزون</h2>

            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>المادة</th>
                            <th>الكمية</th>
                            <th>الوحدة</th>
                            <th>السعر</th>
                            <th>القيمة الإجمالية</th>
                            <th>الحالة</th>
                            <th>المخزن</th>
                        </tr>
                    </thead>
                    <tbody id="inventoryTableBody">
                        <tr>
                            <td colspan="7" style="text-align: center; padding: 40px; color: #6c757d;">
                                <i class="fas fa-box-open" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>
                                لا توجد مواد في المخزون<br>
                                <small>ابدأ بإضافة البيانات التجريبية</small>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- المعاملات -->
        <div id="transactions" class="tab-content">
            <h2><i class="fas fa-exchange-alt"></i> إدارة المعاملات</h2>

            <!-- نموذج إضافة معاملة -->
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
                <h4><i class="fas fa-plus"></i> إضافة معاملة جديدة</h4>
                <form id="transactionForm" onsubmit="addTransactionFixed(event)">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                        <div class="form-group">
                            <label for="itemName">اسم المادة *</label>
                            <input type="text" id="itemName" class="form-control" required>
                        </div>

                        <div class="form-group">
                            <label for="transactionType">نوع المعاملة *</label>
                            <select id="transactionType" class="form-control" required onchange="togglePriceFields()">
                                <option value="">اختر النوع</option>
                                <option value="وارد">وارد</option>
                                <option value="صادر">صادر</option>
                                <option value="تسوية">تسوية مخزون</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="quantity">الكمية *</label>
                            <input type="number" id="quantity" class="form-control" step="0.01" min="0" required>
                        </div>

                        <div class="form-group">
                            <label for="unit">الوحدة *</label>
                            <input type="text" id="unit" class="form-control" required placeholder="كيس، طن، قطعة...">
                        </div>

                        <div class="form-group" id="priceGroup">
                            <label for="unitPrice">سعر الوحدة</label>
                            <input type="number" id="unitPrice" class="form-control" step="0.01" min="0">
                        </div>

                        <div class="form-group" id="currencyGroup">
                            <label for="currency">العملة</label>
                            <select id="currency" class="form-control">
                                <option value="IQD">دينار عراقي</option>
                                <option value="USD">دولار أمريكي</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="warehouse">المخزن *</label>
                            <select id="warehouse" class="form-control" required>
                                <option value="">اختر المخزن</option>
                                <option value="المخزن الرئيسي">المخزن الرئيسي</option>
                                <option value="مخزن فرعي 1">مخزن فرعي 1</option>
                                <option value="مخزن فرعي 2">مخزن فرعي 2</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="supplier">المورد/المستلم</label>
                            <input type="text" id="supplier" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="invoiceNumber">رقم الفاتورة</label>
                            <input type="text" id="invoiceNumber" class="form-control">
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> حفظ المعاملة
                        </button>
                        <button type="reset" class="btn" onclick="resetTransactionForm()">
                            <i class="fas fa-undo"></i> إعادة تعيين
                        </button>
                    </div>
                </form>
            </div>

            <h3><i class="fas fa-list"></i> سجل المعاملات</h3>
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>المادة</th>
                            <th>النوع</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>المجموع</th>
                            <th>المخزن</th>
                            <th>المورد</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="transactionsTableBody">
                        <tr>
                            <td colspan="9" style="text-align: center; padding: 40px; color: #6c757d;">
                                <i class="fas fa-exchange-alt" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>
                                لا توجد معاملات<br>
                                <small>ابدأ بإضافة البيانات التجريبية</small>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- التقارير -->
        <div id="reports" class="tab-content">
            <h2><i class="fas fa-chart-bar"></i> التقارير</h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                    <h4><i class="fas fa-calendar-day"></i> تقرير يومي</h4>
                    <div class="form-group">
                        <label>اختر التاريخ:</label>
                        <input type="date" id="dailyReportDate" class="form-control">
                    </div>
                    <button class="btn" onclick="generateDailyReportFixed()">
                        <i class="fas fa-chart-line"></i> عرض التقرير
                    </button>
                    <button class="btn btn-success" onclick="printDailyReportFixed()">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                </div>

                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                    <h4><i class="fas fa-calendar-week"></i> تقرير أسبوعي</h4>
                    <div class="form-group">
                        <label>اختر الأسبوع:</label>
                        <input type="week" id="weeklyReportDate" class="form-control">
                    </div>
                    <button class="btn" onclick="generateWeeklyReportFixed()">
                        <i class="fas fa-chart-line"></i> عرض التقرير
                    </button>
                    <button class="btn btn-success" onclick="printWeeklyReportFixed()">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                </div>

                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                    <h4><i class="fas fa-calendar-alt"></i> تقرير شهري</h4>
                    <div class="form-group">
                        <label>اختر الشهر:</label>
                        <input type="month" id="monthlyReportDate" class="form-control">
                    </div>
                    <button class="btn" onclick="generateMonthlyReportFixed()">
                        <i class="fas fa-chart-line"></i> عرض التقرير
                    </button>
                    <button class="btn btn-success" onclick="printMonthlyReportFixed()">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                </div>

                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                    <h4><i class="fas fa-warehouse"></i> تقرير المخزن</h4>
                    <div class="form-group">
                        <label>اختر المخزن:</label>
                        <select id="warehouseReportSelect" class="form-control">
                            <option value="">اختر المخزن</option>
                            <option value="المخزن الرئيسي">المخزن الرئيسي</option>
                            <option value="مخزن فرعي 1">مخزن فرعي 1</option>
                        </select>
                    </div>
                    <button class="btn" onclick="generateWarehouseReportFixed()">
                        <i class="fas fa-chart-line"></i> عرض التقرير
                    </button>
                    <button class="btn btn-success" onclick="printWarehouseReportFixed()">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                </div>
            </div>

            <div id="reportResults" style="margin-top: 30px;"></div>
        </div>
    </div>

    <script>
        // نظام مبسط وآمن
        class SimpleInventorySystem {
            constructor() {
                this.items = JSON.parse(localStorage.getItem('inventory_items')) || [];
                this.transactions = JSON.parse(localStorage.getItem('inventory_transactions')) || [];
                this.exchangeRate = 1500;
                this.init();
            }

            init() {
                console.log('🚀 تحميل النظام المبسط...');
                this.updateStats();
                this.renderInventoryTable();
                this.renderTransactionsTable();
                console.log('✅ تم تحميل النظام بنجاح');
            }

            updateStats() {
                try {
                    // إجمالي المواد
                    this.updateElement('totalItems', this.items.length);

                    // القيمة الإجمالية
                    let totalValueIQD = 0;
                    let totalValueUSD = 0;

                    this.items.forEach(item => {
                        const itemValue = item.quantity * (item.unitPrice || 0);
                        if (item.currency === 'USD') {
                            totalValueUSD += itemValue;
                            totalValueIQD += (itemValue * this.exchangeRate);
                        } else {
                            totalValueIQD += itemValue;
                        }
                    });

                    this.updateElement('totalValue', totalValueIQD.toLocaleString('ar-EG'));
                    this.updateElement('totalValueUSD', totalValueUSD.toLocaleString('ar-EG'));

                    // المواد المنخفضة
                    const lowStockItems = this.items.filter(item => item.quantity < (item.minStock || 10)).length;
                    this.updateElement('lowStockItems', lowStockItems);

                    // معاملات اليوم
                    const today = new Date().toDateString();
                    const todayTransactions = this.transactions.filter(transaction => {
                        return new Date(transaction.date).toDateString() === today;
                    }).length;
                    this.updateElement('todayTransactions', todayTransactions);

                    // سعر الصرف
                    this.updateElement('exchangeRateDisplay', this.exchangeRate.toLocaleString('ar-EG'));

                } catch (error) {
                    console.error('خطأ في تحديث الإحصائيات:', error);
                }
            }

            updateElement(id, value) {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            }

            renderInventoryTable() {
                const tbody = document.getElementById('inventoryTableBody');
                if (!tbody) return;

                if (this.items.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="7" style="text-align: center; padding: 40px; color: #6c757d;">
                                <i class="fas fa-box-open" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>
                                لا توجد مواد في المخزون<br>
                                <small>ابدأ بإضافة البيانات التجريبية</small>
                            </td>
                        </tr>
                    `;
                    return;
                }

                tbody.innerHTML = this.items.map(item => {
                    const totalValue = item.quantity * (item.unitPrice || 0);
                    const currencySymbol = (item.currency === 'USD') ? 'دولار' : 'د.ع';
                    const status = this.getItemStatus(item);

                    return `
                        <tr>
                            <td><strong>${item.name}</strong></td>
                            <td>${item.quantity.toLocaleString('ar-EG')}</td>
                            <td>${item.unit}</td>
                            <td>${(item.unitPrice || 0).toLocaleString('ar-EG')} ${currencySymbol}</td>
                            <td>${totalValue.toLocaleString('ar-EG')} ${currencySymbol}</td>
                            <td>${status}</td>
                            <td>${item.warehouse}</td>
                        </tr>
                    `;
                }).join('');
            }

            renderTransactionsTable() {
                const tbody = document.getElementById('transactionsTableBody');
                if (!tbody) return;

                if (this.transactions.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="9" style="text-align: center; padding: 40px; color: #6c757d;">
                                <i class="fas fa-exchange-alt" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>
                                لا توجد معاملات<br>
                                <small>ابدأ بإضافة البيانات التجريبية</small>
                            </td>
                        </tr>
                    `;
                    return;
                }

                tbody.innerHTML = this.transactions.slice(-20).reverse().map(transaction => {
                    const date = new Date(transaction.date);
                    const typeColor = transaction.transactionType === 'وارد' ? '#28a745' :
                                     transaction.transactionType === 'صادر' ? '#dc3545' : '#ffc107';

                    let priceDisplay = '-';
                    let totalDisplay = '-';

                    if (transaction.transactionType === 'وارد' || transaction.transactionType === 'تسوية') {
                        const currencySymbol = transaction.currency === 'USD' ? 'دولار' : 'د.ع';
                        priceDisplay = `${transaction.unitPrice.toLocaleString('ar-EG')} ${currencySymbol}`;
                        totalDisplay = `${transaction.totalPrice.toLocaleString('ar-EG')} ${currencySymbol}`;
                    }

                    return `
                        <tr>
                            <td>${date.toLocaleDateString('ar-EG')}</td>
                            <td><strong>${transaction.itemName}</strong></td>
                            <td><span style="color: ${typeColor}; font-weight: bold;">${transaction.transactionType}</span></td>
                            <td>${transaction.quantity.toLocaleString('ar-EG')} ${transaction.unit}</td>
                            <td>${priceDisplay}</td>
                            <td>${totalDisplay}</td>
                            <td>${transaction.warehouse}</td>
                            <td>${transaction.supplier}</td>
                            <td>
                                <button class="action-btn delete" onclick="deleteTransactionFixed(${transaction.id})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                }).join('');
            }

            getItemStatus(item) {
                const minStock = item.minStock || 10;

                if (item.quantity <= 0) {
                    return '<span class="badge badge-danger"><i class="fas fa-times"></i> نفد المخزون</span>';
                } else if (item.quantity < minStock) {
                    return '<span class="badge badge-warning"><i class="fas fa-exclamation-triangle"></i> منخفض</span>';
                } else {
                    return '<span class="badge badge-success"><i class="fas fa-check"></i> متوفر</span>';
                }
            }

            showAlert(message, type = 'success') {
                const alertContainer = document.getElementById('alertContainer');
                const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';

                alertContainer.innerHTML = `
                    <div class="alert ${alertClass}">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
                        ${message}
                    </div>
                `;

                setTimeout(() => {
                    alertContainer.innerHTML = '';
                }, 5000);
            }

            saveData() {
                localStorage.setItem('inventory_items', JSON.stringify(this.items));
                localStorage.setItem('inventory_transactions', JSON.stringify(this.transactions));
            }
        }

        // متغير النظام العام
        let inventorySystem = null;

        // تحميل النظام عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            try {
                inventorySystem = new SimpleInventorySystem();
                console.log('✅ النظام جاهز للاستخدام');
            } catch (error) {
                console.error('❌ خطأ في تحميل النظام:', error);
                alert('خطأ في تحميل النظام: ' + error.message);
            }
        });

        // دوال التبويبات
        function showTab(tabName) {
            try {
                // إخفاء جميع التبويبات
                document.querySelectorAll('.tab-content').forEach(tab => {
                    tab.classList.remove('active');
                });

                // إزالة التفعيل من جميع الأزرار
                document.querySelectorAll('.tab-btn').forEach(btn => {
                    btn.classList.remove('active');
                });

                // إظهار التبويب المحدد
                const targetTab = document.getElementById(tabName);
                if (targetTab) {
                    targetTab.classList.add('active');
                }

                // تفعيل الزر المحدد
                event.target.classList.add('active');

                console.log('تم التبديل إلى تبويب:', tabName);

            } catch (error) {
                console.error('خطأ في تبديل التبويب:', error);
            }
        }

        // دوال البيانات التجريبية
        function addSampleDataFixed() {
            try {
                console.log('📝 إضافة بيانات تجريبية...');

                if (!inventorySystem) {
                    throw new Error('النظام غير محمل');
                }

                const sampleItems = [
                    {
                        id: 1,
                        name: 'أسمنت',
                        quantity: 100,
                        unit: 'كيس',
                        unitPrice: 8500,
                        currency: 'IQD',
                        minStock: 20,
                        category: 'مواد البناء',
                        warehouse: 'المخزن الرئيسي',
                        created: new Date().toISOString(),
                        lastUpdated: new Date().toISOString()
                    },
                    {
                        id: 2,
                        name: 'حديد تسليح',
                        quantity: 50,
                        unit: 'طن',
                        unitPrice: 850000,
                        currency: 'IQD',
                        minStock: 10,
                        category: 'مواد البناء',
                        warehouse: 'المخزن الرئيسي',
                        created: new Date().toISOString(),
                        lastUpdated: new Date().toISOString()
                    },
                    {
                        id: 3,
                        name: 'مولد كهربائي',
                        quantity: 3,
                        unit: 'قطعة',
                        unitPrice: 800,
                        currency: 'USD',
                        minStock: 2,
                        category: 'معدات كهربائية',
                        warehouse: 'مخزن فرعي 1',
                        created: new Date().toISOString(),
                        lastUpdated: new Date().toISOString()
                    },
                    {
                        id: 4,
                        name: 'كابل كهربائي',
                        quantity: 1,
                        unit: 'متر',
                        unitPrice: 2500,
                        currency: 'IQD',
                        minStock: 50,
                        category: 'معدات كهربائية',
                        warehouse: 'المخزن الرئيسي',
                        created: new Date().toISOString(),
                        lastUpdated: new Date().toISOString()
                    }
                ];

                const sampleTransactions = [
                    {
                        id: 1,
                        itemName: 'أسمنت',
                        quantity: 100,
                        unit: 'كيس',
                        unitPrice: 8500,
                        currency: 'IQD',
                        totalPrice: 850000,
                        transactionType: 'وارد',
                        warehouse: 'المخزن الرئيسي',
                        supplier: 'شركة الأسمنت العراقية',
                        invoiceNumber: 'INV-001',
                        date: new Date().toISOString(),
                        exchangeRateAtTime: 1500
                    },
                    {
                        id: 2,
                        itemName: 'مولد كهربائي',
                        quantity: 5,
                        unit: 'قطعة',
                        unitPrice: 800,
                        currency: 'USD',
                        totalPrice: 4000,
                        transactionType: 'وارد',
                        warehouse: 'مخزن فرعي 1',
                        supplier: 'شركة المولدات المتقدمة',
                        invoiceNumber: 'INV-002',
                        date: new Date().toISOString(),
                        exchangeRateAtTime: 1500
                    }
                ];

                inventorySystem.items = sampleItems;
                inventorySystem.transactions = sampleTransactions;
                inventorySystem.saveData();
                inventorySystem.updateStats();
                inventorySystem.renderInventoryTable();
                inventorySystem.renderTransactionsTable();
                inventorySystem.showAlert('تم إضافة البيانات التجريبية بنجاح!', 'success');

                console.log('✅ تم إضافة البيانات التجريبية');

            } catch (error) {
                console.error('❌ خطأ في إضافة البيانات:', error);
                alert('خطأ في إضافة البيانات: ' + error.message);
            }
        }

        function refreshDataFixed() {
            try {
                if (inventorySystem) {
                    inventorySystem.updateStats();
                    inventorySystem.renderInventoryTable();
                    inventorySystem.renderTransactionsTable();
                    inventorySystem.showAlert('تم تحديث البيانات بنجاح!', 'success');
                }
            } catch (error) {
                console.error('خطأ في تحديث البيانات:', error);
            }
        }

        function clearAllDataFixed() {
            if (confirm('هل أنت متأكد من حذف جميع البيانات؟')) {
                try {
                    localStorage.clear();
                    location.reload();
                } catch (error) {
                    console.error('خطأ في مسح البيانات:', error);
                }
            }
        }

        // دوال التقارير
        function generateDailyReportFixed() {
            try {
                const date = document.getElementById('dailyReportDate').value;
                if (!date) {
                    inventorySystem.showAlert('يرجى اختيار التاريخ', 'danger');
                    return;
                }

                const transactions = inventorySystem.transactions.filter(t =>
                    new Date(t.date).toDateString() === new Date(date).toDateString()
                );

                displayReportFixed(`تقرير يومي - ${new Date(date).toLocaleDateString('ar-EG')}`, transactions);

            } catch (error) {
                console.error('خطأ في التقرير اليومي:', error);
                inventorySystem.showAlert('خطأ في إنشاء التقرير', 'danger');
            }
        }

        function generateWeeklyReportFixed() {
            try {
                const week = document.getElementById('weeklyReportDate').value;
                if (!week) {
                    inventorySystem.showAlert('يرجى اختيار الأسبوع', 'danger');
                    return;
                }

                const [year, weekNum] = week.split('-W');
                const startDate = new Date(year, 0, 1 + (weekNum - 1) * 7);
                const endDate = new Date(startDate.getTime() + 6 * 24 * 60 * 60 * 1000);

                const transactions = inventorySystem.transactions.filter(t => {
                    const tDate = new Date(t.date);
                    return tDate >= startDate && tDate <= endDate;
                });

                displayReportFixed(`تقرير أسبوعي - الأسبوع ${weekNum} من ${year}`, transactions);

            } catch (error) {
                console.error('خطأ في التقرير الأسبوعي:', error);
                inventorySystem.showAlert('خطأ في إنشاء التقرير', 'danger');
            }
        }

        function generateMonthlyReportFixed() {
            try {
                const month = document.getElementById('monthlyReportDate').value;
                if (!month) {
                    inventorySystem.showAlert('يرجى اختيار الشهر', 'danger');
                    return;
                }

                const [year, monthNum] = month.split('-');
                const transactions = inventorySystem.transactions.filter(t => {
                    const tDate = new Date(t.date);
                    return tDate.getFullYear() == year && tDate.getMonth() == monthNum - 1;
                });

                displayReportFixed(`تقرير شهري - ${monthNum}/${year}`, transactions);

            } catch (error) {
                console.error('خطأ في التقرير الشهري:', error);
                inventorySystem.showAlert('خطأ في إنشاء التقرير', 'danger');
            }
        }

        function generateWarehouseReportFixed() {
            try {
                const warehouse = document.getElementById('warehouseReportSelect').value;
                if (!warehouse) {
                    inventorySystem.showAlert('يرجى اختيار المخزن', 'danger');
                    return;
                }

                const transactions = inventorySystem.transactions.filter(t => t.warehouse === warehouse);
                displayReportFixed(`تقرير المخزن - ${warehouse}`, transactions);

            } catch (error) {
                console.error('خطأ في تقرير المخزن:', error);
                inventorySystem.showAlert('خطأ في إنشاء التقرير', 'danger');
            }
        }

        function displayReportFixed(title, transactions) {
            try {
                const totalIn = transactions.filter(t => t.transactionType === 'وارد').reduce((sum, t) => {
                    if (t.currency === 'USD') {
                        const exchangeRateUsed = t.exchangeRateAtTime || inventorySystem.exchangeRate;
                        return sum + (t.totalPrice * exchangeRateUsed);
                    }
                    return sum + (t.totalPrice || 0);
                }, 0);

                const totalOut = transactions.filter(t => t.transactionType === 'صادر').reduce((sum, t) => {
                    return sum + (t.totalPrice || 0);
                }, 0);

                const net = totalIn - totalOut;

                const reportHtml = `
                    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <h3>${title}</h3>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
                            <div style="background: #d4edda; padding: 15px; border-radius: 6px; text-align: center;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #155724;">${totalIn.toLocaleString('ar-EG')} د.ع</div>
                                <div style="color: #155724;">إجمالي الوارد</div>
                            </div>
                            <div style="background: #f8d7da; padding: 15px; border-radius: 6px; text-align: center;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #721c24;">${totalOut.toLocaleString('ar-EG')} د.ع</div>
                                <div style="color: #721c24;">إجمالي الصادر</div>
                            </div>
                            <div style="background: #cce5ff; padding: 15px; border-radius: 6px; text-align: center;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #004085;">${net.toLocaleString('ar-EG')} د.ع</div>
                                <div style="color: #004085;">الصافي</div>
                            </div>
                            <div style="background: #fff3cd; padding: 15px; border-radius: 6px; text-align: center;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #856404;">${transactions.length}</div>
                                <div style="color: #856404;">عدد المعاملات</div>
                            </div>
                        </div>

                        <div class="table-container">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>المادة</th>
                                        <th>النوع</th>
                                        <th>الكمية</th>
                                        <th>السعر</th>
                                        <th>المجموع</th>
                                        <th>المخزن</th>
                                        <th>المورد</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${transactions.map(t => {
                                        let priceDisplay = '-';
                                        let totalDisplay = '-';

                                        if (t.transactionType === 'وارد' || t.transactionType === 'تسوية') {
                                            const currencySymbol = t.currency === 'USD' ? 'دولار' : 'د.ع';
                                            priceDisplay = `${t.unitPrice.toLocaleString('ar-EG')} ${currencySymbol}`;
                                            totalDisplay = `${t.totalPrice.toLocaleString('ar-EG')} ${currencySymbol}`;
                                        }

                                        return `
                                            <tr>
                                                <td>${new Date(t.date).toLocaleDateString('ar-EG')}</td>
                                                <td>${t.itemName}</td>
                                                <td><span style="color: ${t.transactionType === 'وارد' ? '#28a745' : '#dc3545'}; font-weight: bold;">${t.transactionType}</span></td>
                                                <td>${t.quantity} ${t.unit}</td>
                                                <td>${priceDisplay}</td>
                                                <td>${totalDisplay}</td>
                                                <td>${t.warehouse}</td>
                                                <td>${t.supplier}</td>
                                            </tr>
                                        `;
                                    }).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                `;

                document.getElementById('reportResults').innerHTML = reportHtml;

            } catch (error) {
                console.error('خطأ في عرض التقرير:', error);
                inventorySystem.showAlert('خطأ في عرض التقرير', 'danger');
            }
        }

        // دوال الطباعة
        function printDailyReportFixed() {
            try {
                const date = document.getElementById('dailyReportDate').value;
                if (!date) {
                    inventorySystem.showAlert('يرجى اختيار التاريخ أولاً', 'danger');
                    return;
                }

                generateDailyReportFixed();
                setTimeout(() => {
                    window.print();
                }, 500);

            } catch (error) {
                console.error('خطأ في الطباعة:', error);
            }
        }

        function printWeeklyReportFixed() {
            try {
                const week = document.getElementById('weeklyReportDate').value;
                if (!week) {
                    inventorySystem.showAlert('يرجى اختيار الأسبوع أولاً', 'danger');
                    return;
                }

                generateWeeklyReportFixed();
                setTimeout(() => {
                    window.print();
                }, 500);

            } catch (error) {
                console.error('خطأ في الطباعة:', error);
            }
        }

        function printMonthlyReportFixed() {
            try {
                const month = document.getElementById('monthlyReportDate').value;
                if (!month) {
                    inventorySystem.showAlert('يرجى اختيار الشهر أولاً', 'danger');
                    return;
                }

                generateMonthlyReportFixed();
                setTimeout(() => {
                    window.print();
                }, 500);

            } catch (error) {
                console.error('خطأ في الطباعة:', error);
            }
        }

        function printWarehouseReportFixed() {
            try {
                const warehouse = document.getElementById('warehouseReportSelect').value;
                if (!warehouse) {
                    inventorySystem.showAlert('يرجى اختيار المخزن أولاً', 'danger');
                    return;
                }

                generateWarehouseReportFixed();
                setTimeout(() => {
                    window.print();
                }, 500);

            } catch (error) {
                console.error('خطأ في الطباعة:', error);
            }
        }

        // دالة إضافة معاملة جديدة
        function addTransactionFixed(event) {
            event.preventDefault();

            try {
                const formData = {
                    itemName: document.getElementById('itemName').value.trim(),
                    transactionType: document.getElementById('transactionType').value,
                    quantity: parseFloat(document.getElementById('quantity').value),
                    unit: document.getElementById('unit').value.trim(),
                    unitPrice: parseFloat(document.getElementById('unitPrice').value) || 0,
                    currency: document.getElementById('currency').value,
                    warehouse: document.getElementById('warehouse').value,
                    supplier: document.getElementById('supplier').value.trim(),
                    invoiceNumber: document.getElementById('invoiceNumber').value.trim()
                };

                // التحقق من البيانات
                if (!formData.itemName || !formData.transactionType || !formData.quantity || !formData.unit || !formData.warehouse) {
                    inventorySystem.showAlert('يرجى ملء جميع الحقول المطلوبة', 'danger');
                    return;
                }

                // إنشاء المعاملة
                const transaction = {
                    id: Date.now(),
                    ...formData,
                    totalPrice: formData.quantity * formData.unitPrice,
                    date: new Date().toISOString(),
                    exchangeRateAtTime: inventorySystem.exchangeRate
                };

                // إضافة المعاملة
                inventorySystem.transactions.push(transaction);

                // تحديث المخزون
                updateInventoryFromTransaction(transaction);

                // حفظ وتحديث
                inventorySystem.saveData();
                inventorySystem.updateStats();
                inventorySystem.renderInventoryTable();
                inventorySystem.renderTransactionsTable();

                // إعادة تعيين النموذج
                document.getElementById('transactionForm').reset();
                togglePriceFields();

                inventorySystem.showAlert('تم إضافة المعاملة بنجاح!', 'success');

            } catch (error) {
                console.error('خطأ في إضافة المعاملة:', error);
                inventorySystem.showAlert('خطأ في إضافة المعاملة: ' + error.message, 'danger');
            }
        }

        // دالة تحديث المخزون من المعاملة
        function updateInventoryFromTransaction(transaction) {
            let existingItem = inventorySystem.items.find(item =>
                item.name === transaction.itemName && item.warehouse === transaction.warehouse
            );

            if (existingItem) {
                // تحديث المادة الموجودة
                if (transaction.transactionType === 'وارد') {
                    existingItem.quantity += transaction.quantity;
                    if (transaction.unitPrice > 0) {
                        existingItem.unitPrice = transaction.unitPrice;
                        existingItem.currency = transaction.currency;
                    }
                } else if (transaction.transactionType === 'صادر') {
                    existingItem.quantity -= transaction.quantity;
                } else if (transaction.transactionType === 'تسوية') {
                    existingItem.quantity = transaction.quantity;
                    if (transaction.unitPrice > 0) {
                        existingItem.unitPrice = transaction.unitPrice;
                        existingItem.currency = transaction.currency;
                    }
                }
                existingItem.lastUpdated = new Date().toISOString();
            } else {
                // إضافة مادة جديدة (فقط للوارد والتسوية)
                if (transaction.transactionType === 'وارد' || transaction.transactionType === 'تسوية') {
                    const newItem = {
                        id: Date.now(),
                        name: transaction.itemName,
                        quantity: transaction.quantity,
                        unit: transaction.unit,
                        unitPrice: transaction.unitPrice || 0,
                        currency: transaction.currency || 'IQD',
                        minStock: 10,
                        category: 'عام',
                        warehouse: transaction.warehouse,
                        created: new Date().toISOString(),
                        lastUpdated: new Date().toISOString()
                    };
                    inventorySystem.items.push(newItem);
                } else {
                    throw new Error('لا يمكن إجراء ' + transaction.transactionType + ' لمادة غير موجودة');
                }
            }
        }

        // دالة إظهار/إخفاء حقول السعر
        function togglePriceFields() {
            const transactionType = document.getElementById('transactionType').value;
            const priceGroup = document.getElementById('priceGroup');
            const currencyGroup = document.getElementById('currencyGroup');
            const unitPriceInput = document.getElementById('unitPrice');

            if (transactionType === 'وارد' || transactionType === 'تسوية') {
                priceGroup.style.display = 'block';
                currencyGroup.style.display = 'block';
                unitPriceInput.required = true;
            } else {
                priceGroup.style.display = 'none';
                currencyGroup.style.display = 'none';
                unitPriceInput.required = false;
                unitPriceInput.value = '';
            }
        }

        // دالة إعادة تعيين النموذج
        function resetTransactionForm() {
            document.getElementById('transactionForm').reset();
            togglePriceFields();
        }

        // دالة حذف المعاملة
        function deleteTransactionFixed(id) {
            if (confirm('هل أنت متأكد من حذف هذه المعاملة؟')) {
                try {
                    inventorySystem.transactions = inventorySystem.transactions.filter(t => t.id !== id);
                    inventorySystem.saveData();
                    inventorySystem.updateStats();
                    inventorySystem.renderTransactionsTable();
                    inventorySystem.showAlert('تم حذف المعاملة بنجاح!', 'success');
                } catch (error) {
                    console.error('خطأ في حذف المعاملة:', error);
                    inventorySystem.showAlert('خطأ في حذف المعاملة', 'danger');
                }
            }
        }
    </script>
</body>
</html>