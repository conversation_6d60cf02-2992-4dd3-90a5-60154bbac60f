@echo off
echo ========================================
echo    نظام إدارة المخزون - هيمن كروب
echo        بناء التطبيق التنفيذي
echo ========================================
echo.

echo [1/4] فحص Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Node.js غير مثبت!
    echo يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)
echo ✓ Node.js مثبت

echo.
echo [2/4] تثبيت المتطلبات...
call npm install
if %errorlevel% neq 0 (
    echo خطأ في تثبيت المتطلبات!
    pause
    exit /b 1
)
echo ✓ تم تثبيت المتطلبات

echo.
echo [3/4] بناء التطبيق...
call npm run build-win
if %errorlevel% neq 0 (
    echo خطأ في بناء التطبيق!
    pause
    exit /b 1
)
echo ✓ تم بناء التطبيق

echo.
echo [4/4] اكتمل البناء!
echo ملف التثبيت موجود في: dist\
echo.

echo فتح مجلد الملفات المبنية...
start explorer dist

echo.
echo ========================================
echo       تم إنجاز البناء بنجاح!
echo ========================================
pause
