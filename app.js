// نظام إدارة المخزون الاحترافي المتطور - JavaScript
class InventoryManager {
    constructor() {
        this.items = JSON.parse(localStorage.getItem('inventory_items')) || [];
        this.transactions = JSON.parse(localStorage.getItem('inventory_transactions')) || [];
        this.warehouses = JSON.parse(localStorage.getItem('inventory_warehouses')) || [];
        this.settings = JSON.parse(localStorage.getItem('inventory_settings')) || this.getDefaultSettings();
        this.exchangeRate = this.settings.exchangeRate || 1500; // سعر صرف الدولار مقابل الدينار العراقي

        // نظام الميزانية - يجمع كل المدفوعات ولا يمسح منه شيء
        this.budget = JSON.parse(localStorage.getItem('inventory_budget')) || {
            totalIQD: 0,
            totalUSD: 0,
            transactions: []
        };
        // تحديث المخازن الموجودة للنصوص العربية الجديدة
        this.updateExistingWarehouses();

        this.init();
    }

    updateExistingWarehouses() {
        let updated = false;

        // تحديث المخازن التي تحتاج تحديث فقط
        this.warehouses.forEach(warehouse => {
            console.log('فحص المخزن:', warehouse.name, 'النوع الحالي:', warehouse.type);

            // تحديث النصوص القديمة للنصوص العربية الجديدة فقط
            if (warehouse.type === 'رئيسي' || warehouse.type === 'عادي') {
                warehouse.type = 'مخزن رئيسي';
                updated = true;
                console.log('تم تحديث', warehouse.name, 'إلى مخزن رئيسي');
            } else if (warehouse.type === 'فرعي' || warehouse.type === 'تصدير') {
                warehouse.type = 'مخزن فرعي';
                updated = true;
                console.log('تم تحديث', warehouse.name, 'إلى مخزن فرعي');
            } else if (!warehouse.type) {
                // إذا لم يكن هناك نوع، اجعله رئيسي
                warehouse.type = 'مخزن رئيسي';
                updated = true;
                console.log('تم إضافة نوع افتراضي لـ', warehouse.name, ': مخزن رئيسي');
            } else {
                console.log('المخزن', warehouse.name, 'لا يحتاج تحديث، النوع:', warehouse.type);
            }
        });

        // إجبار الحفظ والتحديث
        if (updated || this.warehouses.length > 0) {
            this.saveData();
            console.log('تم حفظ تحديثات المخازن');

            // إعادة تحميل البيانات من localStorage للتأكد
            setTimeout(() => {
                this.warehouses = JSON.parse(localStorage.getItem('inventory_warehouses')) || [];
                this.renderWarehousesTable();
                this.updateWarehouseSelects();
                console.log('تم إعادة تحميل المخازن:', this.warehouses);
            }, 100);
        }
    }

    getDefaultSettings() {
        return {
            companyName: 'هيمن كروب',
            companyAddress: '',
            companyPhone: '',
            currency: 'د.ع',
            lowStockThreshold: 10,
            exchangeRate: 1500
        };
    }

    init() {
        this.bindEvents();
        this.updateStats();
        this.renderInventoryTable();
        this.renderTransactionsTable();
        this.renderWarehousesTable();
        this.loadWarehouses();
        this.setCurrentDateTime();
        this.showWelcomeMessage();
        this.initializeDefaultWarehouses();
        this.setupCostCalculation();
        this.loadExchangeRate();
    }

    bindEvents() {
        const form = document.getElementById('itemForm');
        form.addEventListener('submit', (e) => this.handleFormSubmit(e));

        // تم إزالة event handler للنموذج - نستخدم onclick مباشرة

        // مراقبة تغيير المخزن
        const warehouseSelect = document.getElementById('warehouse');
        warehouseSelect.addEventListener('change', (e) => this.handleWarehouseChange(e));
    }

    setupCostCalculation() {
        const quantityField = document.getElementById('quantity');
        const priceField = document.getElementById('unitPrice');
        const currencyField = document.getElementById('currency');
        const totalCostField = document.getElementById('totalCost');
        const transactionTypeField = document.getElementById('transactionType');

        // حقول الأسعار التي يجب إخفاؤها/إظهارها
        const priceFields = [
            document.getElementById('unitPrice').closest('.form-group'),
            document.getElementById('currency').closest('.form-group'),
            document.getElementById('totalCost').closest('.form-group'),
            document.getElementById('currentExchangeRate').closest('.form-group')
        ];

        const togglePriceFields = () => {
            const transactionType = transactionTypeField.value;
            const shouldShow = transactionType === 'وارد' || transactionType === 'تسوية';

            priceFields.forEach(field => {
                if (field) {
                    field.style.display = shouldShow ? 'block' : 'none';
                }
            });

            // إذا كان صادر أو نقل، امسح قيم الأسعار
            if (!shouldShow) {
                priceField.value = '';
                totalCostField.value = '';
                priceField.removeAttribute('required');
            } else {
                priceField.setAttribute('required', 'required');
                calculateCost();
            }
        };

        const calculateCost = () => {
            const quantity = parseFloat(quantityField.value) || 0;
            const price = parseFloat(priceField.value) || 0;
            const currency = currencyField.value;

            let totalCost = quantity * price;
            let displayText = '';

            if (currency === 'USD') {
                const costInIQD = totalCost * this.exchangeRate;
                displayText = `${totalCost.toLocaleString('ar-EG')} دولار (${costInIQD.toLocaleString('ar-EG')} د.ع بسعر ${this.exchangeRate})`;
            } else {
                displayText = `${totalCost.toLocaleString('ar-EG')} د.ع`;
            }

            totalCostField.value = displayText;
        };

        // إضافة مستمعات الأحداث
        transactionTypeField.addEventListener('change', togglePriceFields);
        quantityField.addEventListener('input', calculateCost);
        priceField.addEventListener('input', calculateCost);
        currencyField.addEventListener('change', calculateCost);

        // تطبيق الحالة الأولية
        togglePriceFields();
    }

    loadExchangeRate() {
        const exchangeRateField = document.getElementById('exchangeRate');
        const currentExchangeRateField = document.getElementById('currentExchangeRate');

        if (exchangeRateField) {
            exchangeRateField.value = this.exchangeRate;
        }
        if (currentExchangeRateField) {
            currentExchangeRateField.value = this.exchangeRate;
        }
    }

    setCurrentDateTime() {
        const now = new Date();
        const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
        document.getElementById('transactionDate').value = localDateTime;
    }

    initializeDefaultWarehouses() {
        // لا نضيف مخازن افتراضية - يضيفها المستخدم بنفسه

        // إصلاح البيانات الموجودة فقط
        this.fixExistingData();
    }

    fixExistingData() {
        let dataFixed = false;

        this.items.forEach(item => {
            if (item.minStock === undefined || item.minStock === null) {
                item.minStock = 10; // قيمة افتراضية
                dataFixed = true;
            }
            if (!item.location) {
                item.location = `${item.warehouse || 'مخزن غير محدد'} - موقع افتراضي`;
                dataFixed = true;
            }
            if (!item.currency) {
                item.currency = 'IQD';
                dataFixed = true;
            }
        });

        if (dataFixed) {
            this.saveData();
            console.log('تم إصلاح البيانات الموجودة');
        }
    }

    showWelcomeMessage() {
        if (this.items.length === 0) {
            this.showAlert('مرحباً بك في نظام إدارة المخزون الاحترافي! ابدأ بإضافة أول معاملة.', 'success');
        }
    }

    handleFormSubmit(e) {
        e.preventDefault();

        let warehouseValue = document.getElementById('warehouse').value;
        if (warehouseValue === 'مخزن جديد') {
            warehouseValue = document.getElementById('newWarehouse').value.trim();
            if (!warehouseValue) {
                this.showAlert('يرجى إدخال اسم المخزن الجديد', 'danger');
                return;
            }
        }

        const transactionType = document.getElementById('transactionType').value;

        // للصادر: استخدم المستلم والمخزن المصروف له ومخزن التصدير، للوارد: استخدم المورد
        const supplierValue = transactionType === 'صادر' ? '' : document.getElementById('supplier').value.trim();
        const receiverValue = transactionType === 'صادر' ? document.getElementById('receiver').value.trim() : '';
        const destinationWarehouseValue = transactionType === 'صادر' ? document.getElementById('destinationWarehouse').value.trim() : '';
        const exportWarehouseValue = transactionType === 'صادر' ? document.getElementById('exportWarehouse').value.trim() : '';

        const formData = {
            itemName: document.getElementById('itemName').value.trim(),
            quantity: parseFloat(document.getElementById('quantity').value),
            unit: document.getElementById('unit').value.trim(),
            transactionType: transactionType,
            transactionDate: document.getElementById('transactionDate').value,
            unitPrice: parseFloat(document.getElementById('unitPrice').value) || 0,
            currency: document.getElementById('currency').value,
            warehouse: warehouseValue,
            supplier: supplierValue,
            receiver: receiverValue,
            destinationWarehouse: destinationWarehouseValue,
            exportWarehouse: exportWarehouseValue,
            invoiceNumber: document.getElementById('invoiceNumber').value.trim(),
            notes: document.getElementById('notes').value.trim()
        };



        if (!this.validateForm(formData)) {
            return;
        }

        this.processTransaction(formData);
        this.clearForm();
        this.updateStats();
        this.renderInventoryTable();
        this.renderTransactionsTable();
        this.renderWarehousesTable();
        this.showAlert('تم حفظ المعاملة بنجاح! ✅', 'success');
    }

    handleWarehouseSubmit(e) {
        e.preventDefault();

        const warehouseData = {
            id: Date.now(),
            name: document.getElementById('warehouseName').value.trim(),
            location: document.getElementById('warehouseLocation').value.trim(),
            manager: document.getElementById('warehouseManager').value.trim(),
            monthlyBudget: parseFloat(document.getElementById('monthlyBudget').value) || 0,
            budgetCurrency: document.getElementById('budgetCurrency').value,
            spent: 0,
            status: 'نشط',
            created: new Date().toISOString()
        };

        if (!warehouseData.name) {
            this.showAlert('يرجى إدخال اسم المخزن', 'danger');
            return;
        }

        this.warehouses.push(warehouseData);
        this.saveData();
        this.renderWarehousesTable();
        this.loadWarehouses();
        this.clearWarehouseForm();
        this.showAlert('تم إضافة المخزن بنجاح! ✅', 'success');
    }

    handleWarehouseChange(e) {
        const newWarehouseGroup = document.getElementById('newWarehouseGroup');
        if (e.target.value === 'مخزن جديد') {
            newWarehouseGroup.style.display = 'block';
            document.getElementById('newWarehouse').required = true;
        } else {
            newWarehouseGroup.style.display = 'none';
            document.getElementById('newWarehouse').required = false;
        }
    }

    validateForm(data) {
        // التحقق من الحقول الأساسية
        let requiredSupplierOrReceiver;
        if (data.transactionType === 'صادر') {
            requiredSupplierOrReceiver = data.receiver; // للصادر نحتاج المستلم
        } else {
            requiredSupplierOrReceiver = data.supplier; // للوارد نحتاج المورد
        }

        if (!data.itemName || !data.quantity || !data.unit || !data.transactionType || !data.warehouse || !requiredSupplierOrReceiver || !data.transactionDate) {
            const missingField = data.transactionType === 'صادر' ? 'المستلم' : 'المورد';
            this.showAlert(`يرجى ملء جميع الحقول المطلوبة بما في ذلك ${missingField}`, 'danger');
            return false;
        }

        if (data.quantity <= 0) {
            this.showAlert('يجب أن تكون الكمية أكبر من صفر', 'danger');
            return false;
        }

        // التحقق من السعر فقط للوارد والتسوية
        if (data.transactionType === 'وارد' || data.transactionType === 'تسوية') {
            if (!data.unitPrice || data.unitPrice <= 0) {
                this.showAlert('يجب إدخال سعر صحيح للمعاملات الواردة', 'danger');
                return false;
            }
        }

        // التحقق من وجود مخزون كافي للصرف والنقل
        if (data.transactionType === 'صادر' || data.transactionType === 'نقل') {
            const existingItem = this.items.find(item =>
                item.name === data.itemName && item.warehouse === data.warehouse
            );

            if (!existingItem) {
                this.showAlert(`المادة "${data.itemName}" غير موجودة في ${data.warehouse}`, 'danger');
                return false;
            }

            if (existingItem.quantity < data.quantity) {
                this.showAlert(`المخزون الحالي في ${data.warehouse} (${existingItem.quantity} ${existingItem.unit}) غير كافي للصرف المطلوب (${data.quantity} ${data.unit})`, 'danger');
                return false;
            }
        }

        return true;
    }

    processTransaction(data) {
        // حساب التكلفة بالدينار العراقي (فقط للوارد والتسوية)
        let costInIQD = 0;
        let totalPrice = 0;

        if (data.transactionType === 'وارد' || data.transactionType === 'تسوية') {
            totalPrice = data.quantity * (data.unitPrice || 0);
            costInIQD = totalPrice;
            if (data.currency === 'USD') {
                costInIQD = costInIQD * this.exchangeRate;
            }
        }

        // إضافة المعاملة مع حفظ سعر الصرف وقت المعاملة
        const transaction = {
            id: Date.now(),
            ...data,
            totalPrice: totalPrice,
            totalPriceIQD: costInIQD,
            exchangeRateAtTime: this.exchangeRate, // حفظ سعر الصرف وقت المعاملة
            date: data.transactionDate ? new Date(data.transactionDate).toISOString() : new Date().toISOString(),
            timestamp: new Date().toLocaleString('ar-EG'),
            user: 'المستخدم الحالي',
            // إضافة الحقول الجديدة للصادر
            receiver: data.receiver || '',
            destinationWarehouse: data.destinationWarehouse || ''
        };

        this.transactions.push(transaction);

        // إضافة للميزانية العامة (فقط الوارد - المدفوعات)
        if (data.transactionType === 'وارد') {
            this.updateWarehouseBudget(data.warehouse, costInIQD);

            // إضافة للميزانية العامة
            if (data.currency === 'USD') {
                this.budget.totalUSD += totalPrice;
            } else {
                this.budget.totalIQD += totalPrice;
            }

            // حفظ تفاصيل المعاملة في الميزانية
            this.budget.transactions.push({
                id: transaction.id,
                date: transaction.date,
                amount: totalPrice,
                currency: data.currency,
                exchangeRate: this.exchangeRate,
                itemName: data.itemName,
                warehouse: data.warehouse
            });
        }

        // تحديث أو إضافة المادة
        let existingItem = this.items.find(item =>
            item.name === data.itemName && item.warehouse === data.warehouse
        );

        if (existingItem) {
            // تحديث المادة الموجودة
            if (data.transactionType === 'وارد') {
                existingItem.quantity += data.quantity;
                // تحديث السعر والعملة فقط للوارد
                if (data.unitPrice) {
                    existingItem.unitPrice = data.unitPrice;
                    existingItem.currency = data.currency;
                }
            } else if (data.transactionType === 'صادر') {
                existingItem.quantity -= data.quantity;
                // لا نحدث السعر للصادر
            } else if (data.transactionType === 'تسوية') {
                existingItem.quantity = data.quantity;
                // تحديث السعر والعملة للتسوية
                if (data.unitPrice) {
                    existingItem.unitPrice = data.unitPrice;
                    existingItem.currency = data.currency;
                }
            } else if (data.transactionType === 'نقل') {
                existingItem.quantity -= data.quantity;
                // لا نحدث السعر للنقل
            }

            existingItem.lastUpdated = new Date().toISOString();
            existingItem.lastTransaction = {
                type: data.transactionType,
                quantity: data.quantity,
                date: transaction.date,
                warehouse: data.warehouse
            };
        } else {
            // إضافة مادة جديدة (فقط للوارد والتسوية)
            if (data.transactionType === 'صادر' || data.transactionType === 'نقل') {
                this.showAlert(`لا يمكن إجراء ${data.transactionType} لمادة غير موجودة في المخزون`, 'danger');
                return;
            }

            let initialQuantity = 0;
            if (data.transactionType === 'وارد') {
                initialQuantity = data.quantity;
            } else if (data.transactionType === 'تسوية') {
                initialQuantity = data.quantity;
            }

            const newItem = {
                id: Date.now(),
                name: data.itemName,
                quantity: initialQuantity,
                unit: data.unit,
                unitPrice: data.unitPrice || 0,
                currency: data.currency || 'IQD',
                minStock: 10, // حد أدنى افتراضي
                category: 'عام',
                warehouse: data.warehouse,
                location: `${data.warehouse} - موقع افتراضي`,
                created: new Date().toISOString(),
                lastUpdated: new Date().toISOString(),
                lastTransaction: {
                    type: data.transactionType,
                    quantity: data.quantity,
                    date: transaction.date,
                    warehouse: data.warehouse
                }
            };

            this.items.push(newItem);
        }

        this.saveData();
    }

    updateWarehouseBudget(warehouseName, amount) {
        const warehouse = this.warehouses.find(w => w.name === warehouseName);
        if (warehouse) {
            // تحويل المبلغ إلى عملة المخزن
            let amountInWarehouseCurrency = amount;
            if (warehouse.budgetCurrency === 'USD') {
                amountInWarehouseCurrency = amount / this.exchangeRate;
            }
            warehouse.spent += amountInWarehouseCurrency;
        }
    }

    updateStats() {
        try {
            // إجمالي المواد
            const totalItemsEl = document.getElementById('totalItems');
            if (totalItemsEl) totalItemsEl.textContent = this.items.length;

            // حساب القيمة الإجمالية بناءً على المعاملات الفعلية (وليس السعر الحالي)
            let totalValueIQD = 0;
            let totalValueUSD = 0;

            this.items.forEach(item => {
                // حساب القيمة الحقيقية للمادة بناءً على معاملاتها
                const itemValues = this.calculateRealItemValue(item);
                totalValueIQD += itemValues.totalIQD;
                totalValueUSD += itemValues.totalUSD;
            });





            // تحديث صفحة الميزانية إذا كانت مفتوحة
            this.updateBudgetPage();

            // المواد المنخفضة
            const lowStockItems = this.items.filter(item => item.quantity < (item.minStock || 10)).length;
            const lowStockEl = document.getElementById('lowStockItems');
            if (lowStockEl) lowStockEl.textContent = lowStockItems;

            // معاملات اليوم
            const today = new Date().toDateString();
            const todayTransactions = this.transactions.filter(transaction => {
                return new Date(transaction.date).toDateString() === today;
            }).length;
            const todayTransactionsEl = document.getElementById('todayTransactions');
            if (todayTransactionsEl) todayTransactionsEl.textContent = todayTransactions;

        } catch (error) {
            console.error('خطأ في تحديث الإحصائيات:', error);
        }
    }

    // تحديث صفحة الميزانية
    updateBudgetPage() {
        // حساب المجموع الموحد (دولار محول لدينار + دينار)
        let totalCombinedIQD = 0;

        // جمع جميع المعاملات بالدينار (محولة من الدولار أو أصلية)
        this.budget.transactions.forEach(transaction => {
            if (transaction.currency === 'USD') {
                totalCombinedIQD += (transaction.amount * transaction.exchangeRate);
            } else {
                totalCombinedIQD += transaction.amount;
            }
        });

        // تحديث الإحصائيات
        const budgetTotalCombinedEl = document.getElementById('budgetTotalCombined');
        const budgetTotalIQDEl = document.getElementById('budgetTotalIQD');
        const budgetTotalUSDEl = document.getElementById('budgetTotalUSD');
        const budgetTransactionsCountEl = document.getElementById('budgetTransactionsCount');

        if (budgetTotalCombinedEl) {
            budgetTotalCombinedEl.textContent = totalCombinedIQD.toLocaleString('en-US');
        }
        if (budgetTotalIQDEl) {
            budgetTotalIQDEl.textContent = this.budget.totalIQD.toLocaleString('en-US');
        }
        if (budgetTotalUSDEl) {
            budgetTotalUSDEl.textContent = this.budget.totalUSD.toLocaleString('en-US');
        }
        if (budgetTransactionsCountEl) {
            budgetTransactionsCountEl.textContent = this.budget.transactions.length;
        }

        // تحديث الجدول
        this.renderBudgetTable();
    }

    // عرض جدول الميزانية
    renderBudgetTable() {
        const tbody = document.getElementById('budgetTableBody');
        if (!tbody) return;

        if (this.budget.transactions.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" style="text-align: center; color: #7f8c8d; padding: 40px;">
                        <i class="fas fa-wallet" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>
                        لا توجد مدفوعات بعد<br>
                        <small>ستظهر هنا جميع المدفوعات عند إضافة معاملات الوارد</small>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.budget.transactions.slice().reverse().map(payment => {
            const date = new Date(payment.date);
            const currencySymbol = payment.currency === 'USD' ? 'دولار' : 'د.ع';
            const equivalentIQD = payment.currency === 'USD' ?
                (payment.amount * payment.exchangeRate).toLocaleString('en-US') :
                payment.amount.toLocaleString('en-US');

            // التحقق من حالة المادة (موجودة أم مصروفة)
            const currentItem = this.items.find(item =>
                item.name === payment.itemName && item.warehouse === payment.warehouse
            );
            const status = currentItem && currentItem.quantity > 0 ?
                '<span class="badge badge-success">موجودة</span>' :
                '<span class="badge badge-warning">مصروفة</span>';

            return `
                <tr>
                    <td>
                        <small>${date.toLocaleDateString('ar-EG')}</small><br>
                        <small style="color: #7f8c8d;">${date.toLocaleTimeString('ar-EG', {hour: '2-digit', minute: '2-digit'})}</small>
                    </td>
                    <td><strong>${payment.itemName}</strong></td>
                    <td><strong>${payment.amount.toLocaleString('en-US')}</strong></td>
                    <td>${currencySymbol}</td>
                    <td>${payment.exchangeRate.toLocaleString('en-US')}</td>
                    <td><strong>${equivalentIQD} د.ع</strong></td>
                    <td>${payment.warehouse}</td>
                    <td>${status}</td>
                </tr>
            `;
        }).join('');
    }

    // حساب القيمة الحقيقية للمادة بناءً على معاملاتها الفعلية
    calculateRealItemValue(item) {
        let totalIQD = 0;
        let totalUSD = 0;
        let remainingQuantity = item.quantity;

        // العثور على جميع معاملات الوارد لهذه المادة مرتبة من الأحدث للأقدم
        const incomingTransactions = this.transactions
            .filter(t =>
                t.itemName === item.name &&
                t.warehouse === item.warehouse &&
                t.transactionType === 'وارد'
            )
            .sort((a, b) => new Date(b.date) - new Date(a.date)); // الأحدث أولاً (LIFO)

        // حساب القيمة بناءً على آخر معاملات الوارد (LIFO - Last In First Out)
        for (const transaction of incomingTransactions) {
            if (remainingQuantity <= 0) break;

            const quantityToTake = Math.min(remainingQuantity, transaction.quantity);
            const valuePerUnit = transaction.unitPrice || 0;
            const totalValueForThisQuantity = quantityToTake * valuePerUnit;

            if (transaction.currency === 'USD') {
                totalUSD += totalValueForThisQuantity;
                // تحويل للدينار بسعر الصرف وقت المعاملة
                const exchangeRateUsed = transaction.exchangeRateAtTime || this.exchangeRate;
                totalIQD += (totalValueForThisQuantity * exchangeRateUsed);
            } else {
                totalIQD += totalValueForThisQuantity;
            }

            remainingQuantity -= quantityToTake;
        }

        return {
            totalIQD: totalIQD,
            totalUSD: totalUSD,
            averageExchangeRate: totalUSD > 0 ? (totalIQD / totalUSD) : this.exchangeRate
        };
    }

    renderInventoryTable() {
        const tbody = document.getElementById('inventoryTableBody');
        const tbodyDetailed = document.getElementById('inventoryTableBodyDetailed');

        if (this.items.length === 0) {
            const emptyRow = `
                <tr>
                    <td colspan="8" style="text-align: center; color: #7f8c8d; padding: 40px;">
                        <i class="fas fa-box-open" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>
                        لا توجد مواد في المخزون<br>
                        <small>ابدأ بإضافة أول معاملة</small>
                    </td>
                </tr>
            `;
            if (tbody) tbody.innerHTML = emptyRow;
            if (tbodyDetailed) tbodyDetailed.innerHTML = emptyRow;
            return;
        }

        const itemsHtml = this.items.map(item => {
            // تأكد من وجود العملة
            if (!item.currency) item.currency = 'IQD';

            // حساب القيمة الحقيقية بناءً على المعاملات الفعلية
            const realValues = this.calculateRealItemValue(item);
            const status = this.getItemStatus(item);
            const lastTransaction = item.lastTransaction ?
                `${item.lastTransaction.type} - ${new Date(item.lastTransaction.date).toLocaleDateString('ar-EG')}` :
                'لا توجد حركة';

            // عرض القيمة الحقيقية
            let totalValueDisplay;
            if (realValues.totalUSD > 0 && realValues.totalIQD > 0) {
                // مختلط (دولار ودينار)
                totalValueDisplay = `
                    <strong>${realValues.totalUSD.toLocaleString('en-US')} دولار</strong><br>
                    <strong>${realValues.totalIQD.toLocaleString('en-US')} د.ع</strong>
                `;
            } else if (realValues.totalUSD > 0) {
                // دولار فقط
                totalValueDisplay = `
                    <strong>${realValues.totalUSD.toLocaleString('en-US')} دولار</strong><br>
                    <small style="color: #7f8c8d;">(${realValues.totalIQD.toLocaleString('en-US')} د.ع)</small>
                `;
            } else {
                // دينار فقط
                totalValueDisplay = `<strong>${realValues.totalIQD.toLocaleString('en-US')} د.ع</strong>`;
            }

            const simpleRow = `
                <tr>
                    <td>
                        <strong>${item.name}</strong>
                        <br><small style="color: #7f8c8d;">${item.category}</small>
                    </td>
                    <td>
                        <span style="font-weight: 600; font-size: 1.1rem;">
                            ${item.quantity.toLocaleString('en-US')}
                        </span>
                    </td>
                    <td>${item.unit}</td>
                    <td>${status}</td>
                </tr>
            `;

            const detailedRow = `
                <tr>
                    <td>
                        <strong>${item.name}</strong>
                        <br><small style="color: #7f8c8d;">${item.category} - ${item.location || 'غير محدد'}</small>
                    </td>
                    <td>
                        <span style="font-weight: 600; font-size: 1.1rem; ${item.quantity < (item.minStock || 10) ? 'color: #e74c3c;' : ''}">
                            ${item.quantity.toLocaleString('ar-EG')}
                        </span>
                        <br><small style="color: #7f8c8d;">الحد الأدنى: ${item.minStock || 10}</small>
                    </td>
                    <td>${item.unit}</td>
                    <td>
                        <small style="color: #7f8c8d;">${lastTransaction}</small>
                    </td>
                    <td>${status}</td>
                    <td>
                        <button class="action-btn edit" onclick="editItem(${item.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete" onclick="deleteItem(${item.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;

            return { simple: simpleRow, detailed: detailedRow };
        });

        if (tbody) {
            tbody.innerHTML = itemsHtml.map(item => item.simple).join('');
        }
        if (tbodyDetailed) {
            tbodyDetailed.innerHTML = itemsHtml.map(item => item.detailed).join('');
        }
    }

    renderTransactionsTable() {
        const tbody = document.getElementById('transactionsTableBody');

        if (this.transactions.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="10" style="text-align: center; color: #7f8c8d; padding: 40px;">
                        <i class="fas fa-exchange-alt" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>
                        لا توجد معاملات<br>
                        <small>ابدأ بإضافة أول معاملة</small>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.transactions.slice(-50).reverse().map(transaction => {
            const date = new Date(transaction.date);
            const typeColor = transaction.transactionType === 'وارد' ? '#2ecc71' :
                             transaction.transactionType === 'صادر' ? '#e74c3c' : '#f39c12';

            const currencySymbol = transaction.currency === 'USD' ? 'دولار' : 'د.ع';

            return `
                <tr>
                    <td>
                        <small>${date.toLocaleDateString('ar-EG')}</small><br>
                        <small style="color: #7f8c8d;">${date.toLocaleTimeString('ar-EG', {hour: '2-digit', minute: '2-digit'})}</small>
                    </td>
                    <td><strong>${transaction.itemName}</strong></td>
                    <td>
                        <span class="badge" style="background: ${typeColor}; color: white;">
                            ${transaction.transactionType}
                        </span>
                    </td>
                    <td>${transaction.quantity.toLocaleString('ar-EG')} ${transaction.unit}</td>
                    <td>${transaction.warehouse}</td>
                    <td>
                        ${transaction.transactionType === 'صادر' ?
                            (transaction.receiver || '-') :
                            (transaction.supplier || '-')
                        }
                        ${transaction.transactionType === 'صادر' && transaction.destinationWarehouse ?
                            `<br><small style="color: #7f8c8d;">إلى: ${transaction.destinationWarehouse}</small>` :
                            ''
                        }
                        ${transaction.transactionType === 'صادر' && transaction.exportWarehouse ?
                            `<br><small style="color: #3498db;"><i class="fas fa-plane"></i> تصدير: ${transaction.exportWarehouse}</small>` :
                            ''
                        }
                    </td>
                    <td>${transaction.invoiceNumber || '-'}</td>
                    <td>
                        <button class="action-btn delete" onclick="deleteTransaction(${transaction.id})" title="حذف من السجلات فقط">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button class="action-btn" onclick="reverseTransactionEffect(${transaction.id})" title="عكس التأثير وحذف" style="background: #dc3545; color: white; margin-right: 5px;">
                            <i class="fas fa-undo"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    }

    renderWarehousesTable() {
        const tbody = document.getElementById('warehousesTableBody');

        if (this.warehouses.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" style="text-align: center; color: #7f8c8d; padding: 40px;">
                        <i class="fas fa-warehouse" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>
                        لا توجد مخازن<br>
                        <small>ابدأ بإضافة أول مخزن</small>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.warehouses.map(warehouse => {
            const currentMonth = new Date().getMonth();
            const currentYear = new Date().getFullYear();

            // حساب المصروف هذا الشهر
            const monthlySpent = this.getMonthlySpent(warehouse.name, currentMonth, currentYear);
            const remaining = warehouse.monthlyBudget - monthlySpent;
            const percentage = warehouse.monthlyBudget > 0 ? (monthlySpent / warehouse.monthlyBudget * 100) : 0;
            const statusColor = percentage > 90 ? '#e74c3c' : percentage > 70 ? '#f39c12' : '#2ecc71';

            const currencySymbol = warehouse.budgetCurrency === 'USD' ? 'دولار' : 'د.ع';

            // تحديد اللون والأيقونة حسب النوع
            const isBranch = warehouse.type === 'مخزن فرعي' || warehouse.type === 'فرعي' || warehouse.type === 'تصدير';
            const typeColor = isBranch ? '#3498db' : '#2ecc71';
            const typeIcon = isBranch ? 'fas fa-map-marker-alt' : 'fas fa-warehouse';

            return `
                <tr>
                    <td>
                        <strong>${warehouse.name}</strong>
                    </td>
                    <td>
                        <span class="badge" style="background: ${typeColor}; color: white;">
                            <i class="${typeIcon}"></i> ${warehouse.type === 'مخزن رئيسي' ? 'مخزن رئيسي' :
                                                        warehouse.type === 'مخزن فرعي' ? 'مخزن فرعي' :
                                                        warehouse.type === 'رئيسي' ? 'مخزن رئيسي' :
                                                        warehouse.type === 'فرعي' ? 'مخزن فرعي' :
                                                        warehouse.type === 'عادي' ? 'مخزن رئيسي' :
                                                        warehouse.type === 'تصدير' ? 'مخزن فرعي' :
                                                        'مخزن رئيسي'}
                        </span>
                    </td>
                    <td>${warehouse.location || 'غير محدد'}</td>
                    <td>${warehouse.manager || 'غير محدد'}</td>
                    <td>${warehouse.monthlyBudget.toLocaleString('ar-EG')} ${currencySymbol}</td>
                    <td style="color: #e74c3c;">${monthlySpent.toLocaleString('ar-EG')} ${currencySymbol}</td>
                    <td style="color: ${remaining >= 0 ? '#2ecc71' : '#e74c3c'};">
                        ${remaining.toLocaleString('ar-EG')} ${currencySymbol}
                    </td>
                    <td>
                        <span class="badge" style="background: ${statusColor}; color: white;">
                            ${warehouse.status}
                        </span>
                        <br><small>${percentage.toFixed(1)}% مستخدم</small>
                    </td>
                    <td>
                        <button class="action-btn edit" onclick="editWarehouse(${warehouse.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete" onclick="deleteWarehouse(${warehouse.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    }

    getMonthlySpent(warehouseName, month, year) {
        return this.transactions
            .filter(t => {
                const tDate = new Date(t.date);
                return t.warehouse === warehouseName &&
                       t.transactionType === 'وارد' &&
                       tDate.getMonth() === month &&
                       tDate.getFullYear() === year;
            })
            .reduce((sum, t) => {
                // تحويل إلى عملة المخزن باستخدام سعر الصرف وقت المعاملة
                const warehouse = this.warehouses.find(w => w.name === warehouseName);
                if (warehouse) {
                    if (warehouse.budgetCurrency === 'USD' && t.currency === 'IQD') {
                        const exchangeRateUsed = t.exchangeRateAtTime || this.exchangeRate;
                        return sum + (t.totalPrice / exchangeRateUsed);
                    } else if (warehouse.budgetCurrency === 'IQD' && t.currency === 'USD') {
                        const exchangeRateUsed = t.exchangeRateAtTime || this.exchangeRate;
                        return sum + (t.totalPrice * exchangeRateUsed);
                    }
                }
                return sum + t.totalPrice;
            }, 0);
    }

    getItemStatus(item) {
        const minStock = item.minStock || 10; // قيمة افتراضية إذا لم تكن محددة

        if (item.quantity <= 0) {
            return '<span class="badge badge-danger"><i class="fas fa-times"></i> نفد المخزون</span>';
        } else if (item.quantity < minStock) {
            return '<span class="badge badge-warning"><i class="fas fa-exclamation-triangle"></i> منخفض</span>';
        } else {
            return '<span class="badge badge-success"><i class="fas fa-check"></i> متوفر</span>';
        }
    }

    showAlert(message, type) {
        const alertContainer = document.getElementById('alertContainer');
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        
        alertContainer.innerHTML = `
            <div class="alert ${alertClass}">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
                ${message}
            </div>
        `;

        // إخفاء التنبيه بعد 5 ثوان
        setTimeout(() => {
            alertContainer.innerHTML = '';
        }, 5000);
    }

    loadWarehouses() {
        const warehouseSelect = document.getElementById('warehouse');
        const reportSelect = document.getElementById('warehouseReportSelect');

        // تحديث قائمة المخازن في النموذج
        const currentValue = warehouseSelect.value;
        warehouseSelect.innerHTML = `
            <option value="">اختر المخزن</option>
            ${this.warehouses.map(warehouse =>
                `<option value="${warehouse.name}">${warehouse.name}</option>`
            ).join('')}
            <option value="مخزن جديد">إضافة مخزن جديد...</option>
        `;
        warehouseSelect.value = currentValue;

        // تحديث قائمة المخازن في التقارير
        if (reportSelect) {
            reportSelect.innerHTML = `
                <option value="">اختر المخزن</option>
                ${this.warehouses.map(warehouse =>
                    `<option value="${warehouse.name}">${warehouse.name}</option>`
                ).join('')}
            `;
        }
    }

    updateWarehouseSelects() {
        const warehouseSelect = document.getElementById('warehouse');
        const reportSelect = document.getElementById('warehouseReportSelect');
        const warehouseAccountSelect = document.getElementById('warehouseAccountSelect');

        if (warehouseSelect) {
            warehouseSelect.innerHTML = `
                <option value="">اختر المخزن</option>
                ${this.warehouses.map(w => `<option value="${w.name}">${w.name}</option>`).join('')}
            `;
        }

        if (reportSelect) {
            reportSelect.innerHTML = `
                <option value="">اختر المخزن</option>
                ${this.warehouses.map(w => `<option value="${w.name}">${w.name}</option>`).join('')}
            `;
        }

        // تحديث قائمة كشوف الحساب (جميع المخازن)
        if (warehouseAccountSelect) {
            warehouseAccountSelect.innerHTML = `
                <option value="">اختر المخزن</option>
                ${this.warehouses.map(w => `<option value="${w.name}">${w.name}</option>`).join('')}
            `;
        }
    }

    clearForm() {
        document.getElementById('itemForm').reset();
        this.setCurrentDateTime();
        document.getElementById('newWarehouseGroup').style.display = 'none';
        document.getElementById('totalCost').value = '';

        // إعادة تطبيق منطق إظهار/إخفاء حقول الأسعار
        const transactionTypeField = document.getElementById('transactionType');
        if (transactionTypeField) {
            transactionTypeField.dispatchEvent(new Event('change'));
        }
    }

    clearWarehouseForm() {
        document.getElementById('warehouseForm').reset();
    }

    saveData() {
        localStorage.setItem('inventory_items', JSON.stringify(this.items));
        localStorage.setItem('inventory_transactions', JSON.stringify(this.transactions));
        localStorage.setItem('inventory_warehouses', JSON.stringify(this.warehouses));
        localStorage.setItem('inventory_settings', JSON.stringify(this.settings));
        localStorage.setItem('inventory_budget', JSON.stringify(this.budget));
    }

    // دوال إضافية للميزات المتقدمة
    exportData() {
        const data = {
            items: this.items,
            transactions: this.transactions,
            exportDate: new Date().toISOString(),
            version: '2.0'
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `inventory-backup-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }

    importData(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = JSON.parse(e.target.result);
                if (data.items && data.transactions) {
                    this.items = data.items;
                    this.transactions = data.transactions;
                    this.saveData();
                    this.updateStats();
                    this.renderInventoryTable();
                    this.showAlert('تم استيراد البيانات بنجاح!', 'success');
                }
            } catch (error) {
                this.showAlert('خطأ في ملف البيانات', 'danger');
            }
        };
        reader.readAsText(file);
    }

    generateReport() {
        const report = {
            totalItems: this.items.length,
            totalValue: this.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0),
            lowStockItems: this.items.filter(item => item.quantity <= item.minStock),
            recentTransactions: this.transactions.slice(-10),
            generatedAt: new Date().toLocaleString('ar-EG')
        };

        console.log('تقرير المخزون:', report);
        return report;
    }
}

// دوال التبويبات
function showTab(tabName) {
    // إخفاء جميع التبويبات
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    // إزالة التفعيل من جميع الأزرار
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // إظهار التبويب المحدد
    document.getElementById(tabName).classList.add('active');

    // تفعيل الزر المحدد
    event.target.classList.add('active');

    // تحديث البيانات حسب التبويب
    if (tabName === 'transactions') {
        inventoryManager.renderTransactionsTable();
    } else if (tabName === 'inventory') {
        inventoryManager.renderInventoryTable();
    } else if (tabName === 'warehouses') {
        inventoryManager.renderWarehousesTable();
    } else if (tabName === 'reports') {
        inventoryManager.loadWarehouses();
    }
}

// دوال عامة
function refreshData() {
    const btn = event.target;
    const originalText = btn.innerHTML;

    btn.innerHTML = '<div class="loading"></div> جاري التحديث...';
    btn.disabled = true;

    setTimeout(() => {
        inventoryManager.updateStats();
        inventoryManager.renderInventoryTable();
        inventoryManager.renderTransactionsTable();
        inventoryManager.renderWarehousesTable();

        btn.innerHTML = originalText;
        btn.disabled = false;

        inventoryManager.showAlert('تم تحديث البيانات بنجاح!', 'success');
    }, 1000);
}

function exportData() {
    inventoryManager.exportData();
}

// دوال التقارير
function generateDailyReport() {
    const date = document.getElementById('dailyReportDate').value;
    const filter = document.getElementById('dailyReportFilter').value;

    if (!date) {
        inventoryManager.showAlert('يرجى اختيار التاريخ', 'danger');
        return;
    }

    // تصفية المعاملات حسب التاريخ
    let transactions = inventoryManager.transactions.filter(t =>
        new Date(t.date).toDateString() === new Date(date).toDateString()
    );

    // تطبيق تصفية نوع المعاملة
    if (filter !== 'all') {
        transactions = transactions.filter(t => t.transactionType === filter);
    }

    // تحديد عنوان التقرير
    const filterText = filter === 'all' ? 'جميع المعاملات' : `معاملات ${filter}`;
    const reportTitle = `تقرير يومي - ${new Date(date).toLocaleDateString('ar-EG')} (${filterText})`;

    displayReport(reportTitle, transactions);
}

function generateWeeklyReport() {
    const week = document.getElementById('weeklyReportDate').value;
    const filter = document.getElementById('weeklyReportFilter').value;

    if (!week) {
        inventoryManager.showAlert('يرجى اختيار الأسبوع', 'danger');
        return;
    }

    const [year, weekNum] = week.split('-W');
    const startDate = new Date(year, 0, 1 + (weekNum - 1) * 7);
    const endDate = new Date(startDate.getTime() + 6 * 24 * 60 * 60 * 1000);

    // تصفية المعاملات حسب الأسبوع
    let transactions = inventoryManager.transactions.filter(t => {
        const tDate = new Date(t.date);
        return tDate >= startDate && tDate <= endDate;
    });

    // تطبيق تصفية نوع المعاملة
    if (filter !== 'all') {
        transactions = transactions.filter(t => t.transactionType === filter);
    }

    // تحديد عنوان التقرير
    const filterText = filter === 'all' ? 'جميع المعاملات' : `معاملات ${filter}`;
    const reportTitle = `تقرير أسبوعي - الأسبوع ${weekNum} من ${year} (${filterText})`;

    displayReport(reportTitle, transactions);
}

function generateMonthlyReport() {
    const month = document.getElementById('monthlyReportDate').value;
    const filter = document.getElementById('monthlyReportFilter').value;

    if (!month) {
        inventoryManager.showAlert('يرجى اختيار الشهر', 'danger');
        return;
    }

    const [year, monthNum] = month.split('-');

    // تصفية المعاملات حسب الشهر
    let transactions = inventoryManager.transactions.filter(t => {
        const tDate = new Date(t.date);
        return tDate.getFullYear() == year && tDate.getMonth() == monthNum - 1;
    });

    // تطبيق تصفية نوع المعاملة
    if (filter !== 'all') {
        transactions = transactions.filter(t => t.transactionType === filter);
    }

    // تحديد عنوان التقرير
    const filterText = filter === 'all' ? 'جميع المعاملات' : `معاملات ${filter}`;
    const reportTitle = `تقرير شهري - ${monthNum}/${year} (${filterText})`;

    displayReport(reportTitle, transactions);
}

function generateWarehouseReport() {
    const warehouse = document.getElementById('warehouseReportSelect').value;
    const filter = document.getElementById('warehouseReportFilter').value;

    if (!warehouse) {
        inventoryManager.showAlert('يرجى اختيار المخزن', 'danger');
        return;
    }

    // تصفية المعاملات حسب المخزن
    let transactions = inventoryManager.transactions.filter(t => t.warehouse === warehouse);

    // تطبيق تصفية نوع المعاملة
    if (filter !== 'all') {
        transactions = transactions.filter(t => t.transactionType === filter);
    }

    // تحديد عنوان التقرير
    const filterText = filter === 'all' ? 'جميع المعاملات' : `معاملات ${filter}`;
    const reportTitle = `تقرير المخزن - ${warehouse} (${filterText})`;

    displayReport(reportTitle, transactions);
}

// دوال طباعة التقارير
function printDailyReport() {
    try {
        const date = document.getElementById('dailyReportDate').value;
        if (!date) {
            inventoryManager.showAlert('يرجى اختيار التاريخ', 'danger');
            return;
        }

        const transactions = inventoryManager.transactions.filter(t =>
            new Date(t.date).toDateString() === new Date(date).toDateString()
        );

        const printContent = generateReportPrintContent(`تقرير يومي - ${new Date(date).toLocaleDateString('ar-EG')}`, transactions);
        printReport(printContent);
    } catch (error) {
        console.error('خطأ في طباعة التقرير:', error);
        inventoryManager.showAlert('خطأ في طباعة التقرير', 'danger');
    }
}

function printWeeklyReport() {
    const week = document.getElementById('weeklyReportDate').value;
    if (!week) {
        inventoryManager.showAlert('يرجى اختيار الأسبوع', 'danger');
        return;
    }

    const [year, weekNum] = week.split('-W');
    const startDate = new Date(year, 0, 1 + (weekNum - 1) * 7);
    const endDate = new Date(startDate.getTime() + 6 * 24 * 60 * 60 * 1000);

    const transactions = inventoryManager.transactions.filter(t => {
        const tDate = new Date(t.date);
        return tDate >= startDate && tDate <= endDate;
    });

    const printContent = generateReportPrintContent(`تقرير أسبوعي - الأسبوع ${weekNum} من ${year}`, transactions);
    printReport(printContent);
}

function printMonthlyReport() {
    const month = document.getElementById('monthlyReportDate').value;
    if (!month) {
        inventoryManager.showAlert('يرجى اختيار الشهر', 'danger');
        return;
    }

    const [year, monthNum] = month.split('-');
    const transactions = inventoryManager.transactions.filter(t => {
        const tDate = new Date(t.date);
        return tDate.getFullYear() == year && tDate.getMonth() == monthNum - 1;
    });

    const printContent = generateReportPrintContent(`تقرير شهري - ${monthNum}/${year}`, transactions);
    printReport(printContent);
}

function printWarehouseReport() {
    const warehouse = document.getElementById('warehouseReportSelect').value;
    if (!warehouse) {
        inventoryManager.showAlert('يرجى اختيار المخزن', 'danger');
        return;
    }

    const transactions = inventoryManager.transactions.filter(t => t.warehouse === warehouse);
    const printContent = generateReportPrintContent(`تقرير المخزن - ${warehouse}`, transactions);
    printReport(printContent);
}

function displayReport(title, transactions) {
    // تحديد نوع التقرير من العنوان
    let reportType = 'all';
    if (title.includes('وارد')) reportType = 'وارد';
    else if (title.includes('صادر')) reportType = 'صادر';

    // حساب الإجماليات بالدينار العراقي (باستخدام سعر الصرف وقت كل معاملة)
    const totalIn = transactions.filter(t => t.transactionType === 'وارد').reduce((sum, t) => {
        if (t.currency === 'USD') {
            const exchangeRateUsed = t.exchangeRateAtTime || inventoryManager.exchangeRate;
            return sum + (t.totalPrice * exchangeRateUsed);
        }
        return sum + t.totalPrice;
    }, 0);
    const totalOut = transactions.filter(t => t.transactionType === 'صادر').reduce((sum, t) => {
        if (t.currency === 'USD') {
            const exchangeRateUsed = t.exchangeRateAtTime || inventoryManager.exchangeRate;
            return sum + (t.totalPrice * exchangeRateUsed);
        }
        return sum + t.totalPrice;
    }, 0);
    const net = totalIn - totalOut;

    // تحديد عناوين الأعمدة حسب نوع التقرير
    let warehouseHeader, supplierReceiverHeader;

    if (reportType === 'صادر') {
        warehouseHeader = 'المخزن المصدر';
        supplierReceiverHeader = 'المستلم/الوجهة';
    } else if (reportType === 'وارد') {
        warehouseHeader = 'المخزن المستقبل';
        supplierReceiverHeader = 'المورد/المصدر';
    } else {
        warehouseHeader = 'المخزن';
        supplierReceiverHeader = 'المورد/المستلم';
    }

    const reportHtml = `
        <h3>${title}</h3>
        <div class="stats-grid" style="margin: 20px 0;">
            <div class="stat-card success">
                <div class="stat-number">${totalIn.toLocaleString('ar-EG')} د.ع</div>
                <div class="stat-label">إجمالي الوارد (المصروف)</div>
            </div>
            <div class="stat-card danger">
                <div class="stat-number">${totalOut.toLocaleString('ar-EG')} د.ع</div>
                <div class="stat-label">إجمالي الصادر</div>
            </div>
            <div class="stat-card ${net >= 0 ? 'primary' : 'warning'}">
                <div class="stat-number">${net.toLocaleString('ar-EG')} د.ع</div>
                <div class="stat-label">الصافي</div>
            </div>
            <div class="stat-card info">
                <div class="stat-number">${inventoryManager.exchangeRate.toLocaleString('ar-EG')}</div>
                <div class="stat-label">سعر الدولار (د.ع)</div>
            </div>
            <div class="stat-card primary">
                <div class="stat-number">${transactions.length}</div>
                <div class="stat-label">عدد المعاملات</div>
            </div>
        </div>
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>المادة</th>
                        <th>النوع</th>
                        <th>الكمية</th>
                        <th>سعر الوحدة</th>
                        <th>المجموع</th>
                        <th>${warehouseHeader}</th>
                        <th>${supplierReceiverHeader}</th>
                    </tr>
                </thead>
                <tbody>
                    ${transactions.map(t => {
                        let priceDisplay = '';
                        let totalDisplay = '';

                        if (t.transactionType === 'صادر' || t.transactionType === 'نقل') {
                            priceDisplay = '-';
                            totalDisplay = '-';
                        } else {
                            const currencySymbol = t.currency === 'USD' ? 'دولار' : 'د.ع';
                            priceDisplay = `${t.unitPrice.toLocaleString('ar-EG')} ${currencySymbol}`;

                            if (t.currency === 'USD') {
                                const exchangeRateUsed = t.exchangeRateAtTime || inventoryManager.exchangeRate;
                                const totalInIQD = t.totalPrice * exchangeRateUsed;
                                totalDisplay = `${t.totalPrice.toLocaleString('ar-EG')} دولار<br><small style="color: #7f8c8d;">(${totalInIQD.toLocaleString('ar-EG')} د.ع بسعر ${exchangeRateUsed})</small>`;
                            } else {
                                totalDisplay = `${t.totalPrice.toLocaleString('ar-EG')} د.ع`;
                            }
                        }

                        return `
                            <tr>
                                <td>${new Date(t.date).toLocaleDateString('ar-EG')}</td>
                                <td>${t.itemName}</td>
                                <td>${t.transactionType}</td>
                                <td>${t.quantity} ${t.unit}</td>
                                <td>${priceDisplay}</td>
                                <td>${totalDisplay}</td>
                                <td>${t.warehouse}</td>
                                <td>${t.transactionType === 'صادر' ?
                                    (t.receiver || '-') +
                                    (t.destinationWarehouse ? `<br><small>إلى: ${t.destinationWarehouse}</small>` : '') +
                                    (t.exportWarehouse ? `<br><small>تصدير: ${t.exportWarehouse}</small>` : '')
                                    : (t.supplier || '-')}</td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        </div>
        <button class="btn btn-success" onclick="exportReport('${title}', ${JSON.stringify(transactions).replace(/"/g, '&quot;')})">
            <i class="fas fa-download"></i> تصدير التقرير
        </button>
    `;

    document.getElementById('reportResults').innerHTML = reportHtml;
}

// دوال تصدير PDF للتقارير
function exportDailyReportPDF() {
    const date = document.getElementById('dailyReportDate').value;
    if (!date) {
        inventoryManager.showAlert('يرجى اختيار التاريخ', 'danger');
        return;
    }

    const transactions = inventoryManager.transactions.filter(t =>
        new Date(t.date).toDateString() === new Date(date).toDateString()
    );

    exportReportToPDF(`تقرير يومي - ${new Date(date).toLocaleDateString('ar-EG')}`, transactions);
}

function exportWeeklyReportPDF() {
    const week = document.getElementById('weeklyReportDate').value;
    if (!week) {
        inventoryManager.showAlert('يرجى اختيار الأسبوع', 'danger');
        return;
    }

    const [year, weekNum] = week.split('-W');
    const startDate = new Date(year, 0, 1 + (weekNum - 1) * 7);
    const endDate = new Date(startDate.getTime() + 6 * 24 * 60 * 60 * 1000);

    const transactions = inventoryManager.transactions.filter(t => {
        const tDate = new Date(t.date);
        return tDate >= startDate && tDate <= endDate;
    });

    exportReportToPDF(`تقرير أسبوعي - الأسبوع ${weekNum} من ${year}`, transactions);
}

function exportMonthlyReportPDF() {
    const month = document.getElementById('monthlyReportDate').value;
    if (!month) {
        inventoryManager.showAlert('يرجى اختيار الشهر', 'danger');
        return;
    }

    const [year, monthNum] = month.split('-');
    const transactions = inventoryManager.transactions.filter(t => {
        const tDate = new Date(t.date);
        return tDate.getFullYear() == year && tDate.getMonth() == monthNum - 1;
    });

    exportReportToPDF(`تقرير شهري - ${monthNum}/${year}`, transactions);
}

function exportWarehouseReportPDF() {
    const warehouse = document.getElementById('warehouseReportSelect').value;
    if (!warehouse) {
        inventoryManager.showAlert('يرجى اختيار المخزن', 'danger');
        return;
    }

    const transactions = inventoryManager.transactions.filter(t => t.warehouse === warehouse);
    exportReportToPDF(`تقرير المخزن - ${warehouse}`, transactions);
}

function exportReportToPDF(title, transactions) {
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF('p', 'mm', 'a4');

    // إضافة الرأس
    addPDFHeader(doc, title);

    // حساب الإحصائيات
    const totalIn = transactions.filter(t => t.transactionType === 'وارد').reduce((sum, t) => sum + t.totalPriceIQD, 0);
    const totalOut = transactions.filter(t => t.transactionType === 'صادر').reduce((sum, t) => sum + t.totalPriceIQD, 0);
    const net = totalIn - totalOut;

    // إضافة الإحصائيات
    doc.setFontSize(12);
    doc.text(`إجمالي الوارد: ${totalIn.toLocaleString('ar-EG')} د.ع`, 20, 70);
    doc.text(`إجمالي الصادر: ${totalOut.toLocaleString('ar-EG')} د.ع`, 20, 80);
    doc.text(`الصافي: ${net.toLocaleString('ar-EG')} د.ع`, 20, 90);
    doc.text(`عدد المعاملات: ${transactions.length}`, 20, 100);

    // إعداد البيانات للجدول
    const tableData = transactions.map(t => [
        new Date(t.date).toLocaleDateString('ar-EG'),
        t.itemName,
        t.transactionType,
        `${t.quantity} ${t.unit}`,
        `${t.totalPrice.toLocaleString('ar-EG')} ${t.currency === 'USD' ? 'دولار' : 'د.ع'}`,
        t.warehouse,
        t.supplier
    ]);

    // إضافة الجدول
    doc.autoTable({
        head: [['التاريخ', 'المادة', 'النوع', 'الكمية', 'القيمة', 'المخزن', 'المورد/المستلم']],
        body: tableData,
        startY: 110,
        styles: {
            font: 'helvetica',
            fontSize: 8,
            cellPadding: 3,
            halign: 'right'
        },
        headStyles: {
            fillColor: [52, 152, 219],
            textColor: 255,
            fontStyle: 'bold'
        },
        alternateRowStyles: {
            fillColor: [245, 245, 245]
        }
    });

    // إضافة التذييل
    addPDFFooter(doc);

    // حفظ الملف
    doc.save(`${title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`);
}

function clearAllData() {
    if (confirm('هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        localStorage.clear();
        location.reload();
    }
}

// تشغيل التطبيق
let inventoryManager;

document.addEventListener('DOMContentLoaded', () => {
    try {
        console.log('🚀 بدء تحميل النظام...');
        inventoryManager = new InventoryManager();
        console.log('✅ تم تحميل النظام بنجاح!');

        // إضافة بيانات تجريبية إذا كان المخزون فارغ
        if (inventoryManager.items.length === 0) {
            console.log('📝 إضافة بيانات تجريبية...');
            addSampleData();
        }

        console.log('🎉 النظام جاهز للاستخدام!');
    } catch (error) {
        console.error('❌ خطأ في تحميل النظام:', error);
        alert('خطأ في تحميل النظام: ' + error.message);
    }
});

// دوال الطباعة والتصدير الاحترافية

// طباعة المعاملات
function printTransactions() {
    const transactions = getFilteredTransactions();
    const printContent = generatePrintContent('سجل المعاملات', transactions, 'transactions');

    // إنشاء نافذة طباعة
    const printWindow = window.open('', '_blank', 'width=800,height=600');
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
}

// طباعة المخزون
function printInventory() {
    const items = getFilteredInventory();
    const printContent = generatePrintContent('تقرير المخزون', items, 'inventory');

    const printWindow = window.open('', '_blank', 'width=800,height=600');
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
}

// تصدير المعاملات PDF
function exportTransactionsPDF() {
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF('p', 'mm', 'a4');

    // إضافة الرأس
    addPDFHeaderSimple(doc, 'سجل المعاملات');

    // إعداد البيانات
    const transactions = getFilteredTransactions();
    const tableData = transactions.map(t => {
        const currencySymbol = t.currency === 'USD' ? 'USD' : 'IQD';
        return [
            new Date(t.date).toLocaleDateString('en-GB'),
            t.itemName,
            t.transactionType === 'وارد' ? 'In' : t.transactionType === 'صادر' ? 'Out' : 'Adjust',
            `${t.quantity} ${t.unit}`,
            `${t.unitPrice} ${currencySymbol}`,
            `${t.totalPrice} ${currencySymbol}`,
            t.warehouse,
            t.supplier
        ];
    });

    // إضافة الجدول
    doc.autoTable({
        head: [['Date', 'Item', 'Type', 'Qty', 'Price', 'Total', 'Warehouse', 'Supplier']],
        body: tableData,
        startY: 60,
        styles: {
            font: 'helvetica',
            fontSize: 8,
            cellPadding: 3,
            halign: 'center'
        },
        headStyles: {
            fillColor: [52, 152, 219],
            textColor: 255,
            fontStyle: 'bold'
        },
        alternateRowStyles: {
            fillColor: [245, 245, 245]
        }
    });

    // إضافة التذييل
    addPDFFooterSimple(doc);

    // حفظ الملف
    doc.save(`transactions_heiman_${new Date().toISOString().split('T')[0]}.pdf`);
}

// تصدير المخزون PDF
function exportInventoryPDF() {
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF('p', 'mm', 'a4');

    doc.setFont('helvetica');
    addPDFHeader(doc, 'تقرير المخزون');

    const items = getFilteredInventory();
    const tableData = items.map(item => [
        item.name,
        item.quantity.toLocaleString('ar-EG'),
        item.unit,
        `${item.unitPrice.toLocaleString('ar-EG')} د.ع`,
        `${(item.quantity * item.unitPrice).toLocaleString('ar-EG')} د.ع`,
        item.quantity <= item.minStock ? 'منخفض' : 'متوفر'
    ]);

    doc.autoTable({
        head: [['المادة', 'الكمية', 'الوحدة', 'السعر', 'القيمة الإجمالية', 'الحالة']],
        body: tableData,
        startY: 60,
        styles: {
            font: 'helvetica',
            fontSize: 9,
            cellPadding: 4
        },
        headStyles: {
            fillColor: [52, 152, 219],
            textColor: 255,
            fontStyle: 'bold'
        },
        alternateRowStyles: {
            fillColor: [245, 245, 245]
        }
    });

    addPDFFooter(doc);
    doc.save(`مخزون_هيمن_كروب_${new Date().toISOString().split('T')[0]}.pdf`);
}

// تصدير المعاملات Excel
function exportTransactionsExcel() {
    try {
        const transactions = inventoryManager.transactions.slice(-100);

        // إعداد البيانات
        const data = [
            ['نظام إدارة المخازن هيمن كروب'],
            ['سجل المعاملات'],
            [`تاريخ التصدير: ${new Date().toLocaleDateString('ar-EG')}`],
            [`سعر الصرف: ${inventoryManager.exchangeRate} د.ع`],
            [], // سطر فارغ
            ['التاريخ', 'المادة', 'النوع', 'الكمية', 'الوحدة', 'السعر', 'العملة', 'المجموع', 'المخزن', 'المورد/المستلم', 'رقم الفاتورة']
        ];

        transactions.forEach(t => {
            const currencySymbol = t.currency === 'USD' ? 'دولار' : 'د.ع';
            data.push([
                new Date(t.date).toLocaleDateString('ar-EG'),
                t.itemName,
                t.transactionType,
                t.quantity,
                t.unit,
                t.unitPrice,
                currencySymbol,
                t.totalPrice,
                t.warehouse,
                t.supplier,
                t.invoiceNumber || ''
            ]);
        });

        // إنشاء ملف Excel
        const ws = XLSX.utils.aoa_to_sheet(data);
        const wb = XLSX.utils.book_new();

        // تنسيق الرأس
        ws['A1'] = { v: 'نظام إدارة المخازن هيمن كروب', s: { font: { bold: true, sz: 16 } } };
        ws['A2'] = { v: 'سجل المعاملات', s: { font: { bold: true, sz: 14 } } };

        XLSX.utils.book_append_sheet(wb, ws, 'المعاملات');
        XLSX.writeFile(wb, `معاملات_هيمن_كروب_${new Date().toISOString().split('T')[0]}.xlsx`);

        inventoryManager.showAlert('تم تصدير المعاملات بنجاح!', 'success');
    } catch (error) {
        console.error('خطأ في تصدير Excel:', error);
        inventoryManager.showAlert('خطأ في تصدير Excel', 'danger');
    }
}

// تصدير المخزون Excel
function exportInventoryExcel() {
    const items = getFilteredInventory();

    const data = [
        ['نظام إدارة المخازن هيمن كروب'],
        ['تقرير المخزون'],
        [`تاريخ التصدير: ${new Date().toLocaleDateString('ar-EG')}`],
        [],
        ['المادة', 'الكمية الحالية', 'الوحدة', 'سعر الوحدة', 'القيمة الإجمالية', 'القيمة بالدينار', 'الحد الأدنى', 'الحالة', 'الفئة']
    ];

    items.forEach(item => {
        const totalValue = item.quantity * item.unitPrice;
        const currencySymbol = item.currency === 'USD' ? 'دولار' : 'د.ع';
        const totalValueIQD = item.currency === 'USD' ? totalValue * inventoryManager.exchangeRate : totalValue;

        data.push([
            item.name,
            item.quantity,
            item.unit,
            `${item.unitPrice} ${currencySymbol}`,
            `${totalValue} ${currencySymbol}`,
            item.currency === 'USD' ? `${totalValueIQD} د.ع` : '',
            item.minStock,
            item.quantity <= item.minStock ? 'منخفض' : 'متوفر',
            item.category
        ]);
    });

    const ws = XLSX.utils.aoa_to_sheet(data);
    const wb = XLSX.utils.book_new();

    ws['A1'] = { v: 'نظام إدارة المخازن هيمن كروب', s: { font: { bold: true, sz: 16 } } };
    ws['A2'] = { v: 'تقرير المخزون', s: { font: { bold: true, sz: 14 } } };

    XLSX.utils.book_append_sheet(wb, ws, 'المخزون');
    XLSX.writeFile(wb, `مخزون_هيمن_كروب_${new Date().toISOString().split('T')[0]}.xlsx`);
}

// دوال مساعدة للطباعة والتصدير

function getFilteredTransactions() {
    try {
        const fromDate = document.getElementById('filterDateFrom')?.value;
        const toDate = document.getElementById('filterDateTo')?.value;

        let transactions = inventoryManager.transactions || [];

        if (fromDate && toDate) {
            transactions = transactions.filter(t => {
                const tDate = new Date(t.date).toDateString();
                return tDate >= new Date(fromDate).toDateString() &&
                       tDate <= new Date(toDate).toDateString();
            });
        }

        return transactions.slice(-100); // آخر 100 معاملة
    } catch (error) {
        console.error('خطأ في تصفية المعاملات:', error);
        return inventoryManager.transactions || [];
    }
}

function getFilteredInventory() {
    try {
        const searchTerm = document.getElementById('searchInventory')?.value?.toLowerCase();

        let items = inventoryManager.items || [];

        if (searchTerm) {
            items = items.filter(item =>
                item.name.toLowerCase().includes(searchTerm) ||
                item.category.toLowerCase().includes(searchTerm) ||
                (item.location && item.location.toLowerCase().includes(searchTerm))
            );
        }

        return items;
    } catch (error) {
        console.error('خطأ في تصفية المخزون:', error);
        return inventoryManager.items || [];
    }
}

function generatePrintContent(title, data, type) {
    const companyName = 'هيمن كروب';
    const currentDate = new Date().toLocaleDateString('ar-EG');
    const currentTime = new Date().toLocaleTimeString('ar-EG');

    let tableHeaders = '';
    let tableRows = '';

    if (type === 'transactions') {
        tableHeaders = `
            <th>التاريخ</th>
            <th>المادة</th>
            <th>النوع</th>
            <th>الكمية</th>
            <th>السعر</th>
            <th>المجموع</th>
            <th>المشروع</th>
            <th>المورد/المستلم</th>
        `;

        tableRows = data.map(t => `
            <tr>
                <td>${new Date(t.date).toLocaleDateString('ar-EG')}</td>
                <td>${t.itemName}</td>
                <td>${t.transactionType}</td>
                <td>${t.quantity} ${t.unit}</td>
                <td>${t.unitPrice.toLocaleString('ar-EG')} د.ع</td>
                <td>${t.totalPrice.toLocaleString('ar-EG')} د.ع</td>
                <td>${t.project}</td>
                <td>${t.supplier}</td>
            </tr>
        `).join('');
    } else if (type === 'inventory') {
        tableHeaders = `
            <th>المادة</th>
            <th>الكمية</th>
            <th>الوحدة</th>
            <th>السعر</th>
            <th>القيمة الإجمالية</th>
            <th>الحالة</th>
        `;

        tableRows = data.map(item => `
            <tr>
                <td>${item.name}</td>
                <td>${item.quantity.toLocaleString('ar-EG')}</td>
                <td>${item.unit}</td>
                <td>${item.unitPrice.toLocaleString('ar-EG')} د.ع</td>
                <td>${(item.quantity * item.unitPrice).toLocaleString('ar-EG')} د.ع</td>
                <td>${item.quantity <= item.minStock ? 'منخفض' : 'متوفر'}</td>
            </tr>
        `).join('');
    }

    return `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>${title} - ${companyName}</title>
            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
            <style>
                body {
                    font-family: 'Cairo', sans-serif;
                    margin: 0;
                    padding: 20px;
                    background: white;
                    color: black;
                    direction: rtl;
                }
                .print-header {
                    text-align: center;
                    margin-bottom: 30px;
                    border-bottom: 3px solid #3498db;
                    padding-bottom: 20px;
                }
                .print-logo {
                    width: 80px;
                    height: 80px;
                    margin: 0 auto 15px;
                    border-radius: 10px;
                    background: #3498db;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-weight: bold;
                    font-size: 12px;
                }
                .print-title {
                    font-size: 28px;
                    font-weight: bold;
                    margin: 15px 0;
                    color: #2c3e50;
                }
                .print-company {
                    font-size: 20px;
                    color: #3498db;
                    font-weight: 600;
                }
                .print-info {
                    display: flex;
                    justify-content: space-between;
                    margin: 20px 0;
                    font-size: 14px;
                    color: #666;
                }
                .print-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-top: 20px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .print-table th,
                .print-table td {
                    border: 1px solid #ddd;
                    padding: 12px 8px;
                    text-align: right;
                    font-size: 13px;
                }
                .print-table th {
                    background: #3498db;
                    color: white;
                    font-weight: bold;
                }
                .print-table tr:nth-child(even) {
                    background: #f8f9fa;
                }
                .print-table tr:hover {
                    background: #e3f2fd;
                }
                .print-footer {
                    margin-top: 40px;
                    text-align: center;
                    font-size: 12px;
                    color: #666;
                    border-top: 1px solid #ddd;
                    padding-top: 20px;
                }
                @media print {
                    body { margin: 0; }
                    .print-table tr:hover { background: inherit; }
                    /* إخفاء URL من أسفل الصفحة */
                    @page {
                        margin: 0.5in;
                        @bottom-left { content: ""; }
                        @bottom-center { content: ""; }
                        @bottom-right { content: ""; }
                        @top-left { content: ""; }
                        @top-center { content: ""; }
                        @top-right { content: ""; }
                    }
                }
            </style>
        </head>
        <body>
            <div class="print-header">
                <div class="print-logo">
                    HEIMAN<br>GROUP
                </div>
                <div class="print-title">${title}</div>
                <div class="print-company">${companyName}</div>
            </div>

            <div class="print-info">
                <div>تاريخ الطباعة: ${currentDate}</div>
                <div>وقت الطباعة: ${currentTime}</div>
                <div>عدد السجلات: ${data.length}</div>
            </div>

            <table class="print-table">
                <thead>
                    <tr>${tableHeaders}</tr>
                </thead>
                <tbody>
                    ${tableRows}
                </tbody>
            </table>

            <div class="print-footer">
                <p><strong>نظام إدارة المخازن هيمن كروب</strong></p>
                <p>تم إنشاء هذا التقرير تلقائياً في ${currentDate} الساعة ${currentTime}</p>
            </div>
        </body>
        </html>
    `;
}

function addPDFHeader(doc, title) {
    // إضافة لوجو (مربع أزرق بسيط)
    doc.setFillColor(52, 152, 219);
    doc.rect(85, 10, 40, 20, 'F');
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(12);
    doc.text('HEIMAN GROUP', 105, 22, { align: 'center' });

    // إضافة العنوان
    doc.setTextColor(0, 0, 0);
    doc.setFontSize(18);
    doc.text(title, 105, 40, { align: 'center' });

    // إضافة اسم الشركة
    doc.setFontSize(14);
    doc.setTextColor(52, 152, 219);
    doc.text('هيمن كروب', 105, 50, { align: 'center' });
}

function addPDFHeaderSimple(doc, title) {
    // إضافة لوجو (مربع أزرق بسيط)
    doc.setFillColor(52, 152, 219);
    doc.rect(85, 10, 40, 20, 'F');
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(12);
    doc.text('HEIMAN GROUP', 105, 22, { align: 'center' });

    // إضافة العنوان
    doc.setTextColor(0, 0, 0);
    doc.setFontSize(18);
    doc.text(title, 105, 40, { align: 'center' });

    // إضافة اسم الشركة
    doc.setFontSize(14);
    doc.setTextColor(52, 152, 219);
    doc.text('Heiman Group', 105, 50, { align: 'center' });
}

function addPDFFooterSimple(doc) {
    const pageCount = doc.internal.getNumberOfPages();
    const currentDate = new Date().toLocaleDateString('en-GB');

    for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setFontSize(10);
        doc.setTextColor(128, 128, 128);
        doc.text(`Page ${i} of ${pageCount}`, 105, 285, { align: 'center' });
        doc.text(`Generated: ${currentDate}`, 20, 285);
        doc.text('Heiman Group Inventory System', 190, 285, { align: 'right' });
    }
}

function addPDFFooter(doc) {
    const pageCount = doc.internal.getNumberOfPages();
    const currentDate = new Date().toLocaleDateString('en-GB');

    for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setFontSize(10);
        doc.setTextColor(128, 128, 128);
        doc.text(`Page ${i} of ${pageCount}`, 105, 285, { align: 'center' });
        doc.text(`Generated: ${currentDate}`, 20, 285);
        doc.text('Heiman Group Inventory System', 190, 285, { align: 'right' });
    }
}

function exportAllData() {
    const data = {
        items: inventoryManager.items,
        transactions: inventoryManager.transactions,
        projects: inventoryManager.projects,
        settings: inventoryManager.settings,
        exportDate: new Date().toISOString(),
        version: '2.0'
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `heiman_group_backup_${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
}

function filterTransactions() {
    const fromDate = document.getElementById('filterDateFrom').value;
    const toDate = document.getElementById('filterDateTo').value;

    if (!fromDate || !toDate) {
        inventoryManager.showAlert('يرجى اختيار تاريخ البداية والنهاية', 'danger');
        return;
    }

    const filtered = inventoryManager.transactions.filter(t => {
        const tDate = new Date(t.date).toDateString();
        return tDate >= new Date(fromDate).toDateString() &&
               tDate <= new Date(toDate).toDateString();
    });

    // عرض النتائج المفلترة
    const tbody = document.getElementById('transactionsTableBody');
    if (filtered.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="10" style="text-align: center; color: #7f8c8d;">
                    لا توجد معاملات في الفترة المحددة
                </td>
            </tr>
        `;
    } else {
        tbody.innerHTML = filtered.map(transaction => {
            const date = new Date(transaction.date);
            const typeColor = transaction.transactionType === 'وارد' ? '#2ecc71' :
                             transaction.transactionType === 'صادر' ? '#e74c3c' : '#f39c12';

            return `
                <tr>
                    <td>
                        <small>${date.toLocaleDateString('ar-EG')}</small><br>
                        <small style="color: #7f8c8d;">${date.toLocaleTimeString('ar-EG', {hour: '2-digit', minute: '2-digit'})}</small>
                    </td>
                    <td><strong>${transaction.itemName}</strong></td>
                    <td>
                        <span class="badge" style="background: ${typeColor}; color: white;">
                            ${transaction.transactionType}
                        </span>
                    </td>
                    <td>${transaction.quantity.toLocaleString('ar-EG')} ${transaction.unit}</td>
                    <td>${transaction.unitPrice.toLocaleString('ar-EG')} د.ع</td>
                    <td><strong>${transaction.totalPrice.toLocaleString('ar-EG')} د.ع</strong></td>
                    <td>${transaction.project}</td>
                    <td>${transaction.supplier}</td>
                    <td>${transaction.invoiceNumber || '-'}</td>
                    <td>
                        <button class="action-btn delete" onclick="deleteTransaction(${transaction.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    }
}

function searchInventory() {
    const searchTerm = document.getElementById('searchInventory').value.toLowerCase();
    if (!searchTerm) {
        inventoryManager.renderInventoryTable();
        return;
    }

    const filtered = inventoryManager.items.filter(item =>
        item.name.toLowerCase().includes(searchTerm) ||
        item.category.toLowerCase().includes(searchTerm) ||
        (item.location && item.location.toLowerCase().includes(searchTerm))
    );

    // عرض النتائج المفلترة
    const tbody = document.getElementById('inventoryTableBodyDetailed');
    if (filtered.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" style="text-align: center; color: #7f8c8d;">
                    لا توجد نتائج للبحث عن "${searchTerm}"
                </td>
            </tr>
        `;
    } else {
        tbody.innerHTML = filtered.map(item => {
            const totalValue = item.quantity * item.unitPrice;
            const status = inventoryManager.getItemStatus(item);
            const lastTransaction = item.lastTransaction ?
                `${item.lastTransaction.type} - ${new Date(item.lastTransaction.date).toLocaleDateString('ar-EG')}` :
                'لا توجد حركة';

            const currencySymbol = item.currency === 'USD' ? 'دولار' : 'د.ع';
            const totalValueDisplay = item.currency === 'USD' ?
                `${totalValue.toLocaleString('ar-EG', {minimumFractionDigits: 2, maximumFractionDigits: 2})} دولار<br><small style="color: #7f8c8d;">(${(totalValue * inventoryManager.exchangeRate).toLocaleString('ar-EG')} د.ع)</small>` :
                `${totalValue.toLocaleString('ar-EG', {minimumFractionDigits: 2, maximumFractionDigits: 2})} د.ع`;

            return `
                <tr>
                    <td>
                        <strong>${item.name}</strong>
                        <br><small style="color: #7f8c8d;">${item.category} - ${item.location || 'غير محدد'}</small>
                    </td>
                    <td>
                        <span style="font-weight: 600; font-size: 1.1rem; ${item.quantity <= item.minStock ? 'color: #e74c3c;' : ''}">
                            ${item.quantity.toLocaleString('ar-EG')}
                        </span>
                        <br><small style="color: #7f8c8d;">الحد الأدنى: ${item.minStock}</small>
                    </td>
                    <td>${item.unit}</td>
                    <td>
                        ${item.unitPrice.toLocaleString('ar-EG', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        })} ${currencySymbol}
                    </td>
                    <td>
                        <strong>
                            ${totalValueDisplay}
                        </strong>
                    </td>
                    <td>
                        <small style="color: #7f8c8d;">${lastTransaction}</small>
                    </td>
                    <td>${status}</td>
                    <td>
                        <button class="action-btn edit" onclick="editItem(${item.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete" onclick="deleteItem(${item.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    }
}

function saveCompanyInfo() {
    inventoryManager.settings.companyName = document.getElementById('companyName').value;
    inventoryManager.settings.companyAddress = document.getElementById('companyAddress').value;
    inventoryManager.settings.companyPhone = document.getElementById('companyPhone').value;

    // حفظ سعر الصرف الجديد
    const newExchangeRate = parseFloat(document.getElementById('exchangeRate').value);
    if (newExchangeRate && newExchangeRate > 0) {
        inventoryManager.settings.exchangeRate = newExchangeRate;
        inventoryManager.exchangeRate = newExchangeRate;

        // تحديث العرض في الصفحة الرئيسية
        document.getElementById('exchangeRateDisplay').textContent = newExchangeRate.toLocaleString('ar-EG');

        // تحديث جميع الجداول لإظهار التحويلات الجديدة
        inventoryManager.updateStats();
        inventoryManager.renderInventoryTable();
        inventoryManager.renderTransactionsTable();
    }

    inventoryManager.saveData();
    inventoryManager.showAlert('تم حفظ معلومات الشركة وسعر الصرف بنجاح!', 'success');
}

// دوال مودال سعر الصرف
function showExchangeRateModal() {
    try {
        const modal = document.getElementById('exchangeRateModal');
        const input = document.getElementById('newExchangeRate');

        if (!modal) {
            alert('خطأ: نافذة سعر الصرف غير موجودة');
            return;
        }

        if (!input) {
            alert('خطأ: حقل سعر الصرف غير موجود');
            return;
        }

        if (!inventoryManager) {
            alert('خطأ: النظام غير محمل');
            return;
        }

        input.value = inventoryManager.exchangeRate;
        modal.style.display = 'block';
        input.focus();

    } catch (error) {
        console.error('خطأ في فتح نافذة سعر الصرف:', error);
        alert('خطأ في فتح نافذة سعر الصرف');
    }
}

function closeExchangeRateModal() {
    try {
        const modal = document.getElementById('exchangeRateModal');
        if (modal) {
            modal.style.display = 'none';
        }
    } catch (error) {
        console.error('خطأ في إغلاق نافذة سعر الصرف:', error);
    }
}

function updateExchangeRate() {
    try {
        const newRateInput = document.getElementById('newExchangeRate');
        if (!newRateInput) {
            alert('خطأ: حقل سعر الصرف غير موجود');
            return;
        }

        const newRate = parseFloat(newRateInput.value);

        if (!newRate || newRate <= 0) {
            alert('يرجى إدخال سعر صحيح أكبر من صفر');
            newRateInput.focus();
            return;
        }

        if (!inventoryManager) {
            alert('خطأ: النظام غير محمل');
            return;
        }

        // تحديث سعر الصرف
        inventoryManager.exchangeRate = newRate;
        if (inventoryManager.settings) {
            inventoryManager.settings.exchangeRate = newRate;
        }

        // تحديث العرض في الواجهة
        const displayElement = document.getElementById('exchangeRateDisplay');
        if (displayElement) {
            displayElement.textContent = newRate.toLocaleString('ar-EG');
        }

        // تحديث الحقول الأخرى إن وجدت
        const exchangeRateField = document.getElementById('exchangeRate');
        const currentExchangeRateField = document.getElementById('currentExchangeRate');
        if (exchangeRateField) exchangeRateField.value = newRate;
        if (currentExchangeRateField) currentExchangeRateField.value = newRate;

        // تحديث جميع الجداول والإحصائيات
        inventoryManager.updateStats();
        inventoryManager.renderInventoryTable();
        inventoryManager.renderTransactionsTable();
        inventoryManager.saveData();

        // إغلاق النافذة وإظهار رسالة نجاح
        closeExchangeRateModal();
        inventoryManager.showAlert(`تم تحديث سعر الصرف إلى ${newRate.toLocaleString('ar-EG')} دينار للدولار الواحد`, 'success');

    } catch (error) {
        console.error('خطأ في تحديث سعر الصرف:', error);
        alert('خطأ في تحديث سعر الصرف: ' + error.message);
    }
}

// دوال الحذف والتعديل
function deleteTransaction(id) {
    if (confirm('هل أنت متأكد من حذف هذه المعاملة؟\n\nملاحظة: سيتم حذف المعاملة من السجلات فقط، ولن يتم تعديل كميات المخزون.')) {
        // حذف المعاملة من السجلات فقط
        inventoryManager.transactions = inventoryManager.transactions.filter(t => t.id !== id);
        inventoryManager.saveData();
        inventoryManager.renderTransactionsTable();
        inventoryManager.updateStats();
        inventoryManager.showAlert('تم حذف المعاملة من السجلات بنجاح!', 'success');
    }
}

// دالة لعكس تأثير المعاملة على المخزون (للاستخدام المتقدم)
function reverseTransactionEffect(id) {
    if (confirm('هل أنت متأكد من عكس تأثير هذه المعاملة على المخزون؟\n\nتحذير: هذا سيؤثر على كميات المخزون الحالية.')) {
        const transaction = inventoryManager.transactions.find(t => t.id === id);

        if (!transaction) {
            inventoryManager.showAlert('المعاملة غير موجودة!', 'danger');
            return;
        }

        // العثور على المادة
        const item = inventoryManager.items.find(i =>
            i.name === transaction.itemName && i.warehouse === transaction.warehouse
        );

        if (item) {
            // عكس تأثير المعاملة
            if (transaction.transactionType === 'وارد') {
                item.quantity -= transaction.quantity;
            } else if (transaction.transactionType === 'صادر') {
                item.quantity += transaction.quantity;
            } else if (transaction.transactionType === 'نقل') {
                item.quantity += transaction.quantity;
            }

            // التأكد من عدم وجود كميات سالبة
            if (item.quantity < 0) {
                item.quantity = 0;
            }

            item.lastUpdated = new Date().toISOString();
        }

        // حذف المعاملة
        inventoryManager.transactions = inventoryManager.transactions.filter(t => t.id !== id);
        inventoryManager.saveData();
        inventoryManager.updateStats();
        inventoryManager.renderTransactionsTable();
        inventoryManager.renderInventoryTable();

        inventoryManager.showAlert('تم عكس تأثير المعاملة وحذفها من السجلات!', 'success');
    }
}



function deleteItem(id) {
    if (confirm('هل أنت متأكد من حذف هذه المادة؟')) {
        inventoryManager.items = inventoryManager.items.filter(i => i.id !== id);
        inventoryManager.saveData();
        inventoryManager.renderInventoryTable();
        inventoryManager.updateStats();
        inventoryManager.showAlert('تم حذف المادة بنجاح!', 'success');
    }
}

function deleteWarehouse(id) {
    if (confirm('هل أنت متأكد من حذف هذا المخزن؟')) {
        inventoryManager.warehouses = inventoryManager.warehouses.filter(w => w.id !== id);
        inventoryManager.saveData();
        inventoryManager.renderWarehousesTable();
        inventoryManager.loadWarehouses();
        inventoryManager.showAlert('تم حذف المخزن بنجاح!', 'success');
    }
}

function fixWarehouseTypes() {
    if (confirm('هل تريد مسح جميع المخازن الموجودة؟ ستحتاج لإضافة مخازن جديدة بنفسك.')) {
        console.log('مسح جميع المخازن...');

        // مسح جميع المخازن الموجودة
        inventoryManager.warehouses = [];

        console.log('تم مسح جميع المخازن');

        // حفظ البيانات الفارغة
        inventoryManager.saveData();

        // إعادة تحميل العرض
        setTimeout(() => {
            inventoryManager.renderWarehousesTable();
            inventoryManager.updateWarehouseSelects();

            inventoryManager.showAlert('تم مسح جميع المخازن. يمكنك الآن إضافة مخازن جديدة.', 'success');
        }, 200);
    }
}



function addWarehouse() {
    const name = document.getElementById('warehouseName').value.trim();
    const type = document.getElementById('warehouseType').value;
    const location = document.getElementById('warehouseLocation').value.trim();
    const manager = document.getElementById('warehouseManager').value.trim();
    const budget = parseFloat(document.getElementById('monthlyBudget').value) || 0;
    const budgetCurrency = document.getElementById('budgetCurrency').value;

    console.log('إضافة مخزن جديد:');
    console.log('الاسم:', name);
    console.log('النوع المختار:', type);
    console.log('الموقع:', location);

    if (!name || !type) {
        inventoryManager.showAlert('يرجى إدخال اسم المخزن ونوعه', 'danger');
        return;
    }

    // التحقق من عدم تكرار الاسم
    if (inventoryManager.warehouses.find(w => w.name === name)) {
        inventoryManager.showAlert('اسم المخزن موجود مسبقاً', 'danger');
        return;
    }

    const warehouse = {
        id: Date.now(),
        name: name,
        type: type,
        location: location,
        manager: manager,
        monthlyBudget: budget,
        budgetCurrency: budgetCurrency,
        currentSpent: 0,
        status: 'نشط',
        createdAt: new Date().toISOString()
    };

    console.log('المخزن الذي سيتم حفظه:', warehouse);

    inventoryManager.warehouses.push(warehouse);
    inventoryManager.saveData();

    console.log('المخازن بعد الحفظ:', inventoryManager.warehouses);

    inventoryManager.renderWarehousesTable();
    inventoryManager.updateWarehouseSelects();

    // إعادة تعيين النموذج
    document.getElementById('warehouseForm').reset();
    inventoryManager.showAlert('تم إضافة المخزن بنجاح!', 'success');
}

function editWarehouse(id) {
    inventoryManager.showAlert('ميزة التعديل قيد التطوير', 'warning');
}

function generateWarehouseAccount() {
    const warehouseName = document.getElementById('warehouseAccountSelect').value;

    if (!warehouseName) {
        inventoryManager.showAlert('يرجى اختيار المخزن', 'danger');
        return;
    }

    // جلب جميع المعاملات المتعلقة بهذا المخزن
    const transactions = inventoryManager.transactions.filter(t =>
        t.warehouse === warehouseName ||
        t.sourceWarehouse === warehouseName ||
        t.targetWarehouse === warehouseName
    );

    // تصنيف المعاملات
    const incoming = transactions.filter(t =>
        (t.transactionType === 'سحب' && t.targetWarehouse === warehouseName) ||
        (t.transactionType === 'تحويل' && t.targetWarehouse === warehouseName)
    );

    const outgoing = transactions.filter(t =>
        (t.transactionType === 'إرجاع' && t.sourceWarehouse === warehouseName) ||
        (t.transactionType === 'تحويل' && t.sourceWarehouse === warehouseName) ||
        (t.transactionType === 'تالف' && t.warehouse === warehouseName)
    );

    // حساب الأرصدة
    const totalIncoming = incoming.reduce((sum, t) => sum + t.quantity, 0);
    const totalOutgoing = outgoing.reduce((sum, t) => sum + t.quantity, 0);
    const balance = totalIncoming - totalOutgoing;

    // عرض النتائج
    const resultsDiv = document.getElementById('warehouseAccountResults');
    resultsDiv.innerHTML = `
        <h3>كشف حساب: ${warehouseName}</h3>
        <div class="stats-grid" style="margin: 20px 0;">
            <div class="stat-card success">
                <div class="stat-number">${totalIncoming}</div>
                <div class="stat-label">إجمالي الوارد</div>
            </div>
            <div class="stat-card danger">
                <div class="stat-number">${totalOutgoing}</div>
                <div class="stat-label">إجمالي الصادر</div>
            </div>
            <div class="stat-card primary">
                <div class="stat-number">${balance}</div>
                <div class="stat-label">الرصيد الحالي</div>
            </div>
        </div>
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>المادة</th>
                        <th>نوع العملية</th>
                        <th>الكمية</th>
                        <th>من/إلى</th>
                    </tr>
                </thead>
                <tbody>
                    ${transactions.map(t => `
                        <tr>
                            <td>${new Date(t.date).toLocaleDateString('ar-EG')}</td>
                            <td>${t.itemName}</td>
                            <td>
                                <span class="badge" style="background: ${getTransactionColor(t.transactionType)}; color: white;">
                                    ${t.transactionType}
                                </span>
                            </td>
                            <td>${t.quantity} ${t.unit}</td>
                            <td>${getTransactionSource(t, warehouseName)}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
}

function getTransactionColor(type) {
    switch (type) {
        case 'سحب': return '#2ecc71';
        case 'إرجاع': return '#3498db';
        case 'تحويل': return '#f39c12';
        case 'تالف': return '#e74c3c';
        default: return '#7f8c8d';
    }
}

function getTransactionSource(transaction, currentWarehouse) {
    switch (transaction.transactionType) {
        case 'سحب':
            return 'من المخزن الرئيسي';
        case 'إرجاع':
            return 'إلى المخزن الرئيسي';
        case 'تحويل':
            if (transaction.sourceWarehouse === currentWarehouse) {
                return `إلى ${transaction.targetWarehouse}`;
            } else {
                return `من ${transaction.sourceWarehouse}`;
            }
        case 'تالف':
            return 'تالف في الموقع';
        default:
            return '-';
    }
}

// دالة عرض تقرير الميزانية الشهرية
function showMonthlyBudgetReport() {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    const monthName = new Date().toLocaleDateString('ar-EG', { month: 'long', year: 'numeric' });

    let reportHtml = `
        <h3>تقرير الميزانية الشهرية - ${monthName}</h3>
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>المخزن</th>
                        <th>الميزانية المخصصة</th>
                        <th>المصروف</th>
                        <th>المتبقي</th>
                        <th>النسبة المستخدمة</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
    `;

    inventoryManager.warehouses.forEach(warehouse => {
        const monthlySpent = inventoryManager.getMonthlySpent(warehouse.name, currentMonth, currentYear);
        const remaining = warehouse.monthlyBudget - monthlySpent;
        const percentage = warehouse.monthlyBudget > 0 ? (monthlySpent / warehouse.monthlyBudget * 100) : 0;
        const statusColor = percentage > 90 ? '#e74c3c' : percentage > 70 ? '#f39c12' : '#2ecc71';
        const currencySymbol = warehouse.budgetCurrency === 'USD' ? 'دولار' : 'د.ع';

        reportHtml += `
            <tr>
                <td><strong>${warehouse.name}</strong></td>
                <td>${warehouse.monthlyBudget.toLocaleString('ar-EG')} ${currencySymbol}</td>
                <td style="color: #e74c3c;">${monthlySpent.toLocaleString('ar-EG')} ${currencySymbol}</td>
                <td style="color: ${remaining >= 0 ? '#2ecc71' : '#e74c3c'};">
                    ${remaining.toLocaleString('ar-EG')} ${currencySymbol}
                </td>
                <td>${percentage.toFixed(1)}%</td>
                <td>
                    <span class="badge" style="background: ${statusColor}; color: white;">
                        ${percentage > 90 ? 'تحذير' : percentage > 70 ? 'تنبيه' : 'طبيعي'}
                    </span>
                </td>
            </tr>
        `;
    });

    reportHtml += `
                </tbody>
            </table>
        </div>
        <div style="margin-top: 20px; display: flex; gap: 10px;">
            <button class="btn btn-success" onclick="printMonthlyBudgetReport()">
                <i class="fas fa-print"></i> طباعة التقرير
            </button>
            <button class="btn btn-danger" onclick="exportMonthlyBudgetReportPDF()">
                <i class="fas fa-file-pdf"></i> تصدير PDF
            </button>
        </div>
    `;

    document.getElementById('reportResults').innerHTML = reportHtml;
}

// دوال مساعدة للطباعة
function generateReportPrintContent(title, transactions) {
    // حساب المجموع بالدولار والدينار منفصلين
    let totalUSD = 0;
    let totalIQD = 0;

    transactions.filter(t => t.transactionType === 'وارد').forEach(t => {
        if (t.currency === 'USD') {
            totalUSD += (t.totalPrice || 0);
        } else {
            totalIQD += (t.totalPrice || 0);
        }
    });

    // المجموع الإجمالي بالدينار
    const totalInIQD = totalIQD + (totalUSD * inventoryManager.exchangeRate);

    const tableRows = transactions.map(t => {
        const currencySymbol = t.currency === 'USD' ? 'دولار' : 'د.ع';
        let totalDisplay = '';
        if (t.currency === 'USD') {
            const exchangeRateUsed = t.exchangeRateAtTime || inventoryManager.exchangeRate;
            const totalInIQD = t.totalPrice * exchangeRateUsed;
            totalDisplay = `${t.totalPrice.toLocaleString('ar-EG')} دولار<br><small>(${totalInIQD.toLocaleString('ar-EG')} د.ع بسعر ${exchangeRateUsed})</small>`;
        } else {
            totalDisplay = `${t.totalPrice.toLocaleString('ar-EG')} د.ع`;
        }

        return `
            <tr>
                <td>${new Date(t.date).toLocaleDateString('ar-EG')}</td>
                <td>${t.itemName}</td>
                <td>${t.transactionType}</td>
                <td>${t.quantity} ${t.unit}</td>
                <td>${t.unitPrice.toLocaleString('ar-EG')} ${currencySymbol}</td>
                <td>${totalDisplay}</td>
                <td>${t.warehouse}</td>
                <td>${t.supplier}</td>
            </tr>
        `;
    }).join('');

    return generatePrintTemplate(title, tableRows, {
        totalIQD: totalIQD.toLocaleString('ar-EG'),
        totalUSD: totalUSD.toLocaleString('ar-EG'),
        count: transactions.length,
        exchangeRate: inventoryManager.exchangeRate
    });
}

function generatePrintTemplate(title, tableRows, stats) {
    const companyName = 'هيمن كروب';
    const currentDate = new Date().toLocaleDateString('ar-EG');
    const currentTime = new Date().toLocaleTimeString('ar-EG');

    return `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>${title} - ${companyName}</title>
            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
            <style>
                body {
                    font-family: 'Cairo', sans-serif;
                    margin: 0;
                    padding: 20px;
                    background: white;
                    color: black;
                    direction: rtl;
                }
                .print-header {
                    text-align: center;
                    margin-bottom: 30px;
                    border-bottom: 3px solid #3498db;
                    padding-bottom: 20px;
                }
                .print-logo {
                    width: 80px;
                    height: 80px;
                    margin: 0 auto 15px;
                    border-radius: 10px;
                    background: #3498db;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-weight: bold;
                    font-size: 12px;
                }
                .print-title {
                    font-size: 28px;
                    font-weight: bold;
                    margin: 15px 0;
                    color: #2c3e50;
                }
                .print-company {
                    font-size: 20px;
                    color: #3498db;
                    font-weight: 600;
                }
                .print-info {
                    display: flex;
                    justify-content: space-between;
                    margin: 20px 0;
                    font-size: 14px;
                    color: #666;
                }
                .stats-section {
                    background: #f8f9fa;
                    padding: 15px;
                    border-radius: 8px;
                    margin: 15px 0;
                    display: grid;
                    grid-template-columns: 1fr 1fr 0.8fr;
                    gap: 10px;
                }
                .stat-item {
                    text-align: center;
                    padding: 10px;
                    background: white;
                    border-radius: 8px;
                    border-left: 4px solid #3498db;
                }
                .stat-value {
                    font-size: 18px;
                    font-weight: bold;
                    color: #2c3e50;
                }
                .stat-label {
                    font-size: 12px;
                    color: #666;
                    margin-top: 5px;
                }
                .print-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-top: 20px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .print-table th,
                .print-table td {
                    border: 1px solid #ddd;
                    padding: 12px 8px;
                    text-align: right;
                    font-size: 13px;
                }
                .print-table th {
                    background: #3498db;
                    color: white;
                    font-weight: bold;
                }
                .print-table tr:nth-child(even) {
                    background: #f8f9fa;
                }
                .print-footer {
                    margin-top: 40px;
                    text-align: center;
                    font-size: 12px;
                    color: #666;
                    border-top: 1px solid #ddd;
                    padding-top: 20px;
                }
                @media print {
                    body { margin: 0; }
                    /* إخفاء URL من أسفل الصفحة */
                    @page {
                        margin: 0.5in;
                        @bottom-left { content: ""; }
                        @bottom-center { content: ""; }
                        @bottom-right { content: ""; }
                        @top-left { content: ""; }
                        @top-center { content: ""; }
                        @top-right { content: ""; }
                    }
                }
            </style>
        </head>
        <body>
            <div class="print-header">
                <div class="print-logo">
                    HEIMAN<br>GROUP
                </div>
                <div class="print-title">${title}</div>
            </div>

            <div class="print-info">
                <div>تاريخ ووقت الطباعة: ${currentDate} - ${currentTime}</div>
                <div>عدد السجلات: ${stats.count}</div>
                <div>سعر صرف الدولار: ${stats.exchangeRate || inventoryManager.exchangeRate} د.ع</div>
            </div>

            <div class="stats-section">
                <div class="stat-item">
                    <div class="stat-value">${stats.totalIQD} د.ع</div>
                    <div class="stat-label">المجموع دينار</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${stats.totalUSD} دولار</div>
                    <div class="stat-label">المجموع دولار</div>
                </div>
                <div class="stat-item" style="font-size: 0.85em;">
                    <div class="stat-value">${stats.exchangeRate}</div>
                    <div class="stat-label">سعر الصرف</div>
                </div>
            </div>

            <table class="print-table">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>المادة</th>
                        <th>النوع</th>
                        <th>الكمية</th>
                        <th>سعر الوحدة</th>
                        <th>المجموع</th>
                        <th>المخزن</th>
                        <th>المورد/المستلم</th>
                    </tr>
                </thead>
                <tbody>
                    ${tableRows}
                </tbody>
            </table>

            <div class="print-footer">
                <p><strong>نظام إدارة المخازن</strong></p>
            </div>
        </body>
        </html>
    `;
}

function printReport(content) {
    try {
        const printWindow = window.open('', '_blank', 'width=800,height=600');
        if (!printWindow) {
            // إذا فشل فتح النافذة، استخدم الطباعة المباشرة
            const printDiv = document.createElement('div');
            printDiv.innerHTML = content;
            printDiv.style.display = 'none';
            document.body.appendChild(printDiv);

            const originalContent = document.body.innerHTML;
            document.body.innerHTML = content;
            window.print();
            document.body.innerHTML = originalContent;
            document.body.removeChild(printDiv);
            return;
        }

        printWindow.document.open();
        printWindow.document.write(content);
        printWindow.document.close();

        // انتظار تحميل المحتوى قبل الطباعة
        printWindow.onload = function() {
            printWindow.focus();
            printWindow.print();
            setTimeout(() => {
                printWindow.close();
            }, 1000);
        };
    } catch (error) {
        console.error('خطأ في الطباعة:', error);
        inventoryManager.showAlert('خطأ في الطباعة. جرب مرة أخرى.', 'danger');
    }
}

function editItem(id) {
    inventoryManager.showAlert('ميزة التعديل قيد التطوير', 'warning');
}

function editProject(id) {
    inventoryManager.showAlert('ميزة التعديل قيد التطوير', 'warning');
}

// إعداد تغيير الحقول حسب نوع المعاملة
function setupTransactionTypeFields() {
    const transactionTypeSelect = document.getElementById('transactionType');
    const supplierGroup = document.getElementById('supplierGroup');
    const receiverGroup = document.getElementById('receiverGroup');
    const destinationWarehouseGroup = document.getElementById('destinationWarehouseGroup');

    function updateFields() {
        const transactionType = transactionTypeSelect.value;
        const sourceWarehouseGroup = document.getElementById('sourceWarehouseGroup');
        const targetWarehouseGroup = document.getElementById('targetWarehouseGroup');

        // إخفاء جميع الحقول أولاً
        supplierGroup.style.display = 'none';
        receiverGroup.style.display = 'none';
        destinationWarehouseGroup.style.display = 'none';
        if (sourceWarehouseGroup) sourceWarehouseGroup.style.display = 'none';
        if (targetWarehouseGroup) targetWarehouseGroup.style.display = 'none';

        // إعادة تعيين required
        document.getElementById('supplier').required = false;
        document.getElementById('receiver').required = false;

        switch (transactionType) {
            case 'وارد':
                supplierGroup.style.display = 'block';
                document.getElementById('supplier').required = true;
                break;

            case 'صادر':
                receiverGroup.style.display = 'block';
                document.getElementById('receiver').required = true;
                break;

            case 'سحب':
                // سحب: المخزن المستهدف (فرعي)
                if (targetWarehouseGroup) targetWarehouseGroup.style.display = 'block';
                updateWarehouseOptions('سحب');
                break;

            case 'إرجاع':
                // إرجاع: المخزن المصدر (فرعي)
                if (sourceWarehouseGroup) sourceWarehouseGroup.style.display = 'block';
                updateWarehouseOptions('إرجاع');
                break;

            case 'تحويل':
                // تحويل: المخزن المصدر والمستهدف (فرعي)
                if (sourceWarehouseGroup) sourceWarehouseGroup.style.display = 'block';
                if (targetWarehouseGroup) targetWarehouseGroup.style.display = 'block';
                updateWarehouseOptions('تحويل');
                break;

            case 'تالف':
                // تالف: لا حاجة لحقول إضافية
                break;
        }

        // تحديث عناوين الجدول
        updateTableHeaders(transactionType);
    }

    function updateWarehouseOptions(operationType) {
        const sourceSelect = document.getElementById('sourceWarehouse');
        const targetSelect = document.getElementById('targetWarehouse');

        if (!inventoryManager || !inventoryManager.warehouses) return;

        const mainWarehouses = inventoryManager.warehouses.filter(w => w.type === 'مخزن رئيسي');
        const branchWarehouses = inventoryManager.warehouses.filter(w => w.type === 'مخزن فرعي');

        switch (operationType) {
            case 'صادر':
                // صادر: مخزن الوجهة (فرعي)
                const destinationSelect = document.getElementById('destinationWarehouse');
                if (destinationSelect) {
                    destinationSelect.innerHTML = '<option value="">اختر المخزن الفرعي</option>' +
                        branchWarehouses.map(w => `<option value="${w.name}">${w.name}</option>`).join('');
                }
                break;

            case 'سحب':
                // سحب: من رئيسي إلى فرعي
                if (targetSelect) {
                    targetSelect.innerHTML = '<option value="">اختر المخزن الفرعي</option>' +
                        branchWarehouses.map(w => `<option value="${w.name}">${w.name}</option>`).join('');
                }
                break;

            case 'إرجاع':
                // إرجاع: من فرعي إلى رئيسي
                if (sourceSelect) {
                    sourceSelect.innerHTML = '<option value="">اختر المخزن الفرعي</option>' +
                        branchWarehouses.map(w => `<option value="${w.name}">${w.name}</option>`).join('');
                }
                break;

            case 'تحويل':
                // تحويل: من فرعي إلى فرعي
                if (sourceSelect) {
                    sourceSelect.innerHTML = '<option value="">اختر المخزن المصدر</option>' +
                        branchWarehouses.map(w => `<option value="${w.name}">${w.name}</option>`).join('');
                }
                if (targetSelect) {
                    targetSelect.innerHTML = '<option value="">اختر المخزن المستهدف</option>' +
                        branchWarehouses.map(w => `<option value="${w.name}">${w.name}</option>`).join('');
                }
                break;
        }
    }

    function updateTableHeaders(transactionType) {
        // تحديث عناوين جدول المعاملات
        const supplierReceiverHeader = document.querySelector('#transactions table thead tr th:nth-child(6)');

        if (supplierReceiverHeader) {
            if (transactionType === 'صادر') {
                supplierReceiverHeader.innerHTML = 'المستلم/الوجهة';
            } else if (transactionType === 'وارد') {
                supplierReceiverHeader.innerHTML = 'المورد/المصدر';
            } else {
                supplierReceiverHeader.innerHTML = 'المورد/المستلم';
            }
        }

        // تحديث عناوين أخرى حسب الحاجة
        const warehouseHeader = document.querySelector('#transactions table thead tr th:nth-child(5)');
        if (warehouseHeader) {
            if (transactionType === 'صادر') {
                warehouseHeader.innerHTML = 'المخزن المصدر';
            } else if (transactionType === 'وارد') {
                warehouseHeader.innerHTML = 'المخزن المستقبل';
            } else {
                warehouseHeader.innerHTML = 'المخزن';
            }
        }
    }

    // تطبيق التغيير عند تحميل الصفحة
    updateFields();

    // تطبيق التغيير عند تغيير نوع المعاملة
    transactionTypeSelect.addEventListener('change', updateFields);

    // تحديث عناوين الجدول عند تحميل الصفحة
    updateTableHeaders('all');
}

function addSampleData() {
    const sampleItems = [
        {
            id: 1,
            name: 'أسمنت',
            quantity: 100,
            unit: 'كيس',
            unitPrice: 8500,
            currency: 'IQD',
            minStock: 20,
            category: 'مواد البناء',
            warehouse: 'المخزن الرئيسي',
            location: 'المخزن الرئيسي - الرف A1',
            created: new Date().toISOString(),
            lastUpdated: new Date().toISOString(),
            lastTransaction: {
                type: 'وارد',
                quantity: 100,
                date: new Date().toISOString(),
                warehouse: 'المخزن الرئيسي'
            }
        },
        {
            id: 2,
            name: 'حديد تسليح',
            quantity: 50,
            unit: 'طن',
            unitPrice: 850000,
            currency: 'IQD',
            minStock: 10,
            category: 'مواد البناء',
            warehouse: 'المخزن الرئيسي',
            location: 'المخزن الرئيسي - الرف B2',
            created: new Date().toISOString(),
            lastUpdated: new Date().toISOString(),
            lastTransaction: {
                type: 'وارد',
                quantity: 50,
                date: new Date().toISOString(),
                warehouse: 'المخزن الرئيسي'
            }
        },
        {
            id: 3,
            name: 'مولد كهربائي',
            quantity: 3,
            unit: 'قطعة',
            unitPrice: 800,
            currency: 'USD',
            minStock: 2,
            category: 'معدات كهربائية',
            warehouse: 'مخزن فرعي 1',
            location: 'مخزن فرعي 1 - المنطقة A',
            created: new Date().toISOString(),
            lastUpdated: new Date().toISOString(),
            lastTransaction: {
                type: 'وارد',
                quantity: 5,
                date: new Date().toISOString(),
                warehouse: 'مخزن فرعي 1'
            }
        },
        {
            id: 4,
            name: 'كابل كهربائي',
            quantity: 1,
            unit: 'متر',
            unitPrice: 2500,
            currency: 'IQD',
            minStock: 50,
            category: 'معدات كهربائية',
            warehouse: 'المخزن الرئيسي',
            location: 'المخزن الرئيسي - الرف C1',
            created: new Date().toISOString(),
            lastUpdated: new Date().toISOString(),
            lastTransaction: {
                type: 'صادر',
                quantity: 49,
                date: new Date().toISOString(),
                warehouse: 'المخزن الرئيسي'
            }
        }
    ];

    // إضافة معاملات تجريبية
    const sampleTransactions = [
        {
            id: 1,
            itemName: 'أسمنت',
            quantity: 100,
            unit: 'كيس',
            transactionType: 'وارد',
            unitPrice: 8500,
            currency: 'IQD',
            totalPrice: 850000,
            totalPriceIQD: 850000,
            warehouse: 'المخزن الرئيسي',
            supplier: 'شركة الأسمنت العراقية',
            invoiceNumber: 'INV-001',
            notes: 'دفعة جديدة من الأسمنت',
            date: new Date().toISOString(),
            timestamp: new Date().toLocaleString('ar-EG'),
            user: 'المستخدم الحالي'
        },
        {
            id: 2,
            itemName: 'مولد كهربائي',
            quantity: 5,
            unit: 'قطعة',
            transactionType: 'وارد',
            unitPrice: 800,
            currency: 'USD',
            totalPrice: 4000,
            totalPriceIQD: 6000000,
            exchangeRateAtTime: 1500, // سعر الصرف وقت المعاملة
            warehouse: 'مخزن فرعي 1',
            supplier: 'شركة المولدات الأمريكية',
            invoiceNumber: 'INV-002',
            notes: 'مولدات عالية الجودة',
            date: new Date().toISOString(),
            timestamp: new Date().toLocaleString('ar-EG'),
            user: 'المستخدم الحالي'
        }
    ];

    inventoryManager.items = sampleItems;
    inventoryManager.transactions = sampleTransactions;
    inventoryManager.saveData();
    inventoryManager.updateStats();
    inventoryManager.renderInventoryTable();
    inventoryManager.renderTransactionsTable();
    inventoryManager.showAlert('تم إضافة البيانات التجريبية بنجاح!', 'success');
}

// إصلاح دوال الطباعة
function printTransactions() {
    try {
        const transactions = inventoryManager.transactions.slice(-50);
        const printContent = generateReportPrintContent('سجل المعاملات', transactions);
        printReport(printContent);
    } catch (error) {
        console.error('خطأ في طباعة المعاملات:', error);
        inventoryManager.showAlert('خطأ في طباعة المعاملات', 'danger');
    }
}

function printInventory() {
    try {
        const items = inventoryManager.items;
        const printContent = generateInventoryPrintContent('تقرير المخزون', items);
        printReport(printContent);
    } catch (error) {
        console.error('خطأ في طباعة المخزون:', error);
        inventoryManager.showAlert('خطأ في طباعة المخزون', 'danger');
    }
}

function generateInventoryPrintContent(title, items) {
    let totalUSD = 0;
    let totalIQD = 0;

    items.forEach(item => {
        const itemValue = item.quantity * (item.unitPrice || 0);
        if (item.currency === 'USD') {
            totalUSD += itemValue;
        } else {
            totalIQD += itemValue;
        }
    });

    const tableRows = items.map(item => {
        const currencySymbol = (item.currency === 'USD') ? 'دولار' : 'د.ع';
        const itemValue = item.quantity * (item.unitPrice || 0);
        let totalDisplay = '';
        if (item.currency === 'USD') {
            const valueInIQD = itemValue * inventoryManager.exchangeRate;
            totalDisplay = `${itemValue.toLocaleString('ar-EG')} دولار<br><small>(${valueInIQD.toLocaleString('ar-EG')} د.ع)</small>`;
        } else {
            totalDisplay = `${itemValue.toLocaleString('ar-EG')} د.ع`;
        }

        return `
            <tr>
                <td>${item.name}</td>
                <td>${item.quantity.toLocaleString('ar-EG')} ${item.unit}</td>
                <td>${(item.unitPrice || 0).toLocaleString('ar-EG')} ${currencySymbol}</td>
                <td>${totalDisplay}</td>
                <td>${item.quantity <= (item.minStock || 0) ? 'منخفض' : 'متوفر'}</td>
                <td>${item.warehouse || 'غير محدد'}</td>
            </tr>
        `;
    }).join('');

    return generatePrintTemplate(title, tableRows, {
        totalIQD: totalIQD.toLocaleString('ar-EG'),
        totalUSD: totalUSD.toLocaleString('ar-EG'),
        count: items.length,
        exchangeRate: inventoryManager.exchangeRate
    });
}

// تشغيل النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    window.inventoryManager = new InventoryManager();

    // إعداد تغيير الحقول حسب نوع المعاملة
    setupTransactionTypeFields();


});
