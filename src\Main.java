import gui.InventoryGUI;
import javax.swing.*;

/**
 * الفئة الرئيسية لتشغيل نظام إدارة المخزون
 */
public class Main {
    public static void main(String[] args) {
        // تعيين Look and Feel للنظام (تم تعطيله لتجنب مشاكل التوافق)
        
        // تشغيل التطبيق في Event Dispatch Thread
        SwingUtilities.invokeLater(() -> {
            try {
                InventoryGUI gui = new InventoryGUI();
                gui.setVisible(true);
            } catch (Exception e) {
                e.printStackTrace();
                JOptionPane.showMessageDialog(null, 
                    "خطأ في تشغيل التطبيق: " + e.getMessage(), 
                    "خطأ", JOptionPane.ERROR_MESSAGE);
            }
        });
    }
}
