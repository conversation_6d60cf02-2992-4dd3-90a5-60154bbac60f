package com.inventory.database;

import com.inventory.models.*;
import com.inventory.utils.PasswordUtils;
import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * مدير قاعدة البيانات المحسن - نظام إدارة المخزون الاحترافي
 */
public class DatabaseManager {
    private static final String DB_URL = "*************************************";
    private static DatabaseManager instance;
    private Connection connection;
    
    private DatabaseManager() {
        try {
            Class.forName("org.sqlite.JDBC");
            connection = DriverManager.getConnection(DB_URL);
            connection.setAutoCommit(false); // للمعاملات
            createTables();
            createDefaultAdmin();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    public static synchronized DatabaseManager getInstance() {
        if (instance == null) {
            instance = new DatabaseManager();
        }
        return instance;
    }
    
    private void createTables() {
        try {
            Statement stmt = connection.createStatement();
            
            // جدول المستخدمين
            String usersTable = """
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT NOT NULL UNIQUE,
                    password TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    email TEXT,
                    role TEXT NOT NULL,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TEXT NOT NULL,
                    last_login TEXT
                )
                """;
            stmt.execute(usersTable);
            
            // جدول الفئات
            String categoriesTable = """
                CREATE TABLE IF NOT EXISTS categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    description TEXT,
                    color TEXT DEFAULT '#2196F3',
                    is_active BOOLEAN DEFAULT 1
                )
                """;
            stmt.execute(categoriesTable);
            
            // جدول المواد المحسن
            String itemsTable = """
                CREATE TABLE IF NOT EXISTS items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    barcode TEXT UNIQUE,
                    unit TEXT NOT NULL,
                    category_id INTEGER,
                    current_stock REAL DEFAULT 0,
                    min_stock REAL DEFAULT 0,
                    max_stock REAL DEFAULT 0,
                    unit_price REAL DEFAULT 0,
                    last_purchase_price REAL DEFAULT 0,
                    location TEXT,
                    image_url TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    notes TEXT,
                    FOREIGN KEY (category_id) REFERENCES categories (id)
                )
                """;
            stmt.execute(itemsTable);
            
            // جدول المعاملات المحسن
            String transactionsTable = """
                CREATE TABLE IF NOT EXISTS transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    item_id INTEGER NOT NULL,
                    quantity REAL NOT NULL,
                    transaction_type TEXT NOT NULL,
                    unit_price REAL DEFAULT 0,
                    total_price REAL DEFAULT 0,
                    project TEXT,
                    supplier_or_receiver TEXT,
                    reference_number TEXT,
                    invoice_number TEXT,
                    transaction_date TEXT NOT NULL,
                    user_id INTEGER NOT NULL,
                    notes TEXT,
                    status TEXT DEFAULT 'PENDING',
                    created_at TEXT NOT NULL,
                    FOREIGN KEY (item_id) REFERENCES items (id),
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
                """;
            stmt.execute(transactionsTable);
            
            // جدول سجل النشاطات
            String activityLogTable = """
                CREATE TABLE IF NOT EXISTS activity_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    action TEXT NOT NULL,
                    table_name TEXT NOT NULL,
                    record_id INTEGER,
                    old_values TEXT,
                    new_values TEXT,
                    timestamp TEXT NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
                """;
            stmt.execute(activityLogTable);
            
            // إنشاء الفهارس للأداء
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_items_name ON items(name)");
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_items_barcode ON items(barcode)");
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_items_category ON items(category_id)");
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_transactions_item ON transactions(item_id)");
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date)");
            stmt.execute("CREATE INDEX IF NOT EXISTS idx_transactions_user ON transactions(user_id)");
            
            connection.commit();
        } catch (SQLException e) {
            try {
                connection.rollback();
            } catch (SQLException ex) {
                ex.printStackTrace();
            }
            e.printStackTrace();
        }
    }
    
    private void createDefaultAdmin() {
        try {
            // التحقق من وجود مدير افتراضي
            String checkSql = "SELECT COUNT(*) FROM users WHERE role = 'ADMIN'";
            PreparedStatement checkStmt = connection.prepareStatement(checkSql);
            ResultSet rs = checkStmt.executeQuery();
            
            if (rs.next() && rs.getInt(1) == 0) {
                // إنشاء مدير افتراضي
                String insertSql = "INSERT INTO users (username, password, full_name, email, role, created_at) VALUES (?, ?, ?, ?, ?, ?)";
                PreparedStatement pstmt = connection.prepareStatement(insertSql);
                pstmt.setString(1, "admin");
                pstmt.setString(2, PasswordUtils.hashPassword("admin123"));
                pstmt.setString(3, "مدير النظام");
                pstmt.setString(4, "<EMAIL>");
                pstmt.setString(5, "ADMIN");
                pstmt.setString(6, LocalDateTime.now().toString());
                
                pstmt.executeUpdate();
                connection.commit();
                
                System.out.println("تم إنشاء المدير الافتراضي:");
                System.out.println("اسم المستخدم: admin");
                System.out.println("كلمة المرور: admin123");
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
    
    // إدارة المستخدمين
    public User authenticateUser(String username, String password) {
        String sql = "SELECT * FROM users WHERE username = ? AND is_active = 1";
        try {
            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, username);
            ResultSet rs = pstmt.executeQuery();
            
            if (rs.next()) {
                String hashedPassword = rs.getString("password");
                if (PasswordUtils.verifyPassword(password, hashedPassword)) {
                    User user = new User();
                    user.setId(rs.getInt("id"));
                    user.setUsername(rs.getString("username"));
                    user.setFullName(rs.getString("full_name"));
                    user.setEmail(rs.getString("email"));
                    user.setRole(User.UserRole.valueOf(rs.getString("role")));
                    user.setActive(rs.getBoolean("is_active"));
                    user.setCreatedAt(LocalDateTime.parse(rs.getString("created_at")));
                    
                    String lastLoginStr = rs.getString("last_login");
                    if (lastLoginStr != null) {
                        user.setLastLogin(LocalDateTime.parse(lastLoginStr));
                    }
                    
                    // تحديث آخر تسجيل دخول
                    updateLastLogin(user.getId());
                    
                    return user;
                }
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }
    
    private void updateLastLogin(int userId) {
        String sql = "UPDATE users SET last_login = ? WHERE id = ?";
        try {
            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, LocalDateTime.now().toString());
            pstmt.setInt(2, userId);
            pstmt.executeUpdate();
            connection.commit();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
    
    // إدارة الفئات
    public boolean addCategory(Category category) {
        String sql = "INSERT INTO categories (name, description, color) VALUES (?, ?, ?)";
        try {
            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, category.getName());
            pstmt.setString(2, category.getDescription());
            pstmt.setString(3, category.getColor());
            
            boolean success = pstmt.executeUpdate() > 0;
            if (success) {
                connection.commit();
            }
            return success;
        } catch (SQLException e) {
            try {
                connection.rollback();
            } catch (SQLException ex) {
                ex.printStackTrace();
            }
            e.printStackTrace();
            return false;
        }
    }
    
    public List<Category> getAllCategories() {
        List<Category> categories = new ArrayList<>();
        String sql = "SELECT * FROM categories WHERE is_active = 1 ORDER BY name";
        
        try {
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            
            while (rs.next()) {
                Category category = new Category();
                category.setId(rs.getInt("id"));
                category.setName(rs.getString("name"));
                category.setDescription(rs.getString("description"));
                category.setColor(rs.getString("color"));
                category.setActive(rs.getBoolean("is_active"));
                
                categories.add(category);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return categories;
    }
    
    // إدارة المواد المحسنة
    public boolean addItem(Item item) {
        String sql = """
            INSERT INTO items (name, description, barcode, unit, category_id, 
                             min_stock, max_stock, unit_price, location, 
                             created_at, updated_at, notes) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;
        try {
            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, item.getName());
            pstmt.setString(2, item.getDescription());
            pstmt.setString(3, item.getBarcode());
            pstmt.setString(4, item.getUnit());
            pstmt.setInt(5, item.getCategoryId());
            pstmt.setDouble(6, item.getMinStock());
            pstmt.setDouble(7, item.getMaxStock());
            pstmt.setDouble(8, item.getUnitPrice());
            pstmt.setString(9, item.getLocation());
            pstmt.setString(10, item.getCreatedAt().toString());
            pstmt.setString(11, item.getUpdatedAt().toString());
            pstmt.setString(12, item.getNotes());
            
            boolean success = pstmt.executeUpdate() > 0;
            if (success) {
                connection.commit();
            }
            return success;
        } catch (SQLException e) {
            try {
                connection.rollback();
            } catch (SQLException ex) {
                ex.printStackTrace();
            }
            e.printStackTrace();
            return false;
        }
    }
    
    public List<Item> getAllItems() {
        List<Item> items = new ArrayList<>();
        String sql = """
            SELECT i.*, c.name as category_name, c.color as category_color
            FROM items i 
            LEFT JOIN categories c ON i.category_id = c.id 
            WHERE i.is_active = 1 
            ORDER BY i.name
            """;
        
        try {
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            
            while (rs.next()) {
                Item item = createItemFromResultSet(rs);
                items.add(item);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return items;
    }
    
    private Item createItemFromResultSet(ResultSet rs) throws SQLException {
        Item item = new Item();
        item.setId(rs.getInt("id"));
        item.setName(rs.getString("name"));
        item.setDescription(rs.getString("description"));
        item.setBarcode(rs.getString("barcode"));
        item.setUnit(rs.getString("unit"));
        item.setCategoryId(rs.getInt("category_id"));
        item.setCurrentStock(rs.getDouble("current_stock"));
        item.setMinStock(rs.getDouble("min_stock"));
        item.setMaxStock(rs.getDouble("max_stock"));
        item.setUnitPrice(rs.getDouble("unit_price"));
        item.setLastPurchasePrice(rs.getDouble("last_purchase_price"));
        item.setLocation(rs.getString("location"));
        item.setImageUrl(rs.getString("image_url"));
        item.setActive(rs.getBoolean("is_active"));
        item.setCreatedAt(LocalDateTime.parse(rs.getString("created_at")));
        item.setUpdatedAt(LocalDateTime.parse(rs.getString("updated_at")));
        item.setNotes(rs.getString("notes"));
        
        // إضافة معلومات الفئة إذا كانت متوفرة
        String categoryName = rs.getString("category_name");
        if (categoryName != null) {
            Category category = new Category();
            category.setId(rs.getInt("category_id"));
            category.setName(categoryName);
            category.setColor(rs.getString("category_color"));
            item.setCategory(category);
        }
        
        return item;
    }
    
    // إغلاق الاتصال
    public void closeConnection() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
}
