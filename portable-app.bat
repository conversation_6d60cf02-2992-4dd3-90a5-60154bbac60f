@echo off
title نظام إدارة المخزون - هيمن كروب (محمول)
color 0A

echo.
echo ========================================
echo    نظام إدارة المخزون - هيمن كروب
echo           النسخة المحمولة
echo ========================================
echo.

REM إنشاء مجلد التطبيق المحمول
if not exist "portable-app" mkdir "portable-app"

echo إنشاء التطبيق المحمول...

REM نسخ الملفات الأساسية
copy "index-professional.html" "portable-app\" >nul 2>&1
if exist "assets" xcopy "assets" "portable-app\assets\" /E /I /Q >nul 2>&1

REM إنشاء ملف تشغيل للتطبيق المحمول
echo @echo off > "portable-app\تشغيل النظام.bat"
echo title نظام إدارة المخزون - هيمن كروب >> "portable-app\تشغيل النظام.bat"
echo echo. >> "portable-app\تشغيل النظام.bat"
echo echo تشغيل نظام إدارة المخزون... >> "portable-app\تشغيل النظام.bat"
echo echo. >> "portable-app\تشغيل النظام.bat"
echo start "" "index-professional.html" >> "portable-app\تشغيل النظام.bat"
echo echo تم تشغيل النظام في المتصفح >> "portable-app\تشغيل النظام.bat"
echo echo يمكنك إغلاق هذه النافذة بأمان >> "portable-app\تشغيل النظام.bat"
echo timeout /t 3 ^>nul >> "portable-app\تشغيل النظام.bat"

REM إنشاء ملف معلومات
echo نظام إدارة المخزون - هيمن كروب > "portable-app\اقرأني.txt"
echo ================================== >> "portable-app\اقرأني.txt"
echo. >> "portable-app\اقرأني.txt"
echo هذا تطبيق محمول لا يحتاج تثبيت >> "portable-app\اقرأني.txt"
echo. >> "portable-app\اقرأني.txt"
echo للتشغيل: >> "portable-app\اقرأني.txt"
echo انقر نقراً مزدوجاً على "تشغيل النظام.bat" >> "portable-app\اقرأني.txt"
echo. >> "portable-app\اقرأني.txt"
echo الميزات: >> "portable-app\اقرأني.txt"
echo - إدارة مخزون شاملة >> "portable-app\اقرأني.txt"
echo - نظام مالي متقدم >> "portable-app\اقرأني.txt"
echo - تقارير وطباعة احترافية >> "portable-app\اقرأني.txt"
echo - لا يحتاج إنترنت >> "portable-app\اقرأني.txt"
echo. >> "portable-app\اقرأني.txt"
echo هيمن كروب - 2024 >> "portable-app\اقرأني.txt"

echo ✅ تم إنشاء التطبيق المحمول!
echo.
echo 📁 المجلد: portable-app\
echo 🚀 للتشغيل: portable-app\تشغيل النظام.bat
echo.

echo فتح مجلد التطبيق المحمول...
start explorer "portable-app"

echo.
echo 🎉 التطبيق المحمول جاهز!
echo يمكنك نسخ مجلد "portable-app" إلى أي مكان
echo.
pause
