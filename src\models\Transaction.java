package models;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * نموذج المعاملة (وارد/صادر)
 */
public class Transaction {
    private int id;
    private String itemName;
    private double quantity;
    private String unit;
    private String transactionType; // "وارد" أو "صادر"
    private LocalDateTime date;
    private String project;
    private String supplierOrReceiver; // المورد أو المستلم
    private double unitPrice;
    private double totalPrice;
    private String notes;
    
    // Constructor
    public Transaction() {
        this.date = LocalDateTime.now();
    }
    
    public Transaction(String itemName, double quantity, String unit, String transactionType,
                      String project, String supplierOrReceiver, double unitPrice, String notes) {
        this.itemName = itemName;
        this.quantity = quantity;
        this.unit = unit;
        this.transactionType = transactionType;
        this.date = LocalDateTime.now();
        this.project = project;
        this.supplierOrReceiver = supplierOrReceiver;
        this.unitPrice = unitPrice;
        this.totalPrice = quantity * unitPrice;
        this.notes = notes;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getItemName() {
        return itemName;
    }
    
    public void setItemName(String itemName) {
        this.itemName = itemName;
    }
    
    public double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(double quantity) {
        this.quantity = quantity;
        this.totalPrice = quantity * unitPrice;
    }
    
    public String getUnit() {
        return unit;
    }
    
    public void setUnit(String unit) {
        this.unit = unit;
    }
    
    public String getTransactionType() {
        return transactionType;
    }
    
    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }
    
    public LocalDateTime getDate() {
        return date;
    }
    
    public void setDate(LocalDateTime date) {
        this.date = date;
    }
    
    public String getFormattedDate() {
        return date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
    }
    
    public String getProject() {
        return project;
    }
    
    public void setProject(String project) {
        this.project = project;
    }
    
    public String getSupplierOrReceiver() {
        return supplierOrReceiver;
    }
    
    public void setSupplierOrReceiver(String supplierOrReceiver) {
        this.supplierOrReceiver = supplierOrReceiver;
    }
    
    public double getUnitPrice() {
        return unitPrice;
    }
    
    public void setUnitPrice(double unitPrice) {
        this.unitPrice = unitPrice;
        this.totalPrice = quantity * unitPrice;
    }
    
    public double getTotalPrice() {
        return totalPrice;
    }
    
    public void setTotalPrice(double totalPrice) {
        this.totalPrice = totalPrice;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
}
