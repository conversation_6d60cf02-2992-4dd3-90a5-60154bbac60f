@echo off
chcp 65001 >nul
title تحديث نظام إدارة المخزون
color 0A

echo.
echo ========================================
echo    تحديث نظام إدارة المخزون
echo ========================================
echo.

echo 🔄 فحص التحديثات...
echo.

REM فحص وجود ملف HTML الجديد
if exist "index-professional.html" (
    echo ✅ ملف النظام موجود
    
    REM نسخ احتياطية
    if exist "dist" (
        echo 💾 إنشاء نسخة احتياطية...
        if not exist "backup" mkdir "backup"
        copy "dist\*.exe" "backup\" >nul 2>&1
        echo ✅ تم حفظ النسخة الاحتياطية
    )
    
    echo.
    echo 🔨 إعادة بناء التطبيق مع التحديثات...
    call "اعمل-كل-شي.bat"
    
) else (
    echo ❌ ملف index-professional.html غير موجود!
    echo تأكد من وجود أحدث نسخة من النظام
    pause
    exit /b 1
)

echo.
echo ✅ تم تحديث النظام بنجاح!
echo.
pause
