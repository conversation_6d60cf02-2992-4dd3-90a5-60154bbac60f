import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * نظام إدارة المخزون الاحترافي - الإصدار المبسط
 */
public class ProfessionalInventorySimple extends JFrame {
    private static final String DB_URL = "*************************************";
    private Connection connection;
    private String currentUser = "admin";

    // مكونات الواجهة
    private JTabbedPane tabbedPane;
    private JTable inventoryTable;
    private JTable transactionsTable;
    private DefaultTableModel inventoryModel;
    private DefaultTableModel transactionsModel;

    // حقول الإدخال
    private JTextField itemNameField;
    private JTextField quantityField;
    private JTextField unitField;
    private JComboBox<String> transactionTypeCombo;
    private JTextField projectField;
    private JTextField supplierField;
    private JTextField priceField;
    private JTextArea notesArea;

    public ProfessionalInventorySimple() {
        initializeDatabase();
        initializeGUI();
        loadData();
    }

    private void initializeDatabase() {
        try {
            Class.forName("org.sqlite.JDBC");
            connection = DriverManager.getConnection(DB_URL);
            createTables();
        } catch (Exception e) {
            e.printStackTrace();
            JOptionPane.showMessageDialog(this, "خطأ في الاتصال بقاعدة البيانات: " + e.getMessage());
        }
    }

    private void createTables() {
        try {
            Statement stmt = connection.createStatement();

            // جدول المواد المحسن
            String itemsTable = """
                CREATE TABLE IF NOT EXISTS items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    unit TEXT NOT NULL,
                    current_stock REAL DEFAULT 0,
                    min_stock REAL DEFAULT 0,
                    unit_price REAL DEFAULT 0,
                    category TEXT DEFAULT 'عام',
                    location TEXT,
                    notes TEXT,
                    created_at TEXT NOT NULL
                )
                """;
            stmt.execute(itemsTable);

            // جدول المعاملات المحسن
            String transactionsTable = """
                CREATE TABLE IF NOT EXISTS transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    item_name TEXT NOT NULL,
                    quantity REAL NOT NULL,
                    transaction_type TEXT NOT NULL,
                    unit_price REAL DEFAULT 0,
                    total_price REAL DEFAULT 0,
                    project TEXT,
                    supplier_or_receiver TEXT,
                    transaction_date TEXT NOT NULL,
                    user_name TEXT DEFAULT 'admin',
                    status TEXT DEFAULT 'مؤكد',
                    notes TEXT
                )
                """;
            stmt.execute(transactionsTable);

            // جدول المستخدمين
            String usersTable = """
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT NOT NULL UNIQUE,
                    password TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    role TEXT NOT NULL,
                    created_at TEXT NOT NULL
                )
                """;
            stmt.execute(usersTable);

            // إنشاء مدير افتراضي
            createDefaultAdmin();

        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    private void createDefaultAdmin() {
        try {
            String checkSql = "SELECT COUNT(*) FROM users WHERE username = 'admin'";
            PreparedStatement checkStmt = connection.prepareStatement(checkSql);
            ResultSet rs = checkStmt.executeQuery();

            if (rs.next() && rs.getInt(1) == 0) {
                String insertSql = "INSERT INTO users (username, password, full_name, role, created_at) VALUES (?, ?, ?, ?, ?)";
                PreparedStatement pstmt = connection.prepareStatement(insertSql);
                pstmt.setString(1, "admin");
                pstmt.setString(2, hashPassword("admin123"));
                pstmt.setString(3, "مدير النظام");
                pstmt.setString(4, "ADMIN");
                pstmt.setString(5, LocalDateTime.now().toString());
                pstmt.executeUpdate();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    private String hashPassword(String password) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(password.getBytes());
            return Base64.getEncoder().encodeToString(hash);
        } catch (Exception e) {
            return password; // fallback
        }
    }

    private void initializeGUI() {
        setTitle("نظام إدارة المخزون الاحترافي v2.0 - " + currentUser);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1200, 800);
        setLocationRelativeTo(null);

        // تعيين الخط العربي
        Font arabicFont = new Font("Segoe UI", Font.PLAIN, 14);
        UIManager.put("Label.font", arabicFont);
        UIManager.put("Button.font", arabicFont);
        UIManager.put("TextField.font", arabicFont);
        UIManager.put("Table.font", arabicFont);
        UIManager.put("TableHeader.font", new Font("Segoe UI", Font.BOLD, 14));

        createComponents();
        setupLayout();
    }

    private void createComponents() {
        tabbedPane = new JTabbedPane();
        tabbedPane.setFont(new Font("Segoe UI", Font.BOLD, 14));

        // تبويب لوحة التحكم
        JPanel dashboardPanel = createDashboardPanel();
        tabbedPane.addTab("🏠 لوحة التحكم", dashboardPanel);

        // تبويب إدخال البيانات
        JPanel inputPanel = createInputPanel();
        tabbedPane.addTab("➕ إدخال البيانات", inputPanel);

        // تبويب المخزون
        JPanel inventoryPanel = createInventoryPanel();
        tabbedPane.addTab("📦 المخزون الحالي", inventoryPanel);

        // تبويب المعاملات
        JPanel transactionsPanel = createTransactionsPanel();
        tabbedPane.addTab("📋 سجل المعاملات", transactionsPanel);
    }

    private JPanel createDashboardPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(new EmptyBorder(20, 20, 20, 20));

        // العنوان الترحيبي
        JLabel welcomeLabel = new JLabel("مرحباً بك في نظام إدارة المخزون الاحترافي", SwingConstants.CENTER);
        welcomeLabel.setFont(new Font("Segoe UI", Font.BOLD, 24));
        welcomeLabel.setForeground(new Color(33, 150, 243));

        // لوحة الإحصائيات
        JPanel statsPanel = new JPanel(new GridLayout(2, 2, 20, 20));
        statsPanel.setBorder(new EmptyBorder(30, 50, 30, 50));

        // بطاقات الإحصائيات
        statsPanel.add(createStatCard("إجمالي المواد", getItemCount(), new Color(33, 150, 243)));
        statsPanel.add(createStatCard("المواد المنخفضة", getLowStockCount(), new Color(244, 67, 54)));
        statsPanel.add(createStatCard("المعاملات اليوم", getTodayTransactions(), new Color(76, 175, 80)));
        statsPanel.add(createStatCard("القيمة الإجمالية", getTotalValue() + " د.ع", new Color(255, 152, 0)));

        panel.add(welcomeLabel, BorderLayout.NORTH);
        panel.add(statsPanel, BorderLayout.CENTER);

        return panel;
    }

    private JPanel createStatCard(String title, String value, Color color) {
        JPanel card = new JPanel();
        card.setLayout(new BoxLayout(card, BoxLayout.Y_AXIS));
        card.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(color, 2),
            new EmptyBorder(15, 15, 15, 15)
        ));
        card.setBackground(Color.WHITE);

        JLabel titleLabel = new JLabel(title);
        titleLabel.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        titleLabel.setForeground(new Color(117, 117, 117));
        titleLabel.setAlignmentX(Component.CENTER_ALIGNMENT);

        JLabel valueLabel = new JLabel(value);
        valueLabel.setFont(new Font("Segoe UI", Font.BOLD, 28));
        valueLabel.setForeground(color);
        valueLabel.setAlignmentX(Component.CENTER_ALIGNMENT);

        card.add(titleLabel);
        card.add(Box.createVerticalStrut(10));
        card.add(valueLabel);

        return card;
    }

    private JPanel createInputPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(new EmptyBorder(20, 20, 20, 20));
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        gbc.anchor = GridBagConstraints.EAST;

        // اسم المادة
        gbc.gridx = 1; gbc.gridy = 0;
        panel.add(new JLabel("اسم المادة:"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        itemNameField = new JTextField(20);
        panel.add(itemNameField, gbc);

        // الكمية
        gbc.gridx = 1; gbc.gridy = 1; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("الكمية:"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        quantityField = new JTextField(20);
        panel.add(quantityField, gbc);

        // الوحدة
        gbc.gridx = 1; gbc.gridy = 2; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("الوحدة:"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        unitField = new JTextField(20);
        panel.add(unitField, gbc);

        // نوع الحركة
        gbc.gridx = 1; gbc.gridy = 3; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("نوع الحركة:"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        transactionTypeCombo = new JComboBox<>(new String[]{"وارد", "صادر", "تسوية"});
        panel.add(transactionTypeCombo, gbc);

        // المشروع
        gbc.gridx = 1; gbc.gridy = 4; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("المشروع:"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        projectField = new JTextField(20);
        panel.add(projectField, gbc);

        // المورد/المستلم
        gbc.gridx = 1; gbc.gridy = 5; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("المورد/المستلم:"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        supplierField = new JTextField(20);
        panel.add(supplierField, gbc);

        // السعر
        gbc.gridx = 1; gbc.gridy = 6; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("السعر (دينار):"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        priceField = new JTextField(20);
        panel.add(priceField, gbc);

        // الملاحظات
        gbc.gridx = 1; gbc.gridy = 7; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("ملاحظات:"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.BOTH;
        notesArea = new JTextArea(3, 20);
        JScrollPane notesScroll = new JScrollPane(notesArea);
        panel.add(notesScroll, gbc);

        // زر الحفظ
        gbc.gridx = 0; gbc.gridy = 8; gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.NONE; gbc.anchor = GridBagConstraints.CENTER;
        JButton saveButton = new JButton("💾 حفظ");
        saveButton.setFont(new Font("Segoe UI", Font.BOLD, 16));
        saveButton.setBackground(new Color(76, 175, 80));
        saveButton.setForeground(Color.WHITE);
        saveButton.setFocusPainted(false);
        saveButton.addActionListener(new SaveButtonListener());
        panel.add(saveButton, gbc);

        return panel;
    }

    private JPanel createInventoryPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(new EmptyBorder(10, 10, 10, 10));

        // جدول المخزون
        String[] columns = {"الرقم", "اسم المادة", "الوحدة", "المخزون الحالي", "الحد الأدنى", "سعر الوحدة", "القيمة الإجمالية", "الفئة", "الموقع", "الحالة"};
        inventoryModel = new DefaultTableModel(columns, 0);
        inventoryTable = new JTable(inventoryModel);
        inventoryTable.setRowHeight(25);

        JScrollPane scrollPane = new JScrollPane(inventoryTable);
        panel.add(scrollPane, BorderLayout.CENTER);

        // زر التحديث
        JButton refreshButton = new JButton("🔄 تحديث");
        refreshButton.setFont(new Font("Segoe UI", Font.BOLD, 14));
        refreshButton.setBackground(new Color(33, 150, 243));
        refreshButton.setForeground(Color.WHITE);
        refreshButton.addActionListener(e -> loadInventoryData());
        panel.add(refreshButton, BorderLayout.SOUTH);

        return panel;
    }

    private JPanel createTransactionsPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(new EmptyBorder(10, 10, 10, 10));

        // جدول المعاملات
        String[] columns = {"الرقم", "التاريخ", "اسم المادة", "الكمية", "نوع الحركة", "المشروع", "المورد/المستلم", "سعر الوحدة", "القيمة الإجمالية", "الحالة"};
        transactionsModel = new DefaultTableModel(columns, 0);
        transactionsTable = new JTable(transactionsModel);
        transactionsTable.setRowHeight(25);

        JScrollPane scrollPane = new JScrollPane(transactionsTable);
        panel.add(scrollPane, BorderLayout.CENTER);

        // زر التحديث
        JButton refreshButton = new JButton("🔄 تحديث");
        refreshButton.setFont(new Font("Segoe UI", Font.BOLD, 14));
        refreshButton.setBackground(new Color(33, 150, 243));
        refreshButton.setForeground(Color.WHITE);
        refreshButton.addActionListener(e -> loadTransactionsData());
        panel.add(refreshButton, BorderLayout.SOUTH);

        return panel;
    }

    private void setupLayout() {
        setLayout(new BorderLayout());
        add(tabbedPane, BorderLayout.CENTER);

        // شريط الحالة
        JPanel statusPanel = new JPanel(new BorderLayout());
        statusPanel.setBorder(BorderFactory.createEtchedBorder());
        statusPanel.setPreferredSize(new Dimension(0, 25));

        JLabel statusLabel = new JLabel("جاهز - نظام إدارة المخزون الاحترافي v2.0");
        statusLabel.setBorder(new EmptyBorder(2, 10, 2, 10));

        JLabel userLabel = new JLabel("المستخدم: " + currentUser);
        userLabel.setBorder(new EmptyBorder(2, 10, 2, 10));

        JLabel timeLabel = new JLabel(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
        timeLabel.setBorder(new EmptyBorder(2, 10, 2, 10));

        statusPanel.add(statusLabel, BorderLayout.WEST);
        statusPanel.add(userLabel, BorderLayout.CENTER);
        statusPanel.add(timeLabel, BorderLayout.EAST);

        add(statusPanel, BorderLayout.SOUTH);
    }

    private void loadData() {
        loadInventoryData();
        loadTransactionsData();
    }

    private void loadInventoryData() {
        inventoryModel.setRowCount(0);
        try {
            String sql = "SELECT * FROM items ORDER BY name";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);

            while (rs.next()) {
                Object[] row = {
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getString("unit"),
                    rs.getDouble("current_stock"),
                    rs.getDouble("min_stock"),
                    rs.getDouble("unit_price"),
                    rs.getDouble("current_stock") * rs.getDouble("unit_price"),
                    rs.getString("category"),
                    rs.getString("location"),
                    rs.getDouble("current_stock") <= rs.getDouble("min_stock") ? "⚠️ منخفض" : "✅ طبيعي"
                };
                inventoryModel.addRow(row);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    private void loadTransactionsData() {
        transactionsModel.setRowCount(0);
        try {
            String sql = "SELECT * FROM transactions ORDER BY transaction_date DESC LIMIT 100";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);

            while (rs.next()) {
                Object[] row = {
                    rs.getInt("id"),
                    rs.getString("transaction_date").substring(0, 16),
                    rs.getString("item_name"),
                    rs.getDouble("quantity"),
                    rs.getString("transaction_type"),
                    rs.getString("project"),
                    rs.getString("supplier_or_receiver"),
                    rs.getDouble("unit_price"),
                    rs.getDouble("total_price"),
                    rs.getString("status")
                };
                transactionsModel.addRow(row);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    // دوال الإحصائيات
    private String getItemCount() {
        try {
            String sql = "SELECT COUNT(*) FROM items";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            if (rs.next()) {
                return String.valueOf(rs.getInt(1));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return "0";
    }

    private String getLowStockCount() {
        try {
            String sql = "SELECT COUNT(*) FROM items WHERE current_stock <= min_stock AND min_stock > 0";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            if (rs.next()) {
                return String.valueOf(rs.getInt(1));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return "0";
    }

    private String getTodayTransactions() {
        try {
            String today = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String sql = "SELECT COUNT(*) FROM transactions WHERE transaction_date LIKE ?";
            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, today + "%");
            ResultSet rs = pstmt.executeQuery();
            if (rs.next()) {
                return String.valueOf(rs.getInt(1));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return "0";
    }

    private String getTotalValue() {
        try {
            String sql = "SELECT SUM(current_stock * unit_price) FROM items";
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            if (rs.next()) {
                return String.format("%.2f", rs.getDouble(1));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return "0.00";
    }

    // فئة مستمع زر الحفظ
    private class SaveButtonListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            try {
                String itemName = itemNameField.getText().trim();
                String quantityText = quantityField.getText().trim();
                String unit = unitField.getText().trim();
                String transactionType = (String) transactionTypeCombo.getSelectedItem();
                String project = projectField.getText().trim();
                String supplier = supplierField.getText().trim();
                String priceText = priceField.getText().trim();
                String notes = notesArea.getText().trim();

                if (itemName.isEmpty() || quantityText.isEmpty() || unit.isEmpty()) {
                    JOptionPane.showMessageDialog(ProfessionalInventorySimple.this,
                        "يرجى ملء الحقول المطلوبة: اسم المادة، الكمية، الوحدة",
                        "خطأ في البيانات", JOptionPane.ERROR_MESSAGE);
                    return;
                }

                double quantity = Double.parseDouble(quantityText);
                double unitPrice = priceText.isEmpty() ? 0.0 : Double.parseDouble(priceText);
                double totalPrice = quantity * unitPrice;

                // إضافة أو تحديث المادة
                ProfessionalInventorySimple.this.addOrUpdateItem(itemName, unit, unitPrice, notes);

                // إضافة المعاملة
                ProfessionalInventorySimple.this.addTransaction(itemName, quantity, transactionType, unitPrice, totalPrice, project, supplier, notes);

                // تحديث المخزون
                ProfessionalInventorySimple.this.updateStock(itemName, quantity, transactionType.equals("وارد"));

                JOptionPane.showMessageDialog(ProfessionalInventorySimple.this,
                    "تم حفظ البيانات بنجاح ✅",
                    "نجح الحفظ", JOptionPane.INFORMATION_MESSAGE);

                ProfessionalInventorySimple.this.clearFields();
                ProfessionalInventorySimple.this.loadData();

            } catch (NumberFormatException ex) {
                JOptionPane.showMessageDialog(ProfessionalInventorySimple.this,
                    "يرجى إدخال أرقام صحيحة في حقول الكمية والسعر",
                    "خطأ في البيانات", JOptionPane.ERROR_MESSAGE);
            } catch (Exception ex) {
                JOptionPane.showMessageDialog(ProfessionalInventorySimple.this,
                    "خطأ في حفظ البيانات: " + ex.getMessage(),
                    "خطأ", JOptionPane.ERROR_MESSAGE);
                ex.printStackTrace();
            }
        }
    }

    // دوال قاعدة البيانات
    private void addOrUpdateItem(String name, String unit, double unitPrice, String notes) throws SQLException {
        // التحقق من وجود المادة
        String checkSql = "SELECT id FROM items WHERE name = ?";
        PreparedStatement checkStmt = connection.prepareStatement(checkSql);
        checkStmt.setString(1, name);
        ResultSet rs = checkStmt.executeQuery();

        if (rs.next()) {
            // تحديث المادة الموجودة
            String updateSql = "UPDATE items SET unit = ?, unit_price = ?, notes = ? WHERE name = ?";
            PreparedStatement updateStmt = connection.prepareStatement(updateSql);
            updateStmt.setString(1, unit);
            updateStmt.setDouble(2, unitPrice);
            updateStmt.setString(3, notes);
            updateStmt.setString(4, name);
            updateStmt.executeUpdate();
        } else {
            // إضافة مادة جديدة
            String insertSql = "INSERT INTO items (name, unit, unit_price, notes, created_at) VALUES (?, ?, ?, ?, ?)";
            PreparedStatement insertStmt = connection.prepareStatement(insertSql);
            insertStmt.setString(1, name);
            insertStmt.setString(2, unit);
            insertStmt.setDouble(3, unitPrice);
            insertStmt.setString(4, notes);
            insertStmt.setString(5, LocalDateTime.now().toString());
            insertStmt.executeUpdate();
        }
    }

    private void addTransaction(String itemName, double quantity, String transactionType,
                               double unitPrice, double totalPrice, String project,
                               String supplier, String notes) throws SQLException {
        String sql = "INSERT INTO transactions (item_name, quantity, transaction_type, unit_price, total_price, project, supplier_or_receiver, transaction_date, user_name, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        PreparedStatement pstmt = connection.prepareStatement(sql);
        pstmt.setString(1, itemName);
        pstmt.setDouble(2, quantity);
        pstmt.setString(3, transactionType);
        pstmt.setDouble(4, unitPrice);
        pstmt.setDouble(5, totalPrice);
        pstmt.setString(6, project);
        pstmt.setString(7, supplier);
        pstmt.setString(8, LocalDateTime.now().toString());
        pstmt.setString(9, currentUser);
        pstmt.setString(10, notes);
        pstmt.executeUpdate();
    }

    private void updateStock(String itemName, double quantity, boolean isIncoming) throws SQLException {
        String sql = "UPDATE items SET current_stock = current_stock + ? WHERE name = ?";
        PreparedStatement pstmt = connection.prepareStatement(sql);
        pstmt.setDouble(1, isIncoming ? quantity : -quantity);
        pstmt.setString(2, itemName);
        pstmt.executeUpdate();
    }

    private void clearFields() {
        itemNameField.setText("");
        quantityField.setText("");
        unitField.setText("");
        projectField.setText("");
        supplierField.setText("");
        priceField.setText("");
        notesArea.setText("");
        transactionTypeCombo.setSelectedIndex(0);
    }

    // الدالة الرئيسية
    public static void main(String[] args) {
        // تعيين Look and Feel (تم تعطيله لتجنب مشاكل التوافق)

        SwingUtilities.invokeLater(() -> {
            try {
                // عرض شاشة ترحيب
                JOptionPane.showMessageDialog(null,
                    "مرحباً بك في نظام إدارة المخزون الاحترافي v2.0\n\n" +
                    "🔐 معلومات تسجيل الدخول:\n" +
                    "👤 اسم المستخدم: admin\n" +
                    "🔑 كلمة المرور: admin123\n\n" +
                    "✨ الميزات الاحترافية:\n" +
                    "• إدارة مخزون متقدمة\n" +
                    "• تتبع المعاملات\n" +
                    "• إحصائيات فورية\n" +
                    "• واجهة عربية حديثة",
                    "نظام إدارة المخزون الاحترافي",
                    JOptionPane.INFORMATION_MESSAGE);

                ProfessionalInventorySimple app = new ProfessionalInventorySimple();
                app.setVisible(true);

            } catch (Exception e) {
                e.printStackTrace();
                JOptionPane.showMessageDialog(null,
                    "خطأ في تشغيل التطبيق: " + e.getMessage(),
                    "خطأ", JOptionPane.ERROR_MESSAGE);
            }
        });
    }
}