@echo off
echo ========================================
echo   نظام إدارة المخزون الاحترافي v2.0
echo ========================================
echo.

REM التحقق من وجود الملفات المجمعة
if not exist "out\com\inventory\ProfessionalInventoryApp.class" (
    echo ❌ الملفات غير مجمعة!
    echo يرجى تشغيل compile-professional.bat أولاً
    echo.
    pause
    exit /b 1
)

REM التحقق من وجود المكتبات
if not exist "lib\sqlite-jdbc-3.44.1.0.jar" (
    echo ❌ مكتبة SQLite غير موجودة!
    echo يرجى تشغيل compile-professional.bat لتحميل المكتبات
    echo.
    pause
    exit /b 1
)

echo 🚀 تشغيل النظام الاحترافي...
echo.

REM تشغيل التطبيق مع الخيارات المتقدمة
java --enable-native-access=ALL-UNNAMED -Dfile.encoding=UTF-8 -Dsun.java2d.uiScale=1.0 -cp "out;lib/*" com.inventory.ProfessionalInventoryApp

REM في حالة فشل الخيارات المتقدمة، جرب الطريقة البسيطة
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ⚠️ فشل التشغيل بالخيارات المتقدمة، جاري المحاولة بالطريقة البسيطة...
    java -Dfile.encoding=UTF-8 -cp "out;lib/*" com.inventory.ProfessionalInventoryApp
)

echo.
echo تم إغلاق النظام.
pause
