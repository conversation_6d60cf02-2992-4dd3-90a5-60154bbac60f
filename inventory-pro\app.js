// نظام إدارة المخزون الاحترافي المتطور - JavaScript
class InventoryManager {
    constructor() {
        this.items = JSON.parse(localStorage.getItem('inventory_items')) || [];
        this.transactions = JSON.parse(localStorage.getItem('inventory_transactions')) || [];
        this.projects = JSON.parse(localStorage.getItem('inventory_projects')) || [];
        this.settings = JSON.parse(localStorage.getItem('inventory_settings')) || this.getDefaultSettings();
        this.init();
    }

    getDefaultSettings() {
        return {
            companyName: 'هيمن كروب - شركة متخصصة',
            companyAddress: '',
            companyPhone: '',
            currency: 'د.ع',
            lowStockThreshold: 10
        };
    }

    init() {
        this.bindEvents();
        this.updateStats();
        this.renderInventoryTable();
        this.renderTransactionsTable();
        this.renderProjectsTable();
        this.loadProjects();
        this.setCurrentDateTime();
        this.showWelcomeMessage();
        this.initializeDefaultProjects();
    }

    bindEvents() {
        const form = document.getElementById('itemForm');
        form.addEventListener('submit', (e) => this.handleFormSubmit(e));

        const projectForm = document.getElementById('projectForm');
        projectForm.addEventListener('submit', (e) => this.handleProjectSubmit(e));

        // مراقبة تغيير المشروع
        const projectSelect = document.getElementById('project');
        projectSelect.addEventListener('change', (e) => this.handleProjectChange(e));
    }

    setCurrentDateTime() {
        const now = new Date();
        const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
        document.getElementById('transactionDate').value = localDateTime;
    }

    initializeDefaultProjects() {
        if (this.projects.length === 0) {
            this.projects = [
                {
                    id: 1,
                    name: 'المشروع الرئيسي',
                    description: 'المشروع الأساسي للشركة',
                    manager: 'إدارة هيمن كروب',
                    budget: 1000000,
                    spent: 0,
                    status: 'نشط',
                    created: new Date().toISOString()
                },
                {
                    id: 2,
                    name: 'مشروع التوسعة',
                    description: 'توسعة الأعمال والفروع',
                    manager: 'قسم التطوير',
                    budget: 500000,
                    spent: 0,
                    status: 'نشط',
                    created: new Date().toISOString()
                },
                {
                    id: 3,
                    name: 'مشروع الصيانة',
                    description: 'صيانة دورية للمعدات والمرافق',
                    manager: 'قسم الصيانة',
                    budget: 200000,
                    spent: 0,
                    status: 'نشط',
                    created: new Date().toISOString()
                }
            ];
            this.saveData();
        }
    }

    showWelcomeMessage() {
        if (this.items.length === 0) {
            this.showAlert('مرحباً بك في نظام إدارة المخزون الاحترافي! ابدأ بإضافة أول معاملة.', 'success');
        }
    }

    handleFormSubmit(e) {
        e.preventDefault();

        let projectValue = document.getElementById('project').value;
        if (projectValue === 'مشروع جديد') {
            projectValue = document.getElementById('newProject').value.trim();
            if (!projectValue) {
                this.showAlert('يرجى إدخال اسم المشروع الجديد', 'danger');
                return;
            }
        }

        const formData = {
            itemName: document.getElementById('itemName').value.trim(),
            quantity: parseFloat(document.getElementById('quantity').value),
            unit: document.getElementById('unit').value.trim(),
            transactionType: document.getElementById('transactionType').value,
            transactionDate: document.getElementById('transactionDate').value,
            unitPrice: parseFloat(document.getElementById('unitPrice').value) || 0,
            project: projectValue,
            supplier: document.getElementById('supplier').value.trim(),
            invoiceNumber: document.getElementById('invoiceNumber').value.trim(),
            notes: document.getElementById('notes').value.trim()
        };

        if (!this.validateForm(formData)) {
            return;
        }

        this.processTransaction(formData);
        this.clearForm();
        this.updateStats();
        this.renderInventoryTable();
        this.renderTransactionsTable();
        this.showAlert('تم حفظ المعاملة بنجاح! ✅', 'success');
    }

    handleProjectSubmit(e) {
        e.preventDefault();

        const projectData = {
            id: Date.now(),
            name: document.getElementById('projectName').value.trim(),
            description: document.getElementById('projectDescription').value.trim(),
            manager: document.getElementById('projectManager').value.trim(),
            budget: parseFloat(document.getElementById('projectBudget').value) || 0,
            spent: 0,
            status: 'نشط',
            created: new Date().toISOString()
        };

        if (!projectData.name) {
            this.showAlert('يرجى إدخال اسم المشروع', 'danger');
            return;
        }

        this.projects.push(projectData);
        this.saveData();
        this.renderProjectsTable();
        this.loadProjects();
        this.clearProjectForm();
        this.showAlert('تم إضافة المشروع بنجاح! ✅', 'success');
    }

    handleProjectChange(e) {
        const newProjectGroup = document.getElementById('newProjectGroup');
        if (e.target.value === 'مشروع جديد') {
            newProjectGroup.style.display = 'block';
            document.getElementById('newProject').required = true;
        } else {
            newProjectGroup.style.display = 'none';
            document.getElementById('newProject').required = false;
        }
    }

    validateForm(data) {
        if (!data.itemName || !data.quantity || !data.unit || !data.transactionType || !data.project || !data.supplier || !data.transactionDate) {
            this.showAlert('يرجى ملء جميع الحقول المطلوبة (المميزة بـ *)', 'danger');
            return false;
        }

        if (data.quantity <= 0) {
            this.showAlert('يجب أن تكون الكمية أكبر من صفر', 'danger');
            return false;
        }

        // التحقق من وجود مخزون كافي للصرف
        if (data.transactionType === 'صادر') {
            const existingItem = this.items.find(item => item.name === data.itemName);
            if (existingItem && existingItem.quantity < data.quantity) {
                this.showAlert(`المخزون الحالي (${existingItem.quantity} ${existingItem.unit}) غير كافي للصرف المطلوب (${data.quantity} ${data.unit})`, 'danger');
                return false;
            }
        }

        return true;
    }

    processTransaction(data) {
        // إضافة المعاملة
        const transaction = {
            id: Date.now(),
            ...data,
            totalPrice: data.quantity * data.unitPrice,
            date: data.transactionDate ? new Date(data.transactionDate).toISOString() : new Date().toISOString(),
            timestamp: new Date().toLocaleString('ar-EG'),
            user: 'المستخدم الحالي'
        };

        this.transactions.push(transaction);

        // تحديث ميزانية المشروع
        this.updateProjectBudget(data.project, transaction.totalPrice, data.transactionType);

        // تحديث أو إضافة المادة
        let existingItem = this.items.find(item => item.name === data.itemName);

        if (existingItem) {
            // تحديث المادة الموجودة
            const oldQuantity = existingItem.quantity;

            if (data.transactionType === 'وارد') {
                existingItem.quantity += data.quantity;
            } else if (data.transactionType === 'صادر') {
                existingItem.quantity -= data.quantity;
            } else if (data.transactionType === 'تسوية') {
                existingItem.quantity = data.quantity;
            } else if (data.transactionType === 'نقل') {
                // النقل لا يؤثر على الكمية الإجمالية
            }

            // تحديث السعر إذا كان جديد
            if (data.unitPrice > 0) {
                existingItem.unitPrice = data.unitPrice;
            }

            existingItem.lastUpdated = new Date().toISOString();
            existingItem.lastTransaction = {
                type: data.transactionType,
                quantity: data.quantity,
                date: transaction.date,
                project: data.project
            };
        } else {
            // إضافة مادة جديدة
            let initialQuantity = 0;
            if (data.transactionType === 'وارد') {
                initialQuantity = data.quantity;
            } else if (data.transactionType === 'تسوية') {
                initialQuantity = data.quantity;
            }

            const newItem = {
                id: Date.now(),
                name: data.itemName,
                quantity: initialQuantity,
                unit: data.unit,
                unitPrice: data.unitPrice || 0,
                minStock: 10, // الحد الأدنى الافتراضي
                category: 'عام',
                location: 'المخزن الرئيسي',
                created: new Date().toISOString(),
                lastUpdated: new Date().toISOString(),
                lastTransaction: {
                    type: data.transactionType,
                    quantity: data.quantity,
                    date: transaction.date,
                    project: data.project
                }
            };

            this.items.push(newItem);
        }

        // إضافة المعاملة للجدول المؤقت حسب النوع
        this.addToPrintingTable(transaction);

        this.saveData();
    }

    updateProjectBudget(projectName, amount, transactionType) {
        const project = this.projects.find(p => p.name === projectName);
        if (project && (transactionType === 'وارد' || transactionType === 'صادر')) {
            if (transactionType === 'صادر') {
                project.spent += amount;
            }
        }
    }

    updateStats() {
        // إجمالي المواد
        document.getElementById('totalItems').textContent = this.items.length;

        // القيمة الإجمالية
        const totalValue = this.items.reduce((sum, item) => {
            return sum + (item.quantity * item.unitPrice);
        }, 0);
        document.getElementById('totalValue').textContent = totalValue.toLocaleString('ar-EG', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });

        // المواد المنخفضة
        const lowStockItems = this.items.filter(item => item.quantity <= item.minStock).length;
        document.getElementById('lowStockItems').textContent = lowStockItems;

        // معاملات اليوم
        const today = new Date().toDateString();
        const todayTransactions = this.transactions.filter(transaction => {
            return new Date(transaction.date).toDateString() === today;
        }).length;
        document.getElementById('todayTransactions').textContent = todayTransactions;
    }

    renderInventoryTable() {
        const tbody = document.getElementById('inventoryTableBody');
        const tbodyDetailed = document.getElementById('inventoryTableBodyDetailed');

        if (this.items.length === 0) {
            const emptyRow = `
                <tr>
                    <td colspan="8" style="text-align: center; color: #7f8c8d; padding: 40px;">
                        <i class="fas fa-box-open" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>
                        لا توجد مواد في المخزون<br>
                        <small>ابدأ بإضافة أول معاملة</small>
                    </td>
                </tr>
            `;
            if (tbody) tbody.innerHTML = emptyRow;
            if (tbodyDetailed) tbodyDetailed.innerHTML = emptyRow;
            return;
        }

        const itemsHtml = this.items.map(item => {
            const totalValue = item.quantity * item.unitPrice;
            const status = this.getItemStatus(item);
            const lastTransaction = item.lastTransaction ?
                `${item.lastTransaction.type} - ${new Date(item.lastTransaction.date).toLocaleDateString('ar-EG')}` :
                'لا توجد حركة';

            const simpleRow = `
                <tr>
                    <td>
                        <strong>${item.name}</strong>
                        <br><small style="color: #7f8c8d;">${item.category}</small>
                    </td>
                    <td>
                        <span style="font-weight: 600; font-size: 1.1rem;">
                            ${item.quantity.toLocaleString('ar-EG')}
                        </span>
                    </td>
                    <td>${item.unit}</td>
                    <td>
                        ${item.unitPrice.toLocaleString('ar-EG', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        })} د.ع
                    </td>
                    <td>
                        <strong>
                            ${totalValue.toLocaleString('ar-EG', {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            })} د.ع
                        </strong>
                    </td>
                    <td>${status}</td>
                </tr>
            `;

            const detailedRow = `
                <tr>
                    <td>
                        <strong>${item.name}</strong>
                        <br><small style="color: #7f8c8d;">${item.category} - ${item.location || 'غير محدد'}</small>
                    </td>
                    <td>
                        <span style="font-weight: 600; font-size: 1.1rem; ${item.quantity <= item.minStock ? 'color: #e74c3c;' : ''}">
                            ${item.quantity.toLocaleString('ar-EG')}
                        </span>
                        <br><small style="color: #7f8c8d;">الحد الأدنى: ${item.minStock}</small>
                    </td>
                    <td>${item.unit}</td>
                    <td>
                        ${item.unitPrice.toLocaleString('ar-EG', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        })} د.ع
                    </td>
                    <td>
                        <strong>
                            ${totalValue.toLocaleString('ar-EG', {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            })} د.ع
                        </strong>
                    </td>
                    <td>
                        <small style="color: #7f8c8d;">${lastTransaction}</small>
                    </td>
                    <td>${status}</td>
                    <td>
                        <button class="action-btn edit" onclick="editItem(${item.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete" onclick="deleteItem(${item.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;

            return { simple: simpleRow, detailed: detailedRow };
        });

        if (tbody) {
            tbody.innerHTML = itemsHtml.map(item => item.simple).join('');
        }
        if (tbodyDetailed) {
            tbodyDetailed.innerHTML = itemsHtml.map(item => item.detailed).join('');
        }
    }

    renderTransactionsTable() {
        const tbody = document.getElementById('transactionsTableBody');

        if (this.transactions.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="10" style="text-align: center; color: #7f8c8d; padding: 40px;">
                        <i class="fas fa-exchange-alt" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>
                        لا توجد معاملات<br>
                        <small>ابدأ بإضافة أول معاملة</small>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.transactions.slice(-50).reverse().map(transaction => {
            const date = new Date(transaction.date);
            const typeColor = transaction.transactionType === 'وارد' ? '#2ecc71' :
                             transaction.transactionType === 'صادر' ? '#e74c3c' : '#f39c12';

            return `
                <tr>
                    <td>
                        <small>${date.toLocaleDateString('ar-EG')}</small><br>
                        <small style="color: #7f8c8d;">${date.toLocaleTimeString('ar-EG', {hour: '2-digit', minute: '2-digit'})}</small>
                    </td>
                    <td><strong>${transaction.itemName}</strong></td>
                    <td>
                        <span class="badge" style="background: ${typeColor}; color: white;">
                            ${transaction.transactionType}
                        </span>
                    </td>
                    <td>${transaction.quantity.toLocaleString('ar-EG')} ${transaction.unit}</td>
                    <td>${transaction.unitPrice.toLocaleString('ar-EG')} د.ع</td>
                    <td><strong>${transaction.totalPrice.toLocaleString('ar-EG')} د.ع</strong></td>
                    <td>${transaction.project}</td>
                    <td>${transaction.supplier}</td>
                    <td>${transaction.invoiceNumber || '-'}</td>
                    <td>
                        <button class="action-btn delete" onclick="deleteTransaction(${transaction.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    }

    renderProjectsTable() {
        const tbody = document.getElementById('projectsTableBody');

        if (this.projects.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" style="text-align: center; color: #7f8c8d; padding: 40px;">
                        <i class="fas fa-project-diagram" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>
                        لا توجد مشاريع<br>
                        <small>ابدأ بإضافة أول مشروع</small>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.projects.map(project => {
            const remaining = project.budget - project.spent;
            const percentage = project.budget > 0 ? (project.spent / project.budget * 100) : 0;
            const statusColor = percentage > 90 ? '#e74c3c' : percentage > 70 ? '#f39c12' : '#2ecc71';

            return `
                <tr>
                    <td>
                        <strong>${project.name}</strong>
                        <br><small style="color: #7f8c8d;">${project.description}</small>
                    </td>
                    <td>${project.manager}</td>
                    <td>${project.budget.toLocaleString('ar-EG')} د.ع</td>
                    <td style="color: #e74c3c;">${project.spent.toLocaleString('ar-EG')} د.ع</td>
                    <td style="color: ${remaining >= 0 ? '#2ecc71' : '#e74c3c'};">
                        ${remaining.toLocaleString('ar-EG')} د.ع
                    </td>
                    <td>
                        <span class="badge" style="background: ${statusColor}; color: white;">
                            ${project.status}
                        </span>
                        <br><small>${percentage.toFixed(1)}% مستخدم</small>
                    </td>
                    <td>
                        <button class="action-btn edit" onclick="editProject(${project.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete" onclick="deleteProject(${project.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    }

    getItemStatus(item) {
        if (item.quantity <= 0) {
            return '<span class="badge badge-danger"><i class="fas fa-times"></i> نفد المخزون</span>';
        } else if (item.quantity <= item.minStock) {
            return '<span class="badge badge-warning"><i class="fas fa-exclamation-triangle"></i> منخفض</span>';
        } else {
            return '<span class="badge badge-success"><i class="fas fa-check"></i> متوفر</span>';
        }
    }

    showAlert(message, type) {
        const alertContainer = document.getElementById('alertContainer');
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        
        alertContainer.innerHTML = `
            <div class="alert ${alertClass}">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
                ${message}
            </div>
        `;

        // إخفاء التنبيه بعد 5 ثوان
        setTimeout(() => {
            alertContainer.innerHTML = '';
        }, 5000);
    }

    loadProjects() {
        const projectSelect = document.getElementById('project');
        const reportSelect = document.getElementById('projectReportSelect');

        // تحديث قائمة المشاريع في النموذج
        const currentValue = projectSelect.value;
        projectSelect.innerHTML = `
            <option value="">اختر المشروع</option>
            ${this.projects.map(project =>
                `<option value="${project.name}">${project.name}</option>`
            ).join('')}
            <option value="مشروع جديد">إضافة مشروع جديد...</option>
        `;
        projectSelect.value = currentValue;

        // تحديث قائمة المشاريع في التقارير
        if (reportSelect) {
            reportSelect.innerHTML = `
                <option value="">اختر المشروع</option>
                ${this.projects.map(project =>
                    `<option value="${project.name}">${project.name}</option>`
                ).join('')}
            `;
        }
    }

    clearForm() {
        document.getElementById('itemForm').reset();
        this.setCurrentDateTime();
        document.getElementById('newProjectGroup').style.display = 'none';
    }

    clearProjectForm() {
        document.getElementById('projectForm').reset();
    }

    saveData() {
        localStorage.setItem('inventory_items', JSON.stringify(this.items));
        localStorage.setItem('inventory_transactions', JSON.stringify(this.transactions));
        localStorage.setItem('inventory_projects', JSON.stringify(this.projects));
        localStorage.setItem('inventory_settings', JSON.stringify(this.settings));

        // حفظ جداول الطباعة المؤقتة
        localStorage.setItem('printing_outgoing', JSON.stringify(this.printingTables.outgoing));
        localStorage.setItem('printing_incoming', JSON.stringify(this.printingTables.incoming));
        localStorage.setItem('printing_damaged', JSON.stringify(this.printingTables.damaged));
        localStorage.setItem('printing_returned', JSON.stringify(this.printingTables.returned));
    }

    // إضافة معاملة للجدول المؤقت
    addToPrintingTable(transaction) {
        const printingItem = {
            id: transaction.id,
            date: transaction.date,
            itemName: transaction.itemName,
            quantity: transaction.quantity,
            unit: transaction.unit,
            supplier: transaction.supplier,
            project: transaction.project,
            notes: transaction.notes,
            timestamp: new Date().toISOString()
        };

        switch(transaction.transactionType) {
            case 'صادر':
                this.printingTables.outgoing.push(printingItem);
                break;
            case 'وارد':
                this.printingTables.incoming.push(printingItem);
                break;
            case 'تالف':
                this.printingTables.damaged.push(printingItem);
                break;
            case 'مرتجع':
                this.printingTables.returned.push(printingItem);
                break;
        }

        this.updatePrintingTablesDisplay();
    }

    // تحديث عرض الجداول المؤقتة
    updatePrintingTablesDisplay() {
        this.renderPrintingTable('outgoing');
        this.renderPrintingTable('incoming');
        this.renderPrintingTable('damaged');
        this.renderPrintingTable('returned');
        this.updatePrintingCounts();
    }

    // عرض جدول طباعة معين
    renderPrintingTable(type) {
        const tbody = document.getElementById(`printing${type.charAt(0).toUpperCase() + type.slice(1)}Body`);
        const data = this.printingTables[type];

        if (!tbody) return;

        if (data.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" style="text-align: center; color: #7f8c8d;">
                        لا توجد معاملات للطباعة
                    </td>
                </tr>
            `;
            this.updatePrintingButtons(type, false);
            return;
        }

        tbody.innerHTML = data.map(item => {
            const date = new Date(item.date);
            return `
                <tr>
                    <td><small>${date.toLocaleDateString('ar-EG')}</small></td>
                    <td><strong>${item.itemName}</strong></td>
                    <td>${item.quantity} ${item.unit}</td>
                    <td>${item.supplier}</td>
                    <td>${item.project}</td>
                    <td>
                        <button class="action-btn delete" onclick="inventoryManager.removeFromPrintingTable('${type}', ${item.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');

        this.updatePrintingButtons(type, true);
    }

    // تحديث أزرار الطباعة
    updatePrintingButtons(type, hasData) {
        const printBtn = document.getElementById(`print${type.charAt(0).toUpperCase() + type.slice(1)}Btn`);
        const clearBtn = document.getElementById(`clear${type.charAt(0).toUpperCase() + type.slice(1)}Btn`);

        if (printBtn) printBtn.disabled = !hasData;
        if (clearBtn) clearBtn.disabled = !hasData;
    }

    // حذف عنصر من الجدول المؤقت
    removeFromPrintingTable(type, id) {
        this.printingTables[type] = this.printingTables[type].filter(item => item.id !== id);
        this.saveData();
        this.updatePrintingTablesDisplay();
        this.showAlert('تم حذف العنصر من جدول الطباعة', 'success');
    }

    // مسح جدول طباعة كامل
    clearPrintingTable(type) {
        if (confirm('هل أنت متأكد من مسح جدول الطباعة؟')) {
            this.printingTables[type] = [];
            this.saveData();
            this.updatePrintingTablesDisplay();
            this.showAlert('تم مسح جدول الطباعة', 'success');
        }
    }

    // تحديث عدادات الطباعة
    updatePrintingCounts() {
        const counts = {
            outgoing: this.printingTables.outgoing.length,
            incoming: this.printingTables.incoming.length,
            damaged: this.printingTables.damaged.length,
            returned: this.printingTables.returned.length
        };

        Object.keys(counts).forEach(type => {
            const countElement = document.getElementById(`${type}Count`);
            if (countElement) {
                countElement.textContent = counts[type];
                countElement.style.display = counts[type] > 0 ? 'inline' : 'none';
            }
        });
    }

    // دوال إضافية للميزات المتقدمة
    exportData() {
        const data = {
            items: this.items,
            transactions: this.transactions,
            exportDate: new Date().toISOString(),
            version: '2.0'
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `inventory-backup-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
    }

    importData(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = JSON.parse(e.target.result);
                if (data.items && data.transactions) {
                    this.items = data.items;
                    this.transactions = data.transactions;
                    this.saveData();
                    this.updateStats();
                    this.renderInventoryTable();
                    this.showAlert('تم استيراد البيانات بنجاح!', 'success');
                }
            } catch (error) {
                this.showAlert('خطأ في ملف البيانات', 'danger');
            }
        };
        reader.readAsText(file);
    }

    generateReport() {
        const report = {
            totalItems: this.items.length,
            totalValue: this.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0),
            lowStockItems: this.items.filter(item => item.quantity <= item.minStock),
            recentTransactions: this.transactions.slice(-10),
            generatedAt: new Date().toLocaleString('ar-EG')
        };

        console.log('تقرير المخزون:', report);
        return report;
    }
}

// دوال التبويبات
function showTab(tabName) {
    // إخفاء جميع التبويبات
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    // إزالة التفعيل من جميع الأزرار
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // إظهار التبويب المحدد
    document.getElementById(tabName).classList.add('active');

    // تفعيل الزر المحدد
    event.target.classList.add('active');

    // تحديث البيانات حسب التبويب
    if (tabName === 'transactions') {
        inventoryManager.renderTransactionsTable();
    } else if (tabName === 'inventory') {
        inventoryManager.renderInventoryTable();
    } else if (tabName === 'projects') {
        inventoryManager.renderProjectsTable();
    } else if (tabName === 'reports') {
        inventoryManager.loadProjects();
    } else if (tabName === 'printing') {
        inventoryManager.updatePrintingTablesDisplay();
        showPrintingSection('outgoing'); // إظهار قسم الصادر افتراضياً
    }
}

// دوال عامة
function refreshData() {
    const btn = event.target;
    const originalText = btn.innerHTML;

    btn.innerHTML = '<div class="loading"></div> جاري التحديث...';
    btn.disabled = true;

    setTimeout(() => {
        inventoryManager.updateStats();
        inventoryManager.renderInventoryTable();
        inventoryManager.renderTransactionsTable();
        inventoryManager.renderProjectsTable();

        btn.innerHTML = originalText;
        btn.disabled = false;

        inventoryManager.showAlert('تم تحديث البيانات بنجاح!', 'success');
    }, 1000);
}

function exportData() {
    inventoryManager.exportData();
}

// دوال التقارير
function generateDailyReport() {
    const date = document.getElementById('dailyReportDate').value;
    if (!date) {
        inventoryManager.showAlert('يرجى اختيار التاريخ', 'danger');
        return;
    }

    const transactions = inventoryManager.transactions.filter(t =>
        new Date(t.date).toDateString() === new Date(date).toDateString()
    );

    displayReport('تقرير يومي - ' + new Date(date).toLocaleDateString('ar-EG'), transactions);
}

function generateWeeklyReport() {
    const week = document.getElementById('weeklyReportDate').value;
    if (!week) {
        inventoryManager.showAlert('يرجى اختيار الأسبوع', 'danger');
        return;
    }

    const [year, weekNum] = week.split('-W');
    const startDate = new Date(year, 0, 1 + (weekNum - 1) * 7);
    const endDate = new Date(startDate.getTime() + 6 * 24 * 60 * 60 * 1000);

    const transactions = inventoryManager.transactions.filter(t => {
        const tDate = new Date(t.date);
        return tDate >= startDate && tDate <= endDate;
    });

    displayReport(`تقرير أسبوعي - الأسبوع ${weekNum} من ${year}`, transactions);
}

function generateMonthlyReport() {
    const month = document.getElementById('monthlyReportDate').value;
    if (!month) {
        inventoryManager.showAlert('يرجى اختيار الشهر', 'danger');
        return;
    }

    const [year, monthNum] = month.split('-');
    const transactions = inventoryManager.transactions.filter(t => {
        const tDate = new Date(t.date);
        return tDate.getFullYear() == year && tDate.getMonth() == monthNum - 1;
    });

    displayReport(`تقرير شهري - ${monthNum}/${year}`, transactions);
}

function generateProjectReport() {
    const project = document.getElementById('projectReportSelect').value;
    if (!project) {
        inventoryManager.showAlert('يرجى اختيار المشروع', 'danger');
        return;
    }

    const transactions = inventoryManager.transactions.filter(t => t.project === project);
    displayReport(`تقرير المشروع - ${project}`, transactions);
}

function displayReport(title, transactions) {
    const totalIn = transactions.filter(t => t.transactionType === 'وارد').reduce((sum, t) => sum + t.totalPrice, 0);
    const totalOut = transactions.filter(t => t.transactionType === 'صادر').reduce((sum, t) => sum + t.totalPrice, 0);
    const net = totalIn - totalOut;

    const reportHtml = `
        <h3>${title}</h3>
        <div class="stats-grid" style="margin: 20px 0;">
            <div class="stat-card success">
                <div class="stat-number">${totalIn.toLocaleString('ar-EG')} د.ع</div>
                <div class="stat-label">إجمالي الوارد</div>
            </div>
            <div class="stat-card danger">
                <div class="stat-number">${totalOut.toLocaleString('ar-EG')} د.ع</div>
                <div class="stat-label">إجمالي الصادر</div>
            </div>
            <div class="stat-card ${net >= 0 ? 'primary' : 'warning'}">
                <div class="stat-number">${net.toLocaleString('ar-EG')} د.ع</div>
                <div class="stat-label">الصافي</div>
            </div>
            <div class="stat-card primary">
                <div class="stat-number">${transactions.length}</div>
                <div class="stat-label">عدد المعاملات</div>
            </div>
        </div>
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>المادة</th>
                        <th>النوع</th>
                        <th>الكمية</th>
                        <th>القيمة</th>
                        <th>المشروع</th>
                    </tr>
                </thead>
                <tbody>
                    ${transactions.map(t => `
                        <tr>
                            <td>${new Date(t.date).toLocaleDateString('ar-EG')}</td>
                            <td>${t.itemName}</td>
                            <td>${t.transactionType}</td>
                            <td>${t.quantity} ${t.unit}</td>
                            <td>${t.totalPrice.toLocaleString('ar-EG')} د.ع</td>
                            <td>${t.project}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
        <button class="btn btn-success" onclick="exportReport('${title}', ${JSON.stringify(transactions).replace(/"/g, '&quot;')})">
            <i class="fas fa-download"></i> تصدير التقرير
        </button>
    `;

    document.getElementById('reportResults').innerHTML = reportHtml;
}

function exportReport(title, transactions) {
    const data = {
        title: title,
        date: new Date().toISOString(),
        transactions: transactions,
        company: inventoryManager.settings.companyName
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
}

function clearAllData() {
    if (confirm('هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        localStorage.clear();
        location.reload();
    }
}

// تشغيل التطبيق
let inventoryManager;

document.addEventListener('DOMContentLoaded', () => {
    inventoryManager = new InventoryManager();
    
    // إضافة بيانات تجريبية إذا كان المخزون فارغ
    if (inventoryManager.items.length === 0) {
        addSampleData();
    }
});

// دوال إضافية
function exportTransactions() {
    const data = {
        transactions: inventoryManager.transactions,
        exportDate: new Date().toISOString(),
        company: inventoryManager.settings.companyName
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `transactions_${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
}

function exportInventory() {
    const data = {
        items: inventoryManager.items,
        exportDate: new Date().toISOString(),
        company: inventoryManager.settings.companyName
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `inventory_${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
}

function exportAllData() {
    const data = {
        items: inventoryManager.items,
        transactions: inventoryManager.transactions,
        projects: inventoryManager.projects,
        settings: inventoryManager.settings,
        exportDate: new Date().toISOString(),
        version: '2.0'
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `heiman_group_backup_${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
}

function filterTransactions() {
    const fromDate = document.getElementById('filterDateFrom').value;
    const toDate = document.getElementById('filterDateTo').value;

    if (!fromDate || !toDate) {
        inventoryManager.showAlert('يرجى اختيار تاريخ البداية والنهاية', 'danger');
        return;
    }

    const filtered = inventoryManager.transactions.filter(t => {
        const tDate = new Date(t.date).toDateString();
        return tDate >= new Date(fromDate).toDateString() &&
               tDate <= new Date(toDate).toDateString();
    });

    // عرض النتائج المفلترة
    const tbody = document.getElementById('transactionsTableBody');
    if (filtered.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="10" style="text-align: center; color: #7f8c8d;">
                    لا توجد معاملات في الفترة المحددة
                </td>
            </tr>
        `;
    } else {
        tbody.innerHTML = filtered.map(transaction => {
            const date = new Date(transaction.date);
            const typeColor = transaction.transactionType === 'وارد' ? '#2ecc71' :
                             transaction.transactionType === 'صادر' ? '#e74c3c' : '#f39c12';

            return `
                <tr>
                    <td>
                        <small>${date.toLocaleDateString('ar-EG')}</small><br>
                        <small style="color: #7f8c8d;">${date.toLocaleTimeString('ar-EG', {hour: '2-digit', minute: '2-digit'})}</small>
                    </td>
                    <td><strong>${transaction.itemName}</strong></td>
                    <td>
                        <span class="badge" style="background: ${typeColor}; color: white;">
                            ${transaction.transactionType}
                        </span>
                    </td>
                    <td>${transaction.quantity.toLocaleString('ar-EG')} ${transaction.unit}</td>
                    <td>${transaction.unitPrice.toLocaleString('ar-EG')} د.ع</td>
                    <td><strong>${transaction.totalPrice.toLocaleString('ar-EG')} د.ع</strong></td>
                    <td>${transaction.project}</td>
                    <td>${transaction.supplier}</td>
                    <td>${transaction.invoiceNumber || '-'}</td>
                    <td>
                        <button class="action-btn delete" onclick="deleteTransaction(${transaction.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    }
}

function searchInventory() {
    const searchTerm = document.getElementById('searchInventory').value.toLowerCase();
    if (!searchTerm) {
        inventoryManager.renderInventoryTable();
        return;
    }

    const filtered = inventoryManager.items.filter(item =>
        item.name.toLowerCase().includes(searchTerm) ||
        item.category.toLowerCase().includes(searchTerm) ||
        (item.location && item.location.toLowerCase().includes(searchTerm))
    );

    // عرض النتائج المفلترة
    const tbody = document.getElementById('inventoryTableBodyDetailed');
    if (filtered.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" style="text-align: center; color: #7f8c8d;">
                    لا توجد نتائج للبحث عن "${searchTerm}"
                </td>
            </tr>
        `;
    } else {
        tbody.innerHTML = filtered.map(item => {
            const totalValue = item.quantity * item.unitPrice;
            const status = inventoryManager.getItemStatus(item);
            const lastTransaction = item.lastTransaction ?
                `${item.lastTransaction.type} - ${new Date(item.lastTransaction.date).toLocaleDateString('ar-EG')}` :
                'لا توجد حركة';

            return `
                <tr>
                    <td>
                        <strong>${item.name}</strong>
                        <br><small style="color: #7f8c8d;">${item.category} - ${item.location || 'غير محدد'}</small>
                    </td>
                    <td>
                        <span style="font-weight: 600; font-size: 1.1rem; ${item.quantity <= item.minStock ? 'color: #e74c3c;' : ''}">
                            ${item.quantity.toLocaleString('ar-EG')}
                        </span>
                        <br><small style="color: #7f8c8d;">الحد الأدنى: ${item.minStock}</small>
                    </td>
                    <td>${item.unit}</td>
                    <td>
                        ${item.unitPrice.toLocaleString('ar-EG', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        })} د.ع
                    </td>
                    <td>
                        <strong>
                            ${totalValue.toLocaleString('ar-EG', {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            })} د.ع
                        </strong>
                    </td>
                    <td>
                        <small style="color: #7f8c8d;">${lastTransaction}</small>
                    </td>
                    <td>${status}</td>
                    <td>
                        <button class="action-btn edit" onclick="editItem(${item.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete" onclick="deleteItem(${item.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
    }
}

function saveCompanyInfo() {
    inventoryManager.settings.companyName = document.getElementById('companyName').value;
    inventoryManager.settings.companyAddress = document.getElementById('companyAddress').value;
    inventoryManager.settings.companyPhone = document.getElementById('companyPhone').value;
    inventoryManager.saveData();
    inventoryManager.showAlert('تم حفظ معلومات الشركة بنجاح!', 'success');
}

// دوال الحذف والتعديل
function deleteTransaction(id) {
    if (confirm('هل أنت متأكد من حذف هذه المعاملة؟')) {
        inventoryManager.transactions = inventoryManager.transactions.filter(t => t.id !== id);
        inventoryManager.saveData();
        inventoryManager.renderTransactionsTable();
        inventoryManager.showAlert('تم حذف المعاملة بنجاح!', 'success');
    }
}

function deleteItem(id) {
    if (confirm('هل أنت متأكد من حذف هذه المادة؟')) {
        inventoryManager.items = inventoryManager.items.filter(i => i.id !== id);
        inventoryManager.saveData();
        inventoryManager.renderInventoryTable();
        inventoryManager.updateStats();
        inventoryManager.showAlert('تم حذف المادة بنجاح!', 'success');
    }
}

function deleteProject(id) {
    if (confirm('هل أنت متأكد من حذف هذا المشروع؟')) {
        inventoryManager.projects = inventoryManager.projects.filter(p => p.id !== id);
        inventoryManager.saveData();
        inventoryManager.renderProjectsTable();
        inventoryManager.loadProjects();
        inventoryManager.showAlert('تم حذف المشروع بنجاح!', 'success');
    }
}

function editItem(id) {
    inventoryManager.showAlert('ميزة التعديل قيد التطوير', 'warning');
}

function editProject(id) {
    inventoryManager.showAlert('ميزة التعديل قيد التطوير', 'warning');
}

function addSampleData() {
    const sampleItems = [
        {
            id: 1,
            name: 'أسمنت',
            quantity: 100,
            unit: 'كيس',
            unitPrice: 8500,
            minStock: 20,
            category: 'مواد البناء',
            location: 'المخزن الرئيسي - الرف A1',
            created: new Date().toISOString(),
            lastUpdated: new Date().toISOString(),
            lastTransaction: {
                type: 'وارد',
                quantity: 100,
                date: new Date().toISOString(),
                project: 'المشروع الرئيسي'
            }
        },
        {
            id: 2,
            name: 'حديد تسليح',
            quantity: 50,
            unit: 'طن',
            unitPrice: 850000,
            minStock: 10,
            category: 'مواد البناء',
            location: 'المخزن الرئيسي - الرف B2',
            created: new Date().toISOString(),
            lastUpdated: new Date().toISOString(),
            lastTransaction: {
                type: 'وارد',
                quantity: 50,
                date: new Date().toISOString(),
                project: 'مشروع التوسعة'
            }
        },
        {
            id: 3,
            name: 'رمل',
            quantity: 8,
            unit: 'متر مكعب',
            unitPrice: 25000,
            minStock: 20,
            category: 'مواد البناء',
            location: 'المخزن الرئيسي - الرف C1',
            created: new Date().toISOString(),
            lastUpdated: new Date().toISOString(),
            lastTransaction: {
                type: 'صادر',
                quantity: 12,
                date: new Date().toISOString(),
                project: 'مشروع الصيانة'
            }
        }
    ];

    inventoryManager.items = sampleItems;
    inventoryManager.saveData();
    inventoryManager.updateStats();
    inventoryManager.renderInventoryTable();
}

// دوال الطباعة المؤقتة
function showPrintingSection(type) {
    // إخفاء جميع الأقسام
    document.querySelectorAll('.printing-section').forEach(section => {
        section.style.display = 'none';
    });

    // إظهار القسم المطلوب
    document.getElementById(`printing${type.charAt(0).toUpperCase() + type.slice(1)}`).style.display = 'block';
}

function getPrintingCount(type) {
    if (!inventoryManager) return 0;
    return inventoryManager.printingTables[type].length;
}

function printAllOutgoing() {
    printPrintingTable('outgoing', 'أوامر الصرف من المخزن');
}

function printAllIncoming() {
    printPrintingTable('incoming', 'أوامر الاستلام من المورد');
}

function printAllDamaged() {
    printPrintingTable('damaged', 'المواد التالفة');
}

function printAllReturned() {
    printPrintingTable('returned', 'المرتجعات');
}

function printPrintingTable(type, title) {
    const data = inventoryManager.printingTables[type];
    if (data.length === 0) {
        inventoryManager.showAlert('لا توجد بيانات للطباعة', 'warning');
        return;
    }

    // إنشاء نافذة طباعة
    const printWindow = window.open('', '_blank', 'width=800,height=600');
    const currentDate = new Date().toLocaleDateString('ar-EG');

    let tableRows = '';
    data.forEach(item => {
        const date = new Date(item.date).toLocaleDateString('ar-EG');
        tableRows += `
            <tr>
                <td>${date}</td>
                <td>${item.itemName}</td>
                <td>${item.quantity} ${item.unit}</td>
                <td>${item.supplier}</td>
                <td>${item.project}</td>
                <td>${item.notes || '-'}</td>
            </tr>
        `;
    });

    const printContent = `
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>${title} - هيمن كروب</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
                .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #000; padding-bottom: 20px; }
                .company-name { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
                .report-title { font-size: 18px; color: #555; }
                .date { margin-top: 10px; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #000; padding: 8px; text-align: center; }
                th { background-color: #f0f0f0; font-weight: bold; }
                .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
                @media print {
                    body { margin: 0; }
                    /* إخفاء URL من أسفل الصفحة */
                    @page {
                        margin: 0.5in;
                        @bottom-left { content: ""; }
                        @bottom-center { content: ""; }
                        @bottom-right { content: ""; }
                        @top-left { content: ""; }
                        @top-center { content: ""; }
                        @top-right { content: ""; }
                    }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <div class="company-name">هيمن كروب</div>
                <div class="report-title">${title}</div>
                <div class="date">التاريخ: ${currentDate}</div>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>المادة</th>
                        <th>الكمية</th>
                        <th>${type === 'outgoing' ? 'المستلم' : type === 'incoming' ? 'المورد' : 'المسؤول'}</th>
                        <th>المشروع</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    ${tableRows}
                </tbody>
            </table>

            <div class="footer">
                <p>تم إنشاء هذا التقرير بواسطة نظام إدارة المخزون - هيمن كروب</p>
                <p>عدد العناصر: ${data.length}</p>
            </div>

            <script>
                window.onload = function() {
                    window.print();
                    window.onafterprint = function() {
                        window.close();
                    };
                };
            </script>
        </body>
        </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();

    // مسح الجدول بعد الطباعة
    setTimeout(() => {
        if (confirm('تم إرسال الطباعة. هل تريد مسح جدول الطباعة؟')) {
            inventoryManager.clearPrintingTable(type);
        }
    }, 1000);
}

function clearPrintingTable(type) {
    inventoryManager.clearPrintingTable(type);
}
