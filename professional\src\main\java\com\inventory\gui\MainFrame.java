package com.inventory.gui;

import com.inventory.database.DatabaseManager;
import com.inventory.models.User;
import com.inventory.models.Category;
import com.inventory.models.Item;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * الواجهة الرئيسية الاحترافية - نظام إدارة المخزون الاحترافي
 */
public class MainFrame extends JFrame {
    private User currentUser;
    private DatabaseManager dbManager;
    
    // مكونات الواجهة
    private JMenuBar menuBar;
    private JToolBar toolBar;
    private JTabbedPane tabbedPane;
    private JPanel statusPanel;
    private JLabel statusLabel;
    private JLabel userLabel;
    private JLabel timeLabel;
    
    // التبويبات
    private JPanel dashboardPanel;
    private JPanel inventoryPanel;
    private JPanel transactionsPanel;
    private JPanel reportsPanel;
    private JPanel settingsPanel;
    
    public MainFrame(User user) {
        this.currentUser = user;
        this.dbManager = DatabaseManager.getInstance();
        
        initializeComponents();
        setupLayout();
        setupMenuBar();
        setupToolBar();
        setupStatusBar();
        setupEventHandlers();
        setupFrame();
        
        // تحديث الوقت كل ثانية
        startTimeUpdater();
        
        // تحميل البيانات الأولية
        loadInitialData();
    }
    
    private void initializeComponents() {
        // إعداد الخط العربي
        Font arabicFont = new Font("Segoe UI", Font.PLAIN, 14);
        Font titleFont = new Font("Segoe UI", Font.BOLD, 16);
        
        // التبويبات الرئيسية
        tabbedPane = new JTabbedPane();
        tabbedPane.setFont(arabicFont);
        tabbedPane.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // إنشاء التبويبات
        dashboardPanel = createDashboardPanel();
        inventoryPanel = createInventoryPanel();
        transactionsPanel = createTransactionsPanel();
        reportsPanel = createReportsPanel();
        settingsPanel = createSettingsPanel();
        
        // إضافة التبويبات
        tabbedPane.addTab("لوحة التحكم", createIcon("dashboard"), dashboardPanel, "عرض إحصائيات النظام");
        tabbedPane.addTab("المخزون", createIcon("inventory"), inventoryPanel, "إدارة المواد والمخزون");
        tabbedPane.addTab("المعاملات", createIcon("transactions"), transactionsPanel, "سجل المعاملات");
        tabbedPane.addTab("التقارير", createIcon("reports"), reportsPanel, "التقارير والإحصائيات");
        
        // إضافة تبويب الإعدادات للمديرين فقط
        if (currentUser.hasPermission("ADMIN_SETTINGS")) {
            tabbedPane.addTab("الإعدادات", createIcon("settings"), settingsPanel, "إعدادات النظام");
        }
    }
    
    private Icon createIcon(String type) {
        // إنشاء أيقونات ملونة بسيطة
        return new ColorIcon(getIconColor(type), 16, 16);
    }
    
    private Color getIconColor(String type) {
        switch (type) {
            case "dashboard": return new Color(33, 150, 243);
            case "inventory": return new Color(76, 175, 80);
            case "transactions": return new Color(255, 152, 0);
            case "reports": return new Color(156, 39, 176);
            case "settings": return new Color(96, 125, 139);
            default: return new Color(117, 117, 117);
        }
    }
    
    private JPanel createDashboardPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(new EmptyBorder(20, 20, 20, 20));
        
        // العنوان الترحيبي
        JLabel welcomeLabel = new JLabel("مرحباً " + currentUser.getFullName());
        welcomeLabel.setFont(new Font("Segoe UI", Font.BOLD, 24));
        welcomeLabel.setForeground(new Color(33, 150, 243));
        welcomeLabel.setHorizontalAlignment(SwingConstants.CENTER);
        
        // لوحة الإحصائيات
        JPanel statsPanel = new JPanel(new GridLayout(2, 2, 20, 20));
        statsPanel.setBorder(new EmptyBorder(30, 0, 30, 0));
        
        // بطاقات الإحصائيات
        statsPanel.add(createStatCard("إجمالي المواد", "0", new Color(33, 150, 243)));
        statsPanel.add(createStatCard("المواد المنخفضة", "0", new Color(244, 67, 54)));
        statsPanel.add(createStatCard("المعاملات اليوم", "0", new Color(76, 175, 80)));
        statsPanel.add(createStatCard("القيمة الإجمالية", "0 د.ع", new Color(255, 152, 0)));
        
        // لوحة الأنشطة الأخيرة
        JPanel activityPanel = new JPanel(new BorderLayout());
        activityPanel.setBorder(BorderFactory.createTitledBorder("الأنشطة الأخيرة"));
        
        JTextArea activityArea = new JTextArea(8, 50);
        activityArea.setEditable(false);
        activityArea.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        activityArea.setText("لا توجد أنشطة حديثة...");
        
        JScrollPane activityScroll = new JScrollPane(activityArea);
        activityPanel.add(activityScroll, BorderLayout.CENTER);
        
        panel.add(welcomeLabel, BorderLayout.NORTH);
        panel.add(statsPanel, BorderLayout.CENTER);
        panel.add(activityPanel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    private JPanel createStatCard(String title, String value, Color color) {
        JPanel card = new JPanel();
        card.setLayout(new BoxLayout(card, BoxLayout.Y_AXIS));
        card.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(color, 2),
            new EmptyBorder(15, 15, 15, 15)
        ));
        card.setBackground(Color.WHITE);
        
        JLabel titleLabel = new JLabel(title);
        titleLabel.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        titleLabel.setForeground(new Color(117, 117, 117));
        titleLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        
        JLabel valueLabel = new JLabel(value);
        valueLabel.setFont(new Font("Segoe UI", Font.BOLD, 28));
        valueLabel.setForeground(color);
        valueLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        
        card.add(titleLabel);
        card.add(Box.createVerticalStrut(10));
        card.add(valueLabel);
        
        return card;
    }
    
    private JPanel createInventoryPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(new EmptyBorder(10, 10, 10, 10));
        
        // شريط الأدوات
        JPanel toolbarPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        
        JButton addItemButton = new JButton("إضافة مادة جديدة");
        addItemButton.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        addItemButton.setBackground(new Color(76, 175, 80));
        addItemButton.setForeground(Color.WHITE);
        addItemButton.setFocusPainted(false);
        
        JButton editItemButton = new JButton("تعديل");
        editItemButton.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        editItemButton.setBackground(new Color(255, 152, 0));
        editItemButton.setForeground(Color.WHITE);
        editItemButton.setFocusPainted(false);
        
        JButton deleteItemButton = new JButton("حذف");
        deleteItemButton.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        deleteItemButton.setBackground(new Color(244, 67, 54));
        deleteItemButton.setForeground(Color.WHITE);
        deleteItemButton.setFocusPainted(false);
        
        toolbarPanel.add(addItemButton);
        toolbarPanel.add(editItemButton);
        toolbarPanel.add(deleteItemButton);
        
        // جدول المواد
        String[] columns = {"الرقم", "اسم المادة", "الوصف", "الفئة", "الوحدة", "المخزون الحالي", "الحد الأدنى", "سعر الوحدة", "القيمة الإجمالية", "الحالة"};
        Object[][] data = {}; // سيتم تحميلها لاحقاً
        
        JTable itemsTable = new JTable(data, columns);
        itemsTable.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        itemsTable.getTableHeader().setFont(new Font("Segoe UI", Font.BOLD, 12));
        itemsTable.setRowHeight(25);
        itemsTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        
        JScrollPane tableScroll = new JScrollPane(itemsTable);
        
        panel.add(toolbarPanel, BorderLayout.NORTH);
        panel.add(tableScroll, BorderLayout.CENTER);
        
        return panel;
    }
    
    private JPanel createTransactionsPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(new EmptyBorder(10, 10, 10, 10));
        
        JLabel label = new JLabel("سجل المعاملات - قيد التطوير", SwingConstants.CENTER);
        label.setFont(new Font("Segoe UI", Font.BOLD, 18));
        label.setForeground(new Color(117, 117, 117));
        
        panel.add(label, BorderLayout.CENTER);
        return panel;
    }
    
    private JPanel createReportsPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(new EmptyBorder(10, 10, 10, 10));
        
        JLabel label = new JLabel("التقارير والإحصائيات - قيد التطوير", SwingConstants.CENTER);
        label.setFont(new Font("Segoe UI", Font.BOLD, 18));
        label.setForeground(new Color(117, 117, 117));
        
        panel.add(label, BorderLayout.CENTER);
        return panel;
    }
    
    private JPanel createSettingsPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(new EmptyBorder(10, 10, 10, 10));
        
        JLabel label = new JLabel("إعدادات النظام - قيد التطوير", SwingConstants.CENTER);
        label.setFont(new Font("Segoe UI", Font.BOLD, 18));
        label.setForeground(new Color(117, 117, 117));
        
        panel.add(label, BorderLayout.CENTER);
        return panel;
    }
    
    private void setupLayout() {
        setLayout(new BorderLayout());
        add(tabbedPane, BorderLayout.CENTER);
    }
    
    private void setupMenuBar() {
        menuBar = new JMenuBar();
        menuBar.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // قائمة الملف
        JMenu fileMenu = new JMenu("ملف");
        fileMenu.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        
        JMenuItem exitItem = new JMenuItem("خروج");
        exitItem.addActionListener(e -> exitApplication());
        fileMenu.add(exitItem);
        
        // قائمة المساعدة
        JMenu helpMenu = new JMenu("مساعدة");
        helpMenu.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        
        JMenuItem aboutItem = new JMenuItem("حول البرنامج");
        aboutItem.addActionListener(e -> showAboutDialog());
        helpMenu.add(aboutItem);
        
        menuBar.add(fileMenu);
        menuBar.add(helpMenu);
        
        setJMenuBar(menuBar);
    }
    
    private void setupToolBar() {
        toolBar = new JToolBar();
        toolBar.setFloatable(false);
        toolBar.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // أزرار سريعة
        JButton quickAddButton = new JButton("إضافة سريعة");
        quickAddButton.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        
        JButton refreshButton = new JButton("تحديث");
        refreshButton.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        
        toolBar.add(quickAddButton);
        toolBar.addSeparator();
        toolBar.add(refreshButton);
        
        add(toolBar, BorderLayout.NORTH);
    }
    
    private void setupStatusBar() {
        statusPanel = new JPanel(new BorderLayout());
        statusPanel.setBorder(BorderFactory.createEtchedBorder());
        statusPanel.setPreferredSize(new Dimension(0, 25));
        
        statusLabel = new JLabel("جاهز");
        statusLabel.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        statusLabel.setBorder(new EmptyBorder(2, 10, 2, 10));
        
        userLabel = new JLabel("المستخدم: " + currentUser.getFullName() + " (" + currentUser.getRole().getArabicName() + ")");
        userLabel.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        userLabel.setBorder(new EmptyBorder(2, 10, 2, 10));
        
        timeLabel = new JLabel();
        timeLabel.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        timeLabel.setBorder(new EmptyBorder(2, 10, 2, 10));
        
        statusPanel.add(statusLabel, BorderLayout.WEST);
        statusPanel.add(userLabel, BorderLayout.CENTER);
        statusPanel.add(timeLabel, BorderLayout.EAST);
        
        add(statusPanel, BorderLayout.SOUTH);
    }
    
    private void setupEventHandlers() {
        // إغلاق التطبيق
        setDefaultCloseOperation(JFrame.DO_NOTHING_ON_CLOSE);
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent e) {
                exitApplication();
            }
        });
    }
    
    private void setupFrame() {
        setTitle("نظام إدارة المخزون الاحترافي - " + currentUser.getFullName());
        setSize(1200, 800);
        setLocationRelativeTo(null);
        setExtendedState(JFrame.MAXIMIZED_BOTH);
    }
    
    private void startTimeUpdater() {
        Timer timer = new Timer(1000, e -> {
            String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            timeLabel.setText(currentTime);
        });
        timer.start();
    }
    
    private void loadInitialData() {
        // تحميل البيانات الأولية في خيط منفصل
        SwingWorker<Void, Void> worker = new SwingWorker<Void, Void>() {
            @Override
            protected Void doInBackground() throws Exception {
                // تحميل الفئات
                List<Category> categories = dbManager.getAllCategories();
                
                // تحميل المواد
                List<Item> items = dbManager.getAllItems();
                
                return null;
            }
            
            @Override
            protected void done() {
                statusLabel.setText("تم تحميل البيانات بنجاح");
            }
        };
        
        worker.execute();
    }
    
    private void exitApplication() {
        int option = JOptionPane.showConfirmDialog(
            this,
            "هل تريد إغلاق التطبيق؟",
            "تأكيد الإغلاق",
            JOptionPane.YES_NO_OPTION,
            JOptionPane.QUESTION_MESSAGE
        );
        
        if (option == JOptionPane.YES_OPTION) {
            dbManager.closeConnection();
            System.exit(0);
        }
    }
    
    private void showAboutDialog() {
        String message = """
            نظام إدارة المخزون الاحترافي
            الإصدار 2.0
            
            تم تطوير هذا النظام باستخدام:
            • Java Swing للواجهة
            • SQLite لقاعدة البيانات
            • تشفير متقدم للأمان
            
            جميع الحقوق محفوظة © 2024
            """;
        
        JOptionPane.showMessageDialog(
            this,
            message,
            "حول البرنامج",
            JOptionPane.INFORMATION_MESSAGE
        );
    }
    
    // فئة مساعدة لإنشاء أيقونات ملونة
    private static class ColorIcon implements Icon {
        private Color color;
        private int width;
        private int height;
        
        public ColorIcon(Color color, int width, int height) {
            this.color = color;
            this.width = width;
            this.height = height;
        }
        
        @Override
        public void paintIcon(Component c, Graphics g, int x, int y) {
            Graphics2D g2d = (Graphics2D) g.create();
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setColor(color);
            g2d.fillOval(x, y, width, height);
            g2d.dispose();
        }
        
        @Override
        public int getIconWidth() {
            return width;
        }
        
        @Override
        public int getIconHeight() {
            return height;
        }
    }
}
