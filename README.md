# نظام إدارة المخزون - هيمن كروب

## نظرة عامة
نظام شامل لإدارة المخزون والمعاملات المالية مصمم خصيصاً لشركة هيمن كروب.

## الميزات

### الوظائف الأساسية
- ✅ تسجيل المواد الداخلة (الوارد)
- ✅ تسجيل المواد المصروفة (الصادر)
- ✅ متابعة الرصيد المتبقي تلقائياً
- ✅ ربط كل عملية بالمشروع والمورد/المستلم
- ✅ إمكانية التصفية والبحث
- ✅ واجهة بسيطة وسهلة باللغة العربية

### البيانات المدعومة
- اسم المادة
- الكمية
- الوحدة (كيلو، متر، قطعة، إلخ)
- نوع الحركة (وارد/صادر)
- التاريخ (تلقائي)
- المشروع
- المورد/المستلم
- السعر بالدينار
- ملاحظات

## كيفية التشغيل

### المتطلبات
- Java 8 أو أحدث
- نظام التشغيل Windows

### خطوات التشغيل

1. **تجميع المشروع:**
   ```
   compile.bat
   ```

2. **تشغيل التطبيق:**
   ```
   run.bat
   ```

## كيفية الاستخدام

### 1. إدخال البيانات
- انتقل إلى تبويب "إدخال البيانات"
- املأ المعلومات المطلوبة:
  - اسم المادة (مطلوب)
  - الكمية (مطلوب)
  - الوحدة (مطلوب)
  - اختر نوع الحركة: وارد أو صادر
  - أدخل المشروع
  - أدخل اسم المورد (للوارد) أو المستلم (للصادر)
  - أدخل السعر بالدينار
  - أضف ملاحظات إضافية إذا لزم الأمر
- اضغط "حفظ"

### 2. عرض المخزون الحالي
- انتقل إلى تبويب "المخزون الحالي"
- ستجد جدول يعرض:
  - اسم المادة
  - الوحدة
  - الكمية المتاحة
  - سعر الوحدة
  - القيمة الإجمالية
  - الملاحظات
- اضغط "تحديث" لتحديث البيانات

### 3. مراجعة سجل المعاملات
- انتقل إلى تبويب "سجل المعاملات"
- ستجد جدول يعرض جميع العمليات مرتبة حسب التاريخ
- يتضمن تفاصيل كل معاملة

### 4. البحث
- انتقل إلى تبويب "البحث"
- أدخل كلمة البحث (يمكن البحث في اسم المادة، المشروع، أو المورد/المستلم)
- اضغط "بحث"
- ستظهر النتائج في الجدول

## هيكل المشروع

```
HRF/
├── src/
│   ├── Main.java                 # نقطة البداية
│   ├── models/
│   │   ├── Item.java            # نموذج المادة
│   │   └── Transaction.java     # نموذج المعاملة
│   ├── database/
│   │   └── DatabaseManager.java # إدارة قاعدة البيانات
│   └── gui/
│       └── InventoryGUI.java    # الواجهة الرسومية
├── lib/                         # المكتبات المطلوبة
├── out/                         # الملفات المجمعة
├── compile.bat                  # ملف التجميع
├── run.bat                      # ملف التشغيل
└── inventory.db                 # قاعدة البيانات (تُنشأ تلقائياً)
```

## قاعدة البيانات

يستخدم النظام قاعدة بيانات SQLite محلية تُنشأ تلقائياً عند أول تشغيل. تحتوي على جدولين:

1. **جدول المواد (items):** يحفظ معلومات المواد والمخزون الحالي
2. **جدول المعاملات (transactions):** يحفظ سجل جميع العمليات

## الملاحظات

- النظام يحسب المخزون تلقائياً بناءً على العمليات
- جميع التواريخ تُسجل تلقائياً
- يمكن إضافة نفس المادة عدة مرات بأسعار مختلفة
- البحث يعمل في جميع الحقول النصية
- النظام يدعم الأرقام العشرية للكميات والأسعار

## الدعم الفني

في حالة وجود مشاكل:
1. تأكد من تثبيت Java بشكل صحيح
2. تأكد من وجود جميع ملفات المكتبات في مجلد `lib`
3. تحقق من أن قاعدة البيانات غير مفتوحة في برنامج آخر

---

**تم تطوير هذا النظام بـ Java مع Swing لواجهة المستخدم و SQLite لقاعدة البيانات**
