package com.inventory.models;

import java.time.LocalDateTime;

/**
 * نموذج المستخدم - نظام إدارة المخزون الاحترافي
 */
public class User {
    private int id;
    private String username;
    private String password; // سيتم تشفيرها
    private String fullName;
    private String email;
    private UserRole role;
    private boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime lastLogin;
    
    public enum UserRole {
        ADMIN("مدير النظام"),
        MANAGER("مدير المخزون"),
        EMPLOYEE("موظف"),
        VIEWER("مشاهد");
        
        private final String arabicName;
        
        UserRole(String arabicName) {
            this.arabicName = arabicName;
        }
        
        public String getArabicName() {
            return arabicName;
        }
    }
    
    // Constructors
    public User() {
        this.createdAt = LocalDateTime.now();
        this.isActive = true;
    }
    
    public User(String username, String password, String fullName, String email, UserRole role) {
        this();
        this.username = username;
        this.password = password;
        this.fullName = fullName;
        this.email = email;
        this.role = role;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getFullName() {
        return fullName;
    }
    
    public void setFullName(String fullName) {
        this.fullName = fullName;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public UserRole getRole() {
        return role;
    }
    
    public void setRole(UserRole role) {
        this.role = role;
    }
    
    public boolean isActive() {
        return isActive;
    }
    
    public void setActive(boolean active) {
        isActive = active;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getLastLogin() {
        return lastLogin;
    }
    
    public void setLastLogin(LocalDateTime lastLogin) {
        this.lastLogin = lastLogin;
    }
    
    // Helper methods
    public boolean hasPermission(String permission) {
        switch (role) {
            case ADMIN:
                return true;
            case MANAGER:
                return !permission.equals("USER_MANAGEMENT");
            case EMPLOYEE:
                return permission.equals("ADD_TRANSACTION") || 
                       permission.equals("VIEW_INVENTORY") ||
                       permission.equals("VIEW_REPORTS");
            case VIEWER:
                return permission.equals("VIEW_INVENTORY") ||
                       permission.equals("VIEW_REPORTS");
            default:
                return false;
        }
    }
    
    @Override
    public String toString() {
        return fullName + " (" + username + ")";
    }
}
