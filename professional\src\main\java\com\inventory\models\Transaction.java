package com.inventory.models;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * نموذج المعاملة المحسن - نظام إدارة المخزون الاحترافي
 */
public class Transaction {
    private int id;
    private int itemId;
    private Item item;
    private double quantity;
    private TransactionType type;
    private double unitPrice;
    private double totalPrice;
    private String project;
    private String supplierOrReceiver;
    private String referenceNumber; // رقم مرجعي
    private String invoiceNumber; // رقم الفاتورة
    private LocalDateTime transactionDate;
    private int userId;
    private User user;
    private String notes;
    private TransactionStatus status;
    private LocalDateTime createdAt;
    
    public enum TransactionType {
        IN("وارد", "دخول"),
        OUT("صادر", "خروج"),
        ADJUSTMENT("تسوية", "تعديل"),
        TRANSFER("نقل", "تحويل");
        
        private final String arabicName;
        private final String description;
        
        TransactionType(String arabicName, String description) {
            this.arabicName = arabicName;
            this.description = description;
        }
        
        public String getArabicName() {
            return arabicName;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public enum TransactionStatus {
        PENDING("معلق"),
        APPROVED("مؤكد"),
        CANCELLED("ملغي");
        
        private final String arabicName;
        
        TransactionStatus(String arabicName) {
            this.arabicName = arabicName;
        }
        
        public String getArabicName() {
            return arabicName;
        }
    }
    
    // Constructors
    public Transaction() {
        this.transactionDate = LocalDateTime.now();
        this.createdAt = LocalDateTime.now();
        this.status = TransactionStatus.PENDING;
    }
    
    public Transaction(int itemId, double quantity, TransactionType type, 
                      double unitPrice, String project, String supplierOrReceiver, int userId) {
        this();
        this.itemId = itemId;
        this.quantity = quantity;
        this.type = type;
        this.unitPrice = unitPrice;
        this.totalPrice = quantity * unitPrice;
        this.project = project;
        this.supplierOrReceiver = supplierOrReceiver;
        this.userId = userId;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public int getItemId() {
        return itemId;
    }
    
    public void setItemId(int itemId) {
        this.itemId = itemId;
    }
    
    public Item getItem() {
        return item;
    }
    
    public void setItem(Item item) {
        this.item = item;
        if (item != null) {
            this.itemId = item.getId();
        }
    }
    
    public double getQuantity() {
        return quantity;
    }
    
    public void setQuantity(double quantity) {
        this.quantity = quantity;
        this.totalPrice = quantity * unitPrice;
    }
    
    public TransactionType getType() {
        return type;
    }
    
    public void setType(TransactionType type) {
        this.type = type;
    }
    
    public double getUnitPrice() {
        return unitPrice;
    }
    
    public void setUnitPrice(double unitPrice) {
        this.unitPrice = unitPrice;
        this.totalPrice = quantity * unitPrice;
    }
    
    public double getTotalPrice() {
        return totalPrice;
    }
    
    public void setTotalPrice(double totalPrice) {
        this.totalPrice = totalPrice;
    }
    
    public String getProject() {
        return project;
    }
    
    public void setProject(String project) {
        this.project = project;
    }
    
    public String getSupplierOrReceiver() {
        return supplierOrReceiver;
    }
    
    public void setSupplierOrReceiver(String supplierOrReceiver) {
        this.supplierOrReceiver = supplierOrReceiver;
    }
    
    public String getReferenceNumber() {
        return referenceNumber;
    }
    
    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }
    
    public String getInvoiceNumber() {
        return invoiceNumber;
    }
    
    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }
    
    public LocalDateTime getTransactionDate() {
        return transactionDate;
    }
    
    public void setTransactionDate(LocalDateTime transactionDate) {
        this.transactionDate = transactionDate;
    }
    
    public String getFormattedDate() {
        return transactionDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
    }
    
    public int getUserId() {
        return userId;
    }
    
    public void setUserId(int userId) {
        this.userId = userId;
    }
    
    public User getUser() {
        return user;
    }
    
    public void setUser(User user) {
        this.user = user;
        if (user != null) {
            this.userId = user.getId();
        }
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    public TransactionStatus getStatus() {
        return status;
    }
    
    public void setStatus(TransactionStatus status) {
        this.status = status;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    // Helper methods
    public boolean isIncoming() {
        return type == TransactionType.IN;
    }
    
    public boolean isOutgoing() {
        return type == TransactionType.OUT;
    }
    
    public double getStockImpact() {
        if (status != TransactionStatus.APPROVED) {
            return 0.0;
        }
        
        switch (type) {
            case IN:
                return quantity;
            case OUT:
                return -quantity;
            case ADJUSTMENT:
                return quantity; // يمكن أن تكون موجبة أو سالبة
            case TRANSFER:
                return 0.0; // لا تؤثر على المخزون الإجمالي
            default:
                return 0.0;
        }
    }
    
    @Override
    public String toString() {
        return String.format("%s - %s (%.2f %s)", 
            getFormattedDate(), 
            type.getArabicName(), 
            quantity, 
            item != null ? item.getUnit() : "");
    }
}
