@echo off
chcp 65001 >nul
title نظام إدارة المخزون - هيمن كروب (القائمة الرئيسية)
color 0A

:menu
cls
echo.
echo ========================================
echo    نظام إدارة المخزون - هيمن كروب
echo           القائمة الرئيسية
echo ========================================
echo.
echo 🎯 اختر ما تريد فعله:
echo.
echo [1] 🚀 تشغيل النظام
echo [2] 🔨 إنشاء ملف .exe
echo [3] 🎨 إضافة أيقونة مخصصة
echo [4] 🔄 تحديث النظام
echo [5] 💾 إنشاء نسخة احتياطية
echo [6] 📊 معلومات النظام
echo [7] 📖 دليل الاستخدام
echo [8] 🚪 خروج
echo.
set /p choice="اختر رقم (1-8): "

if "%choice%"=="1" goto run_system
if "%choice%"=="2" goto build_exe
if "%choice%"=="3" goto add_icon
if "%choice%"=="4" goto update_system
if "%choice%"=="5" goto backup
if "%choice%"=="6" goto system_info
if "%choice%"=="7" goto show_guide
if "%choice%"=="8" goto exit
goto invalid

:run_system
cls
echo.
echo 🚀 تشغيل النظام...
echo ==================
call "شغل-النظام.bat"
goto menu

:build_exe
cls
echo.
echo 🔨 إنشاء ملف .exe...
echo ====================
call "اعمل-كل-شي.bat"
goto menu

:add_icon
cls
echo.
echo 🎨 إضافة أيقونة مخصصة...
echo ==========================
call "اضافة-ايقونة.bat"
goto menu

:update_system
cls
echo.
echo 🔄 تحديث النظام...
echo ==================
call "تحديث-النظام.bat"
goto menu

:backup
cls
echo.
echo 💾 إنشاء نسخة احتياطية...
echo ===========================
call "نسخ-احتياطي.bat"
goto menu

:system_info
cls
echo.
echo 📊 معلومات النظام...
echo ====================
call "معلومات-النظام.bat"
goto menu

:show_guide
cls
echo.
echo 📖 دليل الاستخدام...
echo ====================
if exist "اقرأ-هذا-أولاً.txt" (
    type "اقرأ-هذا-أولاً.txt"
) else (
    echo ❌ ملف الدليل غير موجود!
)
echo.
pause
goto menu

:invalid
echo.
echo ❌ اختيار غير صحيح!
echo يرجى اختيار رقم من 1 إلى 8
pause
goto menu

:exit
cls
echo.
echo ========================================
echo    شكراً لاستخدام نظام إدارة المخزون
echo              هيمن كروب - 2024
echo ========================================
echo.
echo 🎉 تم إنجاز نظام احترافي متكامل!
echo.
echo ✅ الميزات المنجزة:
echo    • نظام مخزون شامل
echo    • نظام مالي متقدم
echo    • إدارة المحروقات
echo    • تقارير احترافية
echo    • طباعة بدون مشاكل
echo    • ملف .exe قابل للتوزيع
echo.
echo 💡 للعودة: شغل "القائمة-الرئيسية.bat"
echo.
timeout /t 5 >nul
exit
