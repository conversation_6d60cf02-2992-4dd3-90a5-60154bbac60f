package com.inventory.gui;

import com.inventory.database.DatabaseManager;
import com.inventory.models.User;
import com.inventory.utils.PasswordUtils;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.KeyEvent;
import java.awt.event.KeyListener;

/**
 * نافذة تسجيل الدخول الاحترافية - نظام إدارة المخزون الاحترافي
 */
public class LoginDialog extends JDialog {
    private JTextField usernameField;
    private JPasswordField passwordField;
    private JButton loginButton;
    private JButton cancelButton;
    private JLabel statusLabel;
    private User authenticatedUser;
    private DatabaseManager dbManager;
    
    public LoginDialog(Frame parent) {
        super(parent, "تسجيل الدخول - نظام إدارة المخزون الاحترافي", true);
        dbManager = DatabaseManager.getInstance();
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        setupDialog();
    }
    
    private void initializeComponents() {
        // تعيين الخط العربي
        Font arabicFont = new Font("Segoe UI", Font.PLAIN, 14);
        Font titleFont = new Font("Segoe UI", Font.BOLD, 18);
        
        // الحقول
        usernameField = new JTextField(20);
        usernameField.setFont(arabicFont);
        usernameField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        passwordField = new JPasswordField(20);
        passwordField.setFont(arabicFont);
        passwordField.setComponentOrientation(ComponentOrientation.RIGHT_TO_LEFT);
        
        // الأزرار
        loginButton = new JButton("دخول");
        loginButton.setFont(arabicFont);
        loginButton.setBackground(new Color(33, 150, 243));
        loginButton.setForeground(Color.WHITE);
        loginButton.setFocusPainted(false);
        loginButton.setBorderPainted(false);
        loginButton.setPreferredSize(new Dimension(100, 35));
        
        cancelButton = new JButton("إلغاء");
        cancelButton.setFont(arabicFont);
        cancelButton.setBackground(new Color(158, 158, 158));
        cancelButton.setForeground(Color.WHITE);
        cancelButton.setFocusPainted(false);
        cancelButton.setBorderPainted(false);
        cancelButton.setPreferredSize(new Dimension(100, 35));
        
        // تسمية الحالة
        statusLabel = new JLabel(" ");
        statusLabel.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        statusLabel.setForeground(Color.RED);
        statusLabel.setHorizontalAlignment(SwingConstants.CENTER);
    }
    
    private void setupLayout() {
        setLayout(new BorderLayout());
        
        // اللوحة الرئيسية
        JPanel mainPanel = new JPanel();
        mainPanel.setLayout(new BoxLayout(mainPanel, BoxLayout.Y_AXIS));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(30, 40, 30, 40));
        mainPanel.setBackground(Color.WHITE);
        
        // العنوان
        JLabel titleLabel = new JLabel("نظام إدارة المخزون الاحترافي");
        titleLabel.setFont(new Font("Segoe UI", Font.BOLD, 20));
        titleLabel.setForeground(new Color(33, 150, 243));
        titleLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        titleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        
        JLabel subtitleLabel = new JLabel("يرجى تسجيل الدخول للمتابعة");
        subtitleLabel.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        subtitleLabel.setForeground(new Color(117, 117, 117));
        subtitleLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        subtitleLabel.setHorizontalAlignment(SwingConstants.CENTER);
        
        // لوحة الحقول
        JPanel fieldsPanel = new JPanel(new GridBagLayout());
        fieldsPanel.setBackground(Color.WHITE);
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        gbc.anchor = GridBagConstraints.EAST;
        
        // اسم المستخدم
        gbc.gridx = 1; gbc.gridy = 0;
        JLabel usernameLabel = new JLabel("اسم المستخدم:");
        usernameLabel.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        fieldsPanel.add(usernameLabel, gbc);
        
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        fieldsPanel.add(usernameField, gbc);
        
        // كلمة المرور
        gbc.gridx = 1; gbc.gridy = 1; gbc.fill = GridBagConstraints.NONE;
        JLabel passwordLabel = new JLabel("كلمة المرور:");
        passwordLabel.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        fieldsPanel.add(passwordLabel, gbc);
        
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        fieldsPanel.add(passwordField, gbc);
        
        // لوحة الأزرار
        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.setBackground(Color.WHITE);
        buttonPanel.add(loginButton);
        buttonPanel.add(Box.createHorizontalStrut(10));
        buttonPanel.add(cancelButton);
        
        // معلومات المدير الافتراضي
        JPanel infoPanel = new JPanel();
        infoPanel.setLayout(new BoxLayout(infoPanel, BoxLayout.Y_AXIS));
        infoPanel.setBackground(new Color(245, 245, 245));
        infoPanel.setBorder(BorderFactory.createTitledBorder("معلومات المدير الافتراضي"));
        
        JLabel adminInfoLabel1 = new JLabel("اسم المستخدم: admin");
        adminInfoLabel1.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        adminInfoLabel1.setAlignmentX(Component.CENTER_ALIGNMENT);
        
        JLabel adminInfoLabel2 = new JLabel("كلمة المرور: admin123");
        adminInfoLabel2.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        adminInfoLabel2.setAlignmentX(Component.CENTER_ALIGNMENT);
        
        infoPanel.add(adminInfoLabel1);
        infoPanel.add(adminInfoLabel2);
        
        // تجميع المكونات
        mainPanel.add(titleLabel);
        mainPanel.add(Box.createVerticalStrut(5));
        mainPanel.add(subtitleLabel);
        mainPanel.add(Box.createVerticalStrut(30));
        mainPanel.add(fieldsPanel);
        mainPanel.add(Box.createVerticalStrut(10));
        mainPanel.add(statusLabel);
        mainPanel.add(Box.createVerticalStrut(20));
        mainPanel.add(buttonPanel);
        mainPanel.add(Box.createVerticalStrut(20));
        mainPanel.add(infoPanel);
        
        add(mainPanel, BorderLayout.CENTER);
    }
    
    private void setupEventHandlers() {
        loginButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                performLogin();
            }
        });
        
        cancelButton.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                dispose();
                System.exit(0);
            }
        });
        
        // تسجيل الدخول بالضغط على Enter
        KeyListener enterKeyListener = new KeyListener() {
            @Override
            public void keyPressed(KeyEvent e) {
                if (e.getKeyCode() == KeyEvent.VK_ENTER) {
                    performLogin();
                }
            }
            
            @Override
            public void keyTyped(KeyEvent e) {}
            
            @Override
            public void keyReleased(KeyEvent e) {}
        };
        
        usernameField.addKeyListener(enterKeyListener);
        passwordField.addKeyListener(enterKeyListener);
    }
    
    private void setupDialog() {
        setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);
        setResizable(false);
        pack();
        setLocationRelativeTo(getParent());
        
        // تركيز على حقل اسم المستخدم
        SwingUtilities.invokeLater(() -> usernameField.requestFocus());
    }
    
    private void performLogin() {
        String username = usernameField.getText().trim();
        String password = new String(passwordField.getPassword());
        
        if (username.isEmpty() || password.isEmpty()) {
            showStatus("يرجى إدخال اسم المستخدم وكلمة المرور", Color.RED);
            return;
        }
        
        // تعطيل الأزرار أثناء المعالجة
        loginButton.setEnabled(false);
        loginButton.setText("جاري التحقق...");
        statusLabel.setText("جاري التحقق من البيانات...");
        statusLabel.setForeground(new Color(33, 150, 243));
        
        // تنفيذ المصادقة في خيط منفصل
        SwingWorker<User, Void> worker = new SwingWorker<User, Void>() {
            @Override
            protected User doInBackground() throws Exception {
                Thread.sleep(500); // محاكاة وقت المعالجة
                return dbManager.authenticateUser(username, password);
            }
            
            @Override
            protected void done() {
                try {
                    User user = get();
                    if (user != null) {
                        authenticatedUser = user;
                        showStatus("تم تسجيل الدخول بنجاح", new Color(76, 175, 80));
                        
                        // إغلاق النافذة بعد تأخير قصير
                        Timer timer = new Timer(1000, e -> dispose());
                        timer.setRepeats(false);
                        timer.start();
                    } else {
                        showStatus("اسم المستخدم أو كلمة المرور غير صحيحة", Color.RED);
                        passwordField.setText("");
                        passwordField.requestFocus();
                    }
                } catch (Exception e) {
                    showStatus("خطأ في الاتصال بقاعدة البيانات", Color.RED);
                    e.printStackTrace();
                } finally {
                    loginButton.setEnabled(true);
                    loginButton.setText("دخول");
                }
            }
        };
        
        worker.execute();
    }
    
    private void showStatus(String message, Color color) {
        statusLabel.setText(message);
        statusLabel.setForeground(color);
    }
    
    public User getAuthenticatedUser() {
        return authenticatedUser;
    }
    
    public static User showLoginDialog(Frame parent) {
        LoginDialog dialog = new LoginDialog(parent);
        dialog.setVisible(true);
        return dialog.getAuthenticatedUser();
    }
}
