package database;

import models.Item;
import models.Transaction;
import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * مدير قاعدة البيانات
 */
public class DatabaseManager {
    private static final String DB_URL = "************************";
    private Connection connection;
    
    public DatabaseManager() {
        try {
            // تحميل SQLite JDBC driver
            Class.forName("org.sqlite.JDBC");
            connection = DriverManager.getConnection(DB_URL);
            createTables();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private void createTables() {
        try {
            Statement stmt = connection.createStatement();
            
            // جدول المواد
            String itemsTable = """
                CREATE TABLE IF NOT EXISTS items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    unit TEXT NOT NULL,
                    current_stock REAL DEFAULT 0,
                    unit_price REAL DEFAULT 0,
                    notes TEXT
                )
                """;
            stmt.execute(itemsTable);
            
            // جدول المعاملات
            String transactionsTable = """
                CREATE TABLE IF NOT EXISTS transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    item_name TEXT NOT NULL,
                    quantity REAL NOT NULL,
                    unit TEXT NOT NULL,
                    transaction_type TEXT NOT NULL,
                    date TEXT NOT NULL,
                    project TEXT,
                    supplier_or_receiver TEXT,
                    unit_price REAL DEFAULT 0,
                    total_price REAL DEFAULT 0,
                    notes TEXT
                )
                """;
            stmt.execute(transactionsTable);
            
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
    
    // إضافة مادة جديدة
    public boolean addItem(Item item) {
        String sql = "INSERT INTO items (name, unit, current_stock, unit_price, notes) VALUES (?, ?, ?, ?, ?)";
        try {
            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, item.getName());
            pstmt.setString(2, item.getUnit());
            pstmt.setDouble(3, item.getCurrentStock());
            pstmt.setDouble(4, item.getUnitPrice());
            pstmt.setString(5, item.getNotes());
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    // إضافة معاملة جديدة
    public boolean addTransaction(Transaction transaction) {
        String sql = "INSERT INTO transactions (item_name, quantity, unit, transaction_type, date, project, supplier_or_receiver, unit_price, total_price, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        try {
            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setString(1, transaction.getItemName());
            pstmt.setDouble(2, transaction.getQuantity());
            pstmt.setString(3, transaction.getUnit());
            pstmt.setString(4, transaction.getTransactionType());
            pstmt.setString(5, transaction.getDate().toString());
            pstmt.setString(6, transaction.getProject());
            pstmt.setString(7, transaction.getSupplierOrReceiver());
            pstmt.setDouble(8, transaction.getUnitPrice());
            pstmt.setDouble(9, transaction.getTotalPrice());
            pstmt.setString(10, transaction.getNotes());
            
            boolean success = pstmt.executeUpdate() > 0;
            
            // تحديث المخزون
            if (success) {
                updateItemStock(transaction.getItemName(), transaction.getQuantity(), 
                              transaction.getTransactionType().equals("وارد"));
            }
            
            return success;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    // تحديث مخزون المادة
    private void updateItemStock(String itemName, double quantity, boolean isIncoming) {
        String sql = "UPDATE items SET current_stock = current_stock + ? WHERE name = ?";
        try {
            PreparedStatement pstmt = connection.prepareStatement(sql);
            pstmt.setDouble(1, isIncoming ? quantity : -quantity);
            pstmt.setString(2, itemName);
            pstmt.executeUpdate();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
    
    // الحصول على جميع المواد
    public List<Item> getAllItems() {
        List<Item> items = new ArrayList<>();
        String sql = "SELECT * FROM items ORDER BY name";
        
        try {
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            
            while (rs.next()) {
                Item item = new Item(
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getString("unit"),
                    rs.getDouble("current_stock"),
                    rs.getDouble("unit_price"),
                    rs.getString("notes")
                );
                items.add(item);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return items;
    }
    
    // الحصول على جميع المعاملات
    public List<Transaction> getAllTransactions() {
        List<Transaction> transactions = new ArrayList<>();
        String sql = "SELECT * FROM transactions ORDER BY date DESC";
        
        try {
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(sql);
            
            while (rs.next()) {
                Transaction transaction = new Transaction();
                transaction.setId(rs.getInt("id"));
                transaction.setItemName(rs.getString("item_name"));
                transaction.setQuantity(rs.getDouble("quantity"));
                transaction.setUnit(rs.getString("unit"));
                transaction.setTransactionType(rs.getString("transaction_type"));
                transaction.setDate(LocalDateTime.parse(rs.getString("date")));
                transaction.setProject(rs.getString("project"));
                transaction.setSupplierOrReceiver(rs.getString("supplier_or_receiver"));
                transaction.setUnitPrice(rs.getDouble("unit_price"));
                transaction.setTotalPrice(rs.getDouble("total_price"));
                transaction.setNotes(rs.getString("notes"));
                
                transactions.add(transaction);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return transactions;
    }
    
    // البحث في المعاملات
    public List<Transaction> searchTransactions(String searchTerm) {
        List<Transaction> transactions = new ArrayList<>();
        String sql = "SELECT * FROM transactions WHERE item_name LIKE ? OR project LIKE ? OR supplier_or_receiver LIKE ? ORDER BY date DESC";
        
        try {
            PreparedStatement pstmt = connection.prepareStatement(sql);
            String searchPattern = "%" + searchTerm + "%";
            pstmt.setString(1, searchPattern);
            pstmt.setString(2, searchPattern);
            pstmt.setString(3, searchPattern);
            
            ResultSet rs = pstmt.executeQuery();
            
            while (rs.next()) {
                Transaction transaction = new Transaction();
                transaction.setId(rs.getInt("id"));
                transaction.setItemName(rs.getString("item_name"));
                transaction.setQuantity(rs.getDouble("quantity"));
                transaction.setUnit(rs.getString("unit"));
                transaction.setTransactionType(rs.getString("transaction_type"));
                transaction.setDate(LocalDateTime.parse(rs.getString("date")));
                transaction.setProject(rs.getString("project"));
                transaction.setSupplierOrReceiver(rs.getString("supplier_or_receiver"));
                transaction.setUnitPrice(rs.getDouble("unit_price"));
                transaction.setTotalPrice(rs.getDouble("total_price"));
                transaction.setNotes(rs.getString("notes"));
                
                transactions.add(transaction);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        
        return transactions;
    }
    
    // إغلاق الاتصال
    public void closeConnection() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
}
