========================================
    نظام إدارة المخزون الاحترافي v2.0
       تعليمات التشغيل والاستخدام
========================================

🚀 التشغيل السريع:
   اضغط مرتين على: start-professional.bat

📋 ملفات التشغيل:
   • start-professional.bat         - تشغيل سريع مع تجميع تلقائي
   • compile-professional.bat       - تجميع النظام فقط
   • run-professional.bat           - تشغيل متقدم
   • run-professional-simple.bat    - تشغيل بسيط للأنظمة القديمة

🔐 تسجيل الدخول الافتراضي:
   👤 اسم المستخدم: admin
   🔐 كلمة المرور: admin123

========================================
              الميزات الاحترافية
========================================

🏢 نظام المستخدمين:
   • مدير النظام (ADMIN) - جميع الصلاحيات
   • مدير المخزون (MANAGER) - إدارة المخزون والتقارير
   • موظف (EMPLOYEE) - إدخال المعاملات والعرض
   • مشاهد (VIEWER) - عرض فقط

📦 إدارة المخزون المتقدمة:
   • فئات المواد مع ألوان مميزة
   • باركود وصور للمواد
   • تنبيهات المخزون المنخفض
   • تتبع مواقع التخزين
   • حساب القيم الإجمالية

💼 المعاملات المحسنة:
   • أنواع: وارد، صادر، تسوية، نقل
   • حالات: معلق، مؤكد، ملغي
   • أرقام مرجعية وفواتير
   • ربط بالمستخدمين والمشاريع

📊 لوحة التحكم:
   • إحصائيات فورية
   • بطاقات ملونة للبيانات
   • سجل الأنشطة الأخيرة
   • تحديث تلقائي

🎨 واجهة حديثة:
   • تصميم عصري ونظيف
   • ألوان متناسقة
   • أيقونات ملونة
   • دعم كامل للعربية

========================================
                كيفية الاستخدام
========================================

1️⃣ تسجيل الدخول:
   • شغل النظام
   • أدخل اسم المستخدم وكلمة المرور
   • اضغط "دخول" أو Enter

2️⃣ لوحة التحكم:
   • عرض الإحصائيات الرئيسية
   • متابعة الأنشطة الأخيرة
   • نظرة سريعة على حالة النظام

3️⃣ إدارة المخزون:
   • إضافة مواد جديدة
   • تعديل معلومات المواد
   • تصنيف المواد حسب الفئات
   • مراقبة مستويات المخزون

4️⃣ المعاملات:
   • تسجيل الوارد والصادر
   • إدارة حالات المعاملات
   • ربط المعاملات بالمشاريع
   • تتبع المراجع والفواتير

5️⃣ التقارير:
   • تقارير المخزون
   • تقارير المعاملات
   • إحصائيات متقدمة
   • تصدير البيانات

6️⃣ الإعدادات (للمديرين):
   • إدارة المستخدمين
   • إعدادات النظام
   • النسخ الاحتياطي
   • سجل الأنشطة

========================================
              الأمان والحماية
========================================

🔐 تشفير متقدم:
   • كلمات المرور مشفرة بـ SHA-256
   • Salt عشوائي لكل كلمة مرور
   • حماية من هجمات القاموس

👥 إدارة الصلاحيات:
   • صلاحيات متدرجة حسب الدور
   • تحكم دقيق في الوصول
   • منع العمليات غير المصرحة

📝 سجل الأنشطة:
   • تتبع جميع العمليات
   • تسجيل التغييرات
   • معرفة المستخدم المسؤول

⏰ إدارة الجلسات:
   • انتهاء صلاحية تلقائي
   • حماية من الجلسات المهجورة
   • تسجيل خروج آمن

========================================
              استكشاف الأخطاء
========================================

❌ مشاكل التشغيل:
   • تأكد من تثبيت Java 8+
   • شغل كمدير إذا لزم الأمر
   • تحقق من اتصال الإنترنت

🔧 مشاكل قاعدة البيانات:
   • أغلق جميع نسخ النظام
   • تأكد من عدم فتح الملف في برنامج آخر
   • احذف ملف .db لإعادة الإنشاء

🎨 مشاكل الواجهة:
   • استخدم الملف البسيط للتشغيل
   • تحقق من دعم الخطوط العربية
   • جرب تشغيل النظام كمدير

📁 مشاكل الملفات:
   • لا تحذف مجلد lib
   • لا تنقل ملفات قاعدة البيانات
   • احتفظ بنسخة احتياطية

========================================
                 نصائح مهمة
========================================

💡 للاستخدام الأمثل:
   • غير كلمة مرور المدير فوراً
   • أنشئ مستخدمين منفصلين للموظفين
   • صنف المواد في فئات منطقية
   • استخدم الباركود للمواد المهمة

🔄 للصيانة:
   • اعمل نسخة احتياطية دورية
   • راقب سجل الأنشطة
   • نظف البيانات القديمة
   • حدث النظام عند توفر إصدارات جديدة

📊 للتقارير:
   • استخدم فترات زمنية محددة
   • صنف التقارير حسب الحاجة
   • احفظ التقارير المهمة
   • شارك التقارير مع الإدارة

========================================
              معلومات تقنية
========================================

🖥️ المتطلبات:
   • Java 8 أو أحدث
   • Windows 7 أو أحدث
   • 512 MB RAM كحد أدنى
   • 100 MB مساحة تخزين

📦 المكتبات:
   • SQLite JDBC 3.44.1.0
   • SLF4J 1.7.36
   • FlatLaf 3.2.5 (اختياري)

🗃️ قاعدة البيانات:
   • SQLite محلية
   • ملف: professional_inventory.db
   • نسخ احتياطي تلقائي
   • فهرسة محسنة للأداء

========================================
تم تطوير هذا النظام بأعلى معايير الجودة
        والأمان والاحترافية
========================================
