@echo off
chcp 65001 >nul
title نظام إدارة المخزون - هيمن كروب
color 0A

echo.
echo ========================================
echo    نظام إدارة المخزون - هيمن كروب
echo ========================================
echo.

REM البحث عن ملف exe
if exist "dist\نظام-المخزون-هيمن-كروب.exe" (
    echo ✅ وجدت الملف!
    echo 🚀 تشغيل النظام...
    start "" "dist\نظام-المخزون-هيمن-كروب.exe"
    echo ✅ تم التشغيل!
    timeout /t 2 >nul
    exit
)

REM إذا لم يوجد، شغل في المتصفح
if exist "index-professional.html" (
    echo 🌐 تشغيل في المتصفح...
    start "" "index-professional.html"
    echo ✅ تم فتح النظام في المتصفح!
    echo.
    echo 💡 لإنشاء ملف .exe:
    echo    انقر على "اعمل-كل-شي.bat"
    timeout /t 3 >nul
    exit
)

echo ❌ لم أجد ملفات النظام!
echo تأكد من وجود index-professional.html
pause
