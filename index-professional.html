<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المخزون - هيمن كروب</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #2c3e50;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 50px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 25px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .company-info {
            position: absolute;
            top: 20px;
            right: 30px;
            text-align: right;
            font-size: 0.9rem;
            opacity: 0.8;
            z-index: 1;
        }

        .nav-tabs {
            display: flex;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 3px solid #dee2e6;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow-x: auto;
            gap: 5px;
        }

        .tab-btn {
            padding: 18px 25px;
            border: none;
            border-radius: 0;
            background: transparent;
            color: #6c757d;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            white-space: nowrap;
            font-family: 'Cairo', sans-serif;
            position: relative;
            min-width: 140px;
        }

        .tab-btn:hover {
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.1) 0%, rgba(41, 128, 185, 0.1) 100%);
            color: #3498db;
            transform: translateY(-2px);
        }

        .tab-btn.active {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
            transform: translateY(-2px);
        }

        .tab-btn.active::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #e74c3c, #f39c12, #2ecc71, #3498db);
        }

        .tab-content {
            display: none;
            padding: 30px;
            min-height: 600px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 35px;
        }

        .stat-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #e74c3c, #f39c12, #2ecc71, #3498db);
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-number {
            font-size: 2.2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 8px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 1rem;
            font-weight: 500;
        }

        .panel {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            position: relative;
            overflow: hidden;
        }

        .panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #2ecc71, #f39c12, #e74c3c);
        }

        .panel h2 {
            color: #2c3e50;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #3498db;
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
            font-size: 0.95rem;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: #ffffff;
            font-family: 'Cairo', sans-serif;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
            transform: translateY(-1px);
        }

        .btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 5px;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
            font-family: 'Cairo', sans-serif;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        }

        .btn-success:hover {
            box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .btn-danger:hover {
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
        }

        .btn-warning:hover {
            box-shadow: 0 6px 20px rgba(243, 156, 18, 0.4);
        }

        .table-container {
            overflow-x: auto;
            margin-top: 25px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            font-size: 0.9rem;
        }

        .table th,
        .table td {
            padding: 15px 12px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }

        .table th {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            font-weight: 600;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .table tbody tr {
            transition: all 0.3s ease;
        }

        .table tbody tr:hover {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            transform: scale(1.01);
        }

        .alert {
            padding: 15px 20px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: 600;
            border-left: 4px solid;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border-left-color: #28a745;
        }

        .alert-danger {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
            border-left-color: #dc3545;
        }

        .alert-warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
            border-left-color: #ffc107;
        }

        .badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .badge-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .badge-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: #212529;
        }

        .badge-danger {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
            color: white;
        }

        .badge-info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
            color: white;
        }

        .badge-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
        }

        .filter-btn {
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .filter-btn.active {
            border-color: #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            transform: translateY(-2px);
        }

        .filter-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .budget-filter-btn {
            border: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
            opacity: 0.8;
        }

        .budget-filter-btn:hover {
            opacity: 1;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .budget-filter-btn.active {
            opacity: 1;
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .action-btn {
            padding: 8px 12px;
            margin: 2px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 11px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .action-btn.edit {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: #212529;
        }

        .action-btn.delete {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        @media (max-width: 768px) {
            .container {
                margin: 0;
                border-radius: 0;
            }

            .nav-tabs {
                flex-wrap: wrap;
            }

            .tab-btn {
                flex: 1;
                min-width: 120px;
                padding: 15px 10px;
                font-size: 14px;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
            }

            .form-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .panel {
                padding: 20px;
                margin-bottom: 20px;
            }

            .header h1 {
                font-size: 1.8rem;
            }

            .company-info {
                position: static;
                text-align: center;
                margin-top: 10px;
            }
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* CSS للطباعة */
        @media print {
            body * {
                visibility: hidden;
            }

            .print-content, .print-content * {
                visibility: visible;
            }

            .print-content {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                background: white;
                color: black;
                font-family: Arial, sans-serif;
                font-size: 12px;
                line-height: 1.4;
            }

            .print-header {
                text-align: center;
                margin-bottom: 20px;
                border-bottom: 2px solid #333;
                padding-bottom: 10px;
            }

            .print-totals {
                display: flex;
                justify-content: center;
                gap: 30px;
                margin: 20px 0;
                font-size: 14px;
                font-weight: bold;
            }

            .print-total-box {
                border: 1px solid #333;
                padding: 8px 15px;
                border-radius: 5px;
                text-align: center;
                min-width: 120px;
            }

            @page {
                margin: 1cm;
                size: A4;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="company-info">
                <div><strong>هيمن كروب</strong></div>
                <div>نظام إدارة المخزون</div>
            </div>

            <!-- Logo and Title Section -->
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <!-- Empty space on right for balance -->
                <div style="width: 80px;"></div>

                <!-- Title in center -->
                <div style="text-align: center; flex-grow: 1;">
                    <h1 style="margin: 0; font-size: 2.5rem; font-weight: 700; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                        <i class="fas fa-warehouse"></i> نظام المخزون
                    </h1>
                </div>

                <!-- Logo on the left (which appears on right in RTL) -->
                <div style="text-align: right;">
                    <img src="logo.jpg" alt="Heiman Group Logo"
                         style="width: 80px; height: 80px; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.3); object-fit: cover;">
                </div>
            </div>
        </div>

        <div class="nav-tabs">
            <button class="tab-btn active" onclick="showTab('dashboard')">
                <i class="fas fa-tachometer-alt"></i> لوحة التحكم
            </button>
            <button class="tab-btn" onclick="showTab('transactions')">
                <i class="fas fa-exchange-alt"></i> المعاملات
            </button>
            <button class="tab-btn" onclick="showTab('inventory')">
                <i class="fas fa-boxes"></i> المخزون
            </button>
            <button class="tab-btn" onclick="showTab('budget')">
                <i class="fas fa-calculator"></i> الميزانية
            </button>
            <button class="tab-btn" onclick="showTab('reports')">
                <i class="fas fa-chart-bar"></i> التقارير
            </button>
            <button class="tab-btn" onclick="showTab('analytics')">
                <i class="fas fa-chart-line"></i> التحليلات
            </button>
            <button class="tab-btn" onclick="showTab('receipts')">
                <i class="fas fa-receipt"></i> الإيصالات
            </button>
            <button class="tab-btn" onclick="showTab('fuel')">
                <i class="fas fa-gas-pump"></i> المحروقات
            </button>
            <button class="tab-btn" onclick="showTab('warehouses')">
                <i class="fas fa-warehouse"></i> المخازن
            </button>
            <button class="tab-btn" onclick="showTab('settings')">
                <i class="fas fa-cog"></i> الإعدادات
            </button>
        </div>

        <div id="alertContainer"></div>

        <!-- لوحة التحكم -->
        <div id="dashboard" class="tab-content active">
            <div class="panel">
                <h2><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h2>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-boxes"></i></div>
                        <div class="stat-number" id="totalItems">0</div>
                        <div class="stat-label">إجمالي المواد</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-exclamation-triangle"></i></div>
                        <div class="stat-number" id="lowStockItems">0</div>
                        <div class="stat-label">مواد منخفضة المخزون</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-calendar-day"></i></div>
                        <div class="stat-number" id="todayTransactions">0</div>
                        <div class="stat-label">معاملات اليوم</div>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 30px;">
                    <button class="btn" onclick="refreshData()">
                        <i class="fas fa-sync"></i> تحديث البيانات
                    </button>
                    <button class="btn btn-warning" onclick="exportData()">
                        <i class="fas fa-download"></i> تصدير البيانات
                    </button>
                    <button class="btn btn-danger" onclick="clearAllData()">
                        <i class="fas fa-trash"></i> مسح جميع البيانات
                    </button>
                </div>
            </div>

            <!-- جدول المخزون السريع -->
            <div class="panel">
                <h2>
                    <i class="fas fa-list"></i>
                    نظرة سريعة على المخزون
                    <button class="btn" onclick="refreshData()" style="margin-right: auto; font-size: 0.9rem;">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                </h2>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>المادة</th>
                                <th>الكمية</th>
                                <th>الوحدة</th>
                                <th>المخزن</th>
                                <th>الحالة</th>
                                <th>آخر تحديث</th>
                            </tr>
                        </thead>
                        <tbody id="dashboardInventoryTable">
                            <tr>
                                <td colspan="6" style="text-align: center; color: #7f8c8d; padding: 40px;">
                                    <i class="fas fa-box-open" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>
                                    لا توجد مواد في المخزون<br>
                                    <small>ابدأ بإضافة بيانات تجريبية أو أضف معاملة جديدة</small>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="panel">
                <h2><i class="fas fa-chart-pie"></i> إحصائيات سريعة</h2>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <div style="background: linear-gradient(135deg, #3498db, #2980b9); color: white; padding: 20px; border-radius: 10px;">
                        <h4><i class="fas fa-arrow-up"></i> المعاملات الواردة</h4>
                        <div style="font-size: 1.5rem; font-weight: bold;" id="incomingTotal">0</div>
                        <small>إجمالي المعاملات الواردة</small>
                    </div>

                    <div style="background: linear-gradient(135deg, #e74c3c, #c0392b); color: white; padding: 20px; border-radius: 10px;">
                        <h4><i class="fas fa-arrow-down"></i> المعاملات الصادرة</h4>
                        <div style="font-size: 1.5rem; font-weight: bold;" id="outgoingTotal">0</div>
                        <small>إجمالي المعاملات الصادرة</small>
                    </div>

                    <div style="background: linear-gradient(135deg, #27ae60, #229954); color: white; padding: 20px; border-radius: 10px;">
                        <h4><i class="fas fa-balance-scale"></i> صافي الحركة</h4>
                        <div style="font-size: 1.5rem; font-weight: bold;" id="netMovement">0</div>
                        <small>الفرق بين الوارد والصادر</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- المعاملات -->
        <div id="transactions" class="tab-content">
            <div class="panel">
                <h2><i class="fas fa-plus-circle"></i> إضافة معاملة</h2>

                <form id="transactionForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="itemName">اسم المادة *</label>
                            <input type="text" id="itemName" class="form-control" required placeholder="أدخل اسم المادة" list="itemsList">
                            <datalist id="itemsList"></datalist>
                        </div>

                        <div class="form-group">
                            <label for="transactionType">نوع المعاملة *</label>
                            <select id="transactionType" class="form-control" required onchange="handleTransactionTypeChange()">
                                <option value="">اختر نوع المعاملة</option>
                                <option value="وارد">وارد (إضافة للمخزون)</option>
                                <option value="صادر">صادر (خروج من المخزون)</option>
                                <option value="تسوية">تسوية مخزون</option>
                                <option value="تالف">تالف (خسارة مالية)</option>
                                <option value="مرتجع">مرتجع (إرجاع للمورد)</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="quantity">الكمية *</label>
                            <input type="number" id="quantity" class="form-control" step="0.01" min="0" required>
                        </div>

                        <div class="form-group">
                            <label for="unit">الوحدة *</label>
                            <input type="text" id="unit" class="form-control" required placeholder="كيس، طن، قطعة، متر..." list="unitsList">
                            <datalist id="unitsList">
                                <option value="كيس">
                                <option value="طن">
                                <option value="قطعة">
                                <option value="متر">
                                <option value="متر مكعب">
                                <option value="لتر">
                                <option value="كيلوغرام">
                            </datalist>
                        </div>

                        <div class="form-group" id="priceGroup">
                            <label for="unitPrice">سعر الوحدة</label>
                            <input type="number" id="unitPrice" class="form-control" step="0.01" min="0" placeholder="0.00">
                        </div>

                        <div class="form-group" id="currencyGroup">
                            <label for="currency">العملة</label>
                            <select id="currency" class="form-control" onchange="handleCurrencyChange()">
                                <option value="IQD">دينار عراقي (د.ع)</option>
                                <option value="USD">دولار أمريكي ($)</option>
                            </select>
                        </div>

                        <div class="form-group" id="exchangeRateGroup" style="display: none;">
                            <label for="transactionExchangeRate">سعر الصرف (د.ع لكل دولار) *</label>
                            <input type="number" id="transactionExchangeRate" class="form-control" step="0.01" min="0" placeholder="1500">
                            <small class="form-text text-muted">سعر الصرف الحالي: <span id="currentExchangeRate">1500</span> د.ع</small>
                        </div>

                        <div class="form-group">
                            <label for="warehouse">المخزن *</label>
                            <select id="warehouse" class="form-control" required>
                                <option value="">اختر المخزن</option>
                                <option value="المخزن الرئيسي">المخزن الرئيسي</option>
                                <option value="مخزن المواد الخام">مخزن المواد الخام</option>
                                <option value="مخزن الأدوات">مخزن الأدوات</option>
                                <option value="مخزن قطع الغيار">مخزن قطع الغيار</option>
                                <option value="مخزن التصدير">مخزن التصدير</option>
                            </select>
                        </div>



                        <div class="form-group">
                            <label for="supplier" id="supplierLabel">المورد/المستلم</label>
                            <input type="text" id="supplier" class="form-control" placeholder="اسم المورد أو المستلم" list="suppliersList">
                            <datalist id="suppliersList"></datalist>
                        </div>

                        <div class="form-group" id="targetWarehouseGroup" style="display: none;">
                            <label for="targetWarehouse" id="targetWarehouseLabel">المخزن المستهدف</label>
                            <select id="targetWarehouse" class="form-control">
                                <option value="">اختر المخزن المستهدف</option>
                                <option value="المخزن الرئيسي">المخزن الرئيسي</option>
                                <option value="مخزن المواد الخام">مخزن المواد الخام</option>
                                <option value="مخزن الأدوات">مخزن الأدوات</option>
                                <option value="مخزن قطع الغيار">مخزن قطع الغيار</option>
                                <option value="مخزن التصدير">مخزن التصدير</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="invoiceNumber">رقم الفاتورة</label>
                            <input type="text" id="invoiceNumber" class="form-control" placeholder="رقم الفاتورة أو المرجع">
                        </div>

                        <div class="form-group">
                            <label for="transactionDate">تاريخ المعاملة</label>
                            <input type="datetime-local" id="transactionDate" class="form-control">
                        </div>

                        <div class="form-group" style="grid-column: 1 / -1;">
                            <label for="notes">ملاحظات</label>
                            <textarea id="notes" class="form-control" rows="3" placeholder="ملاحظات إضافية حول المعاملة"></textarea>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 25px;">
                        <button type="submit" class="btn btn-success" style="padding: 15px 30px; font-size: 16px;">
                            <i class="fas fa-save"></i> حفظ المعاملة
                        </button>
                        <button type="reset" class="btn" onclick="resetTransactionForm()">
                            <i class="fas fa-undo"></i> إعادة تعيين
                        </button>
                        <button type="button" class="btn btn-warning" onclick="showQuickAdd()">
                            <i class="fas fa-bolt"></i> إضافة سريعة
                        </button>
                    </div>
                </form>
            </div>

            <!-- فلاتر البحث -->
            <div class="panel">
                <h2><i class="fas fa-filter"></i> فلاتر البحث والعرض</h2>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
                    <div class="form-group">
                        <label for="filterType">نوع المعاملة</label>
                        <select id="filterType" class="form-control" onchange="applyFilters()">
                            <option value="">جميع الأنواع</option>
                            <option value="وارد">وارد</option>
                            <option value="صادر">صادر</option>
                            <option value="تسوية">تسوية</option>
                            <option value="تالف">تالف</option>
                            <option value="مرتجع">مرتجع</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="filterWarehouse">المخزن</label>
                        <select id="filterWarehouse" class="form-control" onchange="applyFilters()">
                            <option value="">جميع المخازن</option>
                            <option value="المخزن الرئيسي">المخزن الرئيسي</option>
                            <option value="مخزن المواد الخام">مخزن المواد الخام</option>
                            <option value="مخزن الأدوات">مخزن الأدوات</option>
                            <option value="مخزن قطع الغيار">مخزن قطع الغيار</option>
                            <option value="مخزن التصدير">مخزن التصدير</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="filterDateFrom">من تاريخ</label>
                        <input type="date" id="filterDateFrom" class="form-control" onchange="applyFilters()">
                    </div>

                    <div class="form-group">
                        <label for="filterDateTo">إلى تاريخ</label>
                        <input type="date" id="filterDateTo" class="form-control" onchange="applyFilters()">
                    </div>

                    <div class="form-group">
                        <label for="searchItem">البحث في المواد</label>
                        <input type="text" id="searchItem" class="form-control" placeholder="اسم المادة..." oninput="applyFilters()">
                    </div>

                    <div style="display: flex; align-items: end; gap: 10px;">
                        <button class="btn" onclick="clearFilters()">
                            <i class="fas fa-times"></i> مسح الفلاتر
                        </button>
                        <button class="btn btn-warning" onclick="exportFilteredData()">
                            <i class="fas fa-download"></i> تصدير
                        </button>
                    </div>
                </div>
            </div>

            <!-- سجل المعاملات -->
            <div class="panel">
                <h2>
                    <i class="fas fa-list"></i> سجل المعاملات
                    <span id="transactionCount" style="background: #3498db; color: white; padding: 5px 10px; border-radius: 15px; font-size: 0.8rem; margin-right: 10px;">0</span>
                </h2>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>التاريخ والوقت</th>
                                <th>المادة</th>
                                <th>النوع</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>سعر الصرف</th>
                                <th>المجموع</th>
                                <th>المخزن المصدر</th>
                                <th>المخزن المستهدف</th>
                                <th>المورد/المستلم</th>
                                <th>رقم الفاتورة</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="transactionsTableBody">
                            <tr>
                                <td colspan="10" style="text-align: center; color: #7f8c8d; padding: 40px;">
                                    <i class="fas fa-exchange-alt" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>
                                    لا توجد معاملات بعد<br>
                                    <small>ابدأ بإضافة معاملة جديدة أو أضف بيانات تجريبية</small>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <!-- المخزون -->
        <div id="inventory" class="tab-content">
            <div class="panel">
                <h2><i class="fas fa-boxes"></i> المخزون</h2>

                <!-- إحصائيات المخزون -->
                <div class="stats-grid" style="grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); margin-bottom: 25px;">
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-cubes"></i></div>
                        <div class="stat-number" id="inventoryItemsCount">0</div>
                        <div class="stat-label">أصناف المواد</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-weight"></i></div>
                        <div class="stat-number" id="totalQuantity">0</div>
                        <div class="stat-label">إجمالي الكميات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-exclamation-circle"></i></div>
                        <div class="stat-number" id="lowStockCount">0</div>
                        <div class="stat-label">مخزون منخفض</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-times-circle"></i></div>
                        <div class="stat-number" id="outOfStockCount">0</div>
                        <div class="stat-label">نفد المخزون</div>
                    </div>
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>المادة</th>
                                <th>الكمية الحالية</th>
                                <th>الوحدة</th>
                                <th>المخزن</th>
                                <th>الحالة</th>
                                <th>آخر تحديث</th>
                            </tr>
                        </thead>
                        <tbody id="inventoryTableBody">
                            <tr>
                                <td colspan="6" style="text-align: center; color: #7f8c8d; padding: 40px;">
                                    <i class="fas fa-box-open" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>
                                    لا توجد مواد في المخزون<br>
                                    <small>ابدأ بإضافة معاملة وارد أو أضف بيانات تجريبية</small>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- أزرار الطباعة والتصدير للمخزون -->
                <div style="margin-top: 20px; text-align: center;">
                    <button class="btn btn-success" onclick="printInventory()" style="margin: 5px;">
                        <i class="fas fa-print"></i> طباعة المخزون
                    </button>
                    <button class="btn btn-warning" onclick="exportInventoryToExcel()" style="margin: 5px;">
                        <i class="fas fa-file-excel"></i> تصدير Excel
                    </button>
                </div>
            </div>
        </div>

        <!-- الميزانية -->
        <div id="budget" class="tab-content">
            <div class="panel">
                <h2><i class="fas fa-calculator"></i> الميزانية المالية الشاملة</h2>

                <!-- إحصائيات الميزانية -->
                <div class="stats-grid" style="grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); margin-bottom: 30px;">
                    <div class="stat-card" style="background: linear-gradient(135deg, #27ae60, #2ecc71);">
                        <div class="stat-icon" style="color: white;"><i class="fas fa-arrow-up"></i></div>
                        <div class="stat-number" style="color: white;" id="totalIncomingIQD">0</div>
                        <div class="stat-label" style="color: white;">وارد مواد (د.ع)</div>
                    </div>

                    <div class="stat-card" style="background: linear-gradient(135deg, #3498db, #5dade2);">
                        <div class="stat-icon" style="color: white;"><i class="fas fa-dollar-sign"></i></div>
                        <div class="stat-number" style="color: white;" id="totalIncomingUSD">0</div>
                        <div class="stat-label" style="color: white;">وارد مواد ($)</div>
                    </div>

                    <div class="stat-card" style="background: linear-gradient(135deg, #ff6b6b, #ee5a52);">
                        <div class="stat-icon" style="color: white;"><i class="fas fa-gas-pump"></i></div>
                        <div class="stat-number" style="color: white;" id="totalFuelIncomingIQD">0</div>
                        <div class="stat-label" style="color: white;">وارد محروقات (د.ع)</div>
                    </div>

                    <div class="stat-card" style="background: linear-gradient(135deg, #ffa726, #ff9800);">
                        <div class="stat-icon" style="color: white;"><i class="fas fa-gas-pump"></i></div>
                        <div class="stat-number" style="color: white;" id="totalFuelIncomingUSD">0</div>
                        <div class="stat-label" style="color: white;">وارد محروقات ($)</div>
                    </div>

                    <div class="stat-card" style="background: linear-gradient(135deg, #e74c3c, #ec7063);">
                        <div class="stat-icon" style="color: white;"><i class="fas fa-arrow-down"></i></div>
                        <div class="stat-number" style="color: white;" id="totalOutgoingIQD">0</div>
                        <div class="stat-label" style="color: white;">إجمالي المرتجع والتالف (د.ع)</div>
                    </div>

                    <div class="stat-card" style="background: linear-gradient(135deg, #f39c12, #f7dc6f);">
                        <div class="stat-icon" style="color: white;"><i class="fas fa-dollar-sign"></i></div>
                        <div class="stat-number" style="color: white;" id="totalOutgoingUSD">0</div>
                        <div class="stat-label" style="color: white;">إجمالي المرتجع والتالف ($)</div>
                    </div>

                    <div class="stat-card" style="background: linear-gradient(135deg, #8e44ad, #bb8fce);">
                        <div class="stat-icon" style="color: white;"><i class="fas fa-balance-scale"></i></div>
                        <div class="stat-number" style="color: white;" id="netBudgetIQD">0</div>
                        <div class="stat-label" style="color: white;">صافي الميزانية (د.ع)</div>
                    </div>

                    <div class="stat-card" style="background: linear-gradient(135deg, #34495e, #5d6d7e);">
                        <div class="stat-icon" style="color: white;"><i class="fas fa-dollar-sign"></i></div>
                        <div class="stat-number" style="color: white;" id="netBudgetUSD">0</div>
                        <div class="stat-label" style="color: white;">صافي الميزانية ($)</div>
                    </div>

                    <div class="stat-card" style="background: linear-gradient(135deg, #9b59b6, #8e44ad);">
                        <div class="stat-icon" style="color: white;"><i class="fas fa-calculator"></i></div>
                        <div class="stat-number" style="color: white;" id="totalCombinedIQD">0</div>
                        <div class="stat-label" style="color: white;">المجموع الكلي (د.ع)</div>
                    </div>

                    <div class="stat-card" style="background: linear-gradient(135deg, #e67e22, #d35400);">
                        <div class="stat-icon" style="color: white;"><i class="fas fa-calculator"></i></div>
                        <div class="stat-number" style="color: white;" id="totalCombinedUSD">0</div>
                        <div class="stat-label" style="color: white;">المجموع الكلي ($)</div>
                    </div>
                </div>

                <!-- فلاتر الميزانية -->
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 25px;">
                    <h4><i class="fas fa-filter"></i> فلاتر الميزانية</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                        <div class="form-group">
                            <label for="budgetDateFrom">من تاريخ</label>
                            <input type="date" id="budgetDateFrom" class="form-control" onchange="updateBudgetStats()">
                        </div>
                        <div class="form-group">
                            <label for="budgetDateTo">إلى تاريخ</label>
                            <input type="date" id="budgetDateTo" class="form-control" onchange="updateBudgetStats()">
                        </div>
                        <div class="form-group">
                            <label for="budgetWarehouse">المخزن</label>
                            <select id="budgetWarehouse" class="form-control" onchange="updateBudgetStats()">
                                <option value="">جميع المخازن</option>
                                <option value="المخزن الرئيسي">المخزن الرئيسي</option>
                                <option value="مخزن المواد الخام">مخزن المواد الخام</option>
                                <option value="مخزن الأدوات">مخزن الأدوات</option>
                                <option value="مخزن قطع الغيار">مخزن قطع الغيار</option>
                                <option value="مخزن التصدير">مخزن التصدير</option>
                            </select>
                        </div>
                        <div style="display: flex; align-items: end; gap: 10px;">
                            <button class="btn" onclick="clearBudgetFilters()">
                                <i class="fas fa-times"></i> مسح الفلاتر
                            </button>
                            <button class="btn btn-success" onclick="printBudget()">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                            <button class="btn btn-warning" onclick="exportBudgetReport()">
                                <i class="fas fa-download"></i> تصدير
                            </button>
                        </div>
                    </div>
                </div>

                <!-- جدول تفاصيل الميزانية -->
                <div class="panel">
                    <h3><i class="fas fa-list"></i> تفاصيل المعاملات المالية</h3>

                    <!-- أزرار اختيار نوع البيانات -->
                    <div style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                        <h4 style="margin-bottom: 15px; color: #2c3e50;">عرض تفاصيل:</h4>
                        <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                            <button onclick="showBudgetDetails('materials')" class="btn budget-filter-btn active" id="budgetMaterialsBtn" style="background: #27ae60; color: white; padding: 10px 20px;">
                                <i class="fas fa-boxes"></i> المواد
                            </button>
                            <button onclick="showBudgetDetails('fuel')" class="btn budget-filter-btn" id="budgetFuelBtn" style="background: #e74c3c; color: white; padding: 10px 20px;">
                                <i class="fas fa-gas-pump"></i> المحروقات
                            </button>
                            <button onclick="showBudgetDetails('both')" class="btn budget-filter-btn" id="budgetBothBtn" style="background: #3498db; color: white; padding: 10px 20px;">
                                <i class="fas fa-list"></i> الكل معاً
                            </button>
                        </div>
                        <div style="margin-top: 10px;">
                            <span id="budgetDetailsCount" style="color: #7f8c8d; font-size: 0.9rem;"></span>
                        </div>

                        <!-- زر الطباعة الذكي -->
                        <div style="margin-top: 15px; text-align: center;">
                            <button onclick="printBudgetDetails()" class="btn" id="budgetPrintBtn" style="background: #17a2b8; color: white; padding: 10px 20px;">
                                <i class="fas fa-print"></i> <span id="budgetPrintText">طباعة تفاصيل المواد</span>
                            </button>
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المادة/المحروقات</th>
                                    <th>النوع</th>
                                    <th>الكمية</th>
                                    <th>سعر الوحدة</th>
                                    <th>العملة</th>
                                    <th>سعر الصرف</th>
                                    <th>المجموع (د.ع)</th>
                                    <th>المجموع ($)</th>
                                    <th>المخزن</th>
                                    <th>المورد</th>
                                </tr>
                            </thead>
                            <tbody id="budgetTableBody">
                                <tr>
                                    <td colspan="11" style="text-align: center; color: #7f8c8d; padding: 40px;">
                                        <i class="fas fa-calculator" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>
                                        لا توجد معاملات مالية<br>
                                        <small>ابدأ بإضافة معاملات وارد أو مرتجع أو تالف</small>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- التقارير -->
        <div id="reports" class="tab-content">
            <div class="panel">
                <h2><i class="fas fa-chart-bar"></i> التقارير</h2>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 25px;">
                    <div style="background: linear-gradient(135deg, #3498db, #2980b9); color: white; padding: 25px; border-radius: 15px;">
                        <h4><i class="fas fa-calendar-day"></i> تقرير يومي</h4>
                        <p>تقرير مفصل لمعاملات يوم محدد</p>
                        <input type="date" id="dailyReportDate" class="form-control" style="margin: 15px 0;">
                        <button class="btn" onclick="generateDailyReport()">
                            <i class="fas fa-chart-line"></i> عرض التقرير
                        </button>
                    </div>

                    <div style="background: linear-gradient(135deg, #27ae60, #229954); color: white; padding: 25px; border-radius: 15px;">
                        <h4><i class="fas fa-warehouse"></i> تقرير المخزن</h4>
                        <p>تقرير مفصل لمخزن محدد</p>
                        <select id="warehouseReportSelect" class="form-control" style="margin: 15px 0;">
                            <option value="">اختر المخزن</option>
                            <option value="المخزن الرئيسي">المخزن الرئيسي</option>
                            <option value="مخزن المواد الخام">مخزن المواد الخام</option>
                            <option value="مخزن الأدوات">مخزن الأدوات</option>
                            <option value="مخزن قطع الغيار">مخزن قطع الغيار</option>
                            <option value="مخزن التصدير">مخزن التصدير</option>
                        </select>
                        <button class="btn" onclick="generateWarehouseReport()">
                            <i class="fas fa-building"></i> عرض التقرير
                        </button>
                    </div>
                </div>

                <!-- فلترة التقارير حسب نوع المعاملة -->
                <div style="margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 10px; border: 1px solid #e9ecef;">
                    <h3 style="margin-bottom: 15px; color: #2c3e50;"><i class="fas fa-filter"></i> فلترة التقارير</h3>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap; margin-bottom: 15px;">
                        <button onclick="filterReportByType('all')" class="btn filter-btn active" id="reportFilterAll" style="background: #6c757d; padding: 10px 20px;">
                            <i class="fas fa-list"></i> جميع المعاملات
                        </button>
                        <button onclick="filterReportByType('وارد')" class="btn filter-btn" id="reportFilterIncoming" style="background: #28a745; padding: 10px 20px;">
                            <i class="fas fa-arrow-down"></i> الوارد فقط
                        </button>
                        <button onclick="filterReportByType('صادر')" class="btn filter-btn" id="reportFilterOutgoing" style="background: #dc3545; padding: 10px 20px;">
                            <i class="fas fa-arrow-up"></i> الصادر فقط
                        </button>
                        <button onclick="filterReportByType('تالف')" class="btn filter-btn" id="reportFilterDamaged" style="background: #ffc107; color: #212529; padding: 10px 20px;">
                            <i class="fas fa-exclamation-triangle"></i> التالف فقط
                        </button>
                        <button onclick="filterReportByType('مرتجع')" class="btn filter-btn" id="reportFilterReturned" style="background: #17a2b8; padding: 10px 20px;">
                            <i class="fas fa-undo"></i> المرتجع فقط
                        </button>
                    </div>
                    <div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
                        <span id="reportFilterCount" style="color: #7f8c8d; font-size: 0.9rem;">اختر نوع المعاملة لعرض التقرير</span>
                        <button onclick="generateFilteredReport()" class="btn" style="background: #17a2b8; padding: 10px 20px;" id="generateFilteredReportBtn" disabled>
                            <i class="fas fa-chart-bar"></i> عرض التقرير المفلتر
                        </button>
                    </div>
                </div>

                <div id="reportResults" style="margin-top: 30px;"></div>

                <!-- أزرار الطباعة والتصدير -->
                <div style="margin-top: 20px; text-align: center; display: none;" id="reportActions">
                    <button class="btn btn-success" onclick="printCurrentReport()" style="margin: 5px;">
                        <i class="fas fa-print"></i> طباعة التقرير
                    </button>
                    <button class="btn btn-warning" onclick="exportReportToExcel()" style="margin: 5px;">
                        <i class="fas fa-file-excel"></i> تصدير Excel
                    </button>
                </div>
            </div>
        </div>

        <!-- التحليلات المتقدمة -->
        <div id="analytics" class="tab-content">
            <div class="panel">
                <h2><i class="fas fa-chart-line"></i> التحليلات المتقدمة</h2>

                <div style="text-align: center; padding: 60px; color: #7f8c8d;">
                    <i class="fas fa-chart-line" style="font-size: 4rem; margin-bottom: 20px; display: block;"></i>
                    <h3>التحليلات المتقدمة</h3>
                    <p>هذا القسم قيد التطوير وسيتضمن تحليلات متقدمة</p>
                </div>
            </div>
        </div>

        <!-- الإيصالات -->
        <div id="receipts" class="tab-content">
            <div class="panel">
                <h2><i class="fas fa-receipt"></i> إدارة الإيصالات</h2>

                <!-- قائمة المعاملات للتحويل إلى إيصالات -->
                <div style="margin-bottom: 30px;">
                    <h3>المعاملات الحديثة</h3>

                    <!-- أزرار التحكم والفلترة -->
                    <div style="margin-bottom: 15px;">
                        <!-- فلترة حسب نوع المعاملة -->
                        <div style="margin-bottom: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                            <h4 style="margin-bottom: 10px; color: #2c3e50;">فلترة حسب نوع المعاملة:</h4>
                            <div style="display: flex; gap: 10px; flex-wrap: wrap; margin-bottom: 10px;">
                                <button onclick="filterByType('all')" class="btn filter-btn active" id="filterAll" style="background: #6c757d; padding: 8px 15px;">
                                    <i class="fas fa-list"></i> الكل
                                </button>
                                <button onclick="filterByType('وارد')" class="btn filter-btn" id="filterIncoming" style="background: #28a745; padding: 8px 15px;">
                                    <i class="fas fa-arrow-down"></i> الوارد
                                </button>
                                <button onclick="filterByType('صادر')" class="btn filter-btn" id="filterOutgoing" style="background: #dc3545; padding: 8px 15px;">
                                    <i class="fas fa-arrow-up"></i> الصادر
                                </button>
                                <button onclick="filterByType('تالف')" class="btn filter-btn" id="filterDamaged" style="background: #ffc107; color: #212529; padding: 8px 15px;">
                                    <i class="fas fa-exclamation-triangle"></i> التالف
                                </button>
                                <button onclick="filterByType('مرتجع')" class="btn filter-btn" id="filterReturned" style="background: #17a2b8; padding: 8px 15px;">
                                    <i class="fas fa-undo"></i> المرتجع
                                </button>
                            </div>
                            <span id="filteredCount" style="color: #7f8c8d; font-size: 0.9rem;"></span>
                        </div>

                        <!-- صف أزرار التحكم -->
                        <div style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
                            <button onclick="selectAllVisible()" class="btn" style="background: #3498db; padding: 8px 15px;">
                                <i class="fas fa-check-square"></i> تحديد الكل المعروض
                            </button>
                            <button onclick="clearSelection()" class="btn" style="background: #95a5a6; padding: 8px 15px;">
                                <i class="fas fa-times"></i> إلغاء التحديد
                            </button>
                            <button onclick="generateMultipleReceipt()" class="btn" style="background: #27ae60; padding: 8px 15px;" id="generateReceiptBtn" disabled>
                                <i class="fas fa-receipt"></i> إنشاء إيصال للمحدد
                            </button>
                            <span id="selectedCount" style="color: #7f8c8d; margin-right: 15px;">لم يتم تحديد أي معاملة</span>
                        </div>
                    </div>

                    <div class="table-container" style="max-height: 300px;">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th style="width: 40px;">
                                        <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                                    </th>
                                    <th>التاريخ</th>
                                    <th>النوع</th>
                                    <th>المادة</th>
                                    <th>الكمية</th>
                                    <th>الشخص</th>
                                    <th>إجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="receiptTransactionsTable">
                                <tr>
                                    <td colspan="7" style="text-align: center; color: #7f8c8d;">
                                        لا توجد معاملات
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- منطقة الإيصال -->
                <div id="receiptArea" style="display: none;">
                    <div style="background: white; border: 2px solid #ddd; border-radius: 10px; padding: 30px; margin: 20px 0; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                        <!-- رأس الإيصال -->
                        <div style="display: flex; justify-content: space-between; align-items: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px;">
                            <div style="text-align: right;">
                                <img src="logo.jpg" alt="Heiman Group Logo"
                                     style="width: 60px; height: 60px; border-radius: 8px; object-fit: cover;">
                            </div>
                            <div style="text-align: center; flex-grow: 1;">
                                <h2 style="margin: 0; font-size: 1.8rem; color: #2c3e50;">هيمن كروب</h2>
                                <p style="margin: 5px 0 0 0; color: #7f8c8d;">إيصال استلام/تسليم</p>
                            </div>
                            <div style="text-align: left; font-size: 0.9rem; color: #666;">
                                <div>التاريخ: <span id="receiptDate"></span></div>
                                <div>الوقت: <span id="receiptTime"></span></div>
                                <div>رقم الإيصال: <span id="receiptNumber"></span></div>
                            </div>
                        </div>

                        <!-- نص الإيصال -->
                        <div style="margin: 30px 0; font-size: 1.1rem; line-height: 1.8;">
                            <div class="receipt-controls" style="padding: 15px; background: #f8f9fa; border-radius: 8px; margin-bottom: 20px;">
                                <!-- الصف الأول: نوع المعاملة واسم الشخص -->
                                <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 15px; flex-wrap: wrap;">
                                    <label style="font-weight: 600; color: #2c3e50;">نوع المعاملة:</label>
                                    <select id="receiptTransactionType" onchange="toggleReceiptFields()" style="padding: 8px 12px; border: 2px solid #ddd; border-radius: 5px; min-width: 150px; font-size: 1rem;">
                                        <option value="وارد">وارد (دخول)</option>
                                        <option value="صادر">صادر (خروج)</option>
                                        <option value="تالف">تالف</option>
                                        <option value="مرتجع">مرتجع</option>
                                    </select>
                                    <input type="text" id="receiptPersonName" placeholder="اسم الشخص" style="padding: 8px 12px; border: 2px solid #ddd; border-radius: 5px; min-width: 200px; font-size: 1rem;">
                                </div>

                                <!-- الصف الثاني: حقول إضافية للوارد -->
                                <div id="receiptExtraFields" style="display: none; margin-bottom: 15px;">
                                    <div style="display: flex; align-items: center; gap: 15px; flex-wrap: wrap;">
                                        <input type="number" id="receiptPrice" placeholder="السعر" step="0.01" style="padding: 8px 12px; border: 2px solid #ddd; border-radius: 5px; min-width: 120px; font-size: 1rem;">
                                        <select id="receiptCurrency" style="padding: 8px 12px; border: 2px solid #ddd; border-radius: 5px; min-width: 100px; font-size: 1rem;">
                                            <option value="IQD">د.ع</option>
                                            <option value="USD">$</option>
                                        </select>
                                        <input type="text" id="receiptWarehouseKeeper" placeholder="اسم أمين المخزن" style="padding: 8px 12px; border: 2px solid #ddd; border-radius: 5px; min-width: 200px; font-size: 1rem;">
                                        <label style="margin-right: 15px;">
                                            <input type="checkbox" id="receiptIsFuel" onchange="toggleReceiptFuelField()"> محروقات
                                        </label>
                                    </div>
                                </div>

                                <!-- زر التحديث -->
                                <div style="text-align: center;">
                                    <button onclick="updateReceiptText()" class="btn" style="background: #27ae60; padding: 8px 15px; font-size: 0.9rem;">
                                        <i class="fas fa-check"></i> موافق
                                    </button>
                                </div>
                            </div>
                            <p id="receiptText" style="margin-bottom: 20px; font-weight: 600; padding: 15px; background: white; border: 2px solid #e9ecef; border-radius: 8px;"></p>
                        </div>

                        <!-- جدول المواد -->
                        <div style="margin: 30px 0;">
                            <table style="width: 100%; border-collapse: collapse; border: 2px solid #333;">
                                <thead>
                                    <tr style="background: #f8f9fa;">
                                        <th style="border: 1px solid #333; padding: 12px; text-align: center;">م</th>
                                        <th style="border: 1px solid #333; padding: 12px; text-align: center;">اسم المادة</th>
                                        <th style="border: 1px solid #333; padding: 12px; text-align: center;">الكمية</th>
                                        <th style="border: 1px solid #333; padding: 12px; text-align: center;">الوحدة</th>
                                        <th style="border: 1px solid #333; padding: 12px; text-align: center;">ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody id="receiptItemsTable">
                                </tbody>
                            </table>
                        </div>

                        <!-- التوقيعات -->
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 50px; margin-top: 50px;">
                            <div style="text-align: center;">
                                <div style="border-top: 2px solid #333; padding-top: 10px; margin-top: 40px;">
                                    <strong>توقيع المستلم</strong>
                                </div>
                                <div style="margin-top: 10px; color: #666;">
                                    الاسم: <span id="receiverName"></span>
                                </div>
                            </div>
                            <div style="text-align: center;">
                                <div style="border-top: 2px solid #333; padding-top: 10px; margin-top: 40px;">
                                    <strong>توقيع أمين المخزن</strong>
                                </div>
                                <div style="margin-top: 10px; color: #666;">
                                    الاسم: <span id="warehouseKeeperName">_______________</span>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="receipt-actions" style="text-align: center; margin-top: 30px; border-top: 1px solid #ddd; padding-top: 20px;">
                            <button onclick="editReceipt()" class="btn" style="background: #f39c12; margin-left: 10px;">
                                <i class="fas fa-edit"></i> تعديل
                            </button>
                            <button onclick="printReceipt()" class="btn" style="background: #27ae60; margin-left: 10px;">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                            <button onclick="closeReceipt()" class="btn" style="background: #95a5a6;">
                                <i class="fas fa-times"></i> إغلاق
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تبويب المحروقات -->
        <div id="fuel" class="tab-content">
            <h2><i class="fas fa-gas-pump"></i> إدارة المحروقات</h2>

            <!-- نموذج إضافة معاملة محروقات -->
            <div class="panel">
                <h3><i class="fas fa-plus"></i> إضافة معاملة محروقات</h3>
                <form id="fuelTransactionForm" onsubmit="handleFuelTransactionSubmit(event)">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                        <div class="form-group">
                            <label for="fuelItemName">نوع المحروقات *</label>
                            <select id="fuelItemName" class="form-control" required>
                                <option value="">اختر نوع المحروقات</option>
                                <option value="بنزين عادي">بنزين عادي</option>
                                <option value="بنزين ممتاز">بنزين ممتاز</option>
                                <option value="ديزل">ديزل</option>
                                <option value="كاز">كاز</option>
                                <option value="غاز">غاز</option>
                                <option value="زيت محرك">زيت محرك</option>
                                <option value="زيت هيدروليك">زيت هيدروليك</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="fuelTransactionType">نوع المعاملة *</label>
                            <select id="fuelTransactionType" class="form-control" required onchange="handleFuelTypeChange()">
                                <option value="وارد">وارد (دخول)</option>
                                <option value="صادر">صادر (خروج)</option>
                                <option value="مرتجع">مرتجع</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="fuelQuantity">الكمية *</label>
                            <input type="number" id="fuelQuantity" class="form-control" step="0.01" min="0" required>
                        </div>

                        <div class="form-group">
                            <label for="fuelUnit">الوحدة *</label>
                            <select id="fuelUnit" class="form-control" required>
                                <option value="لتر">لتر</option>
                                <option value="غالون">غالون</option>
                                <option value="برميل">برميل</option>
                                <option value="كيلو">كيلو</option>
                            </select>
                        </div>

                        <!-- حقول السعر - تظهر فقط للوارد -->
                        <div class="form-group" id="fuelPriceGroup">
                            <label for="fuelUnitPrice">سعر الوحدة *</label>
                            <input type="number" id="fuelUnitPrice" class="form-control" step="0.01" min="0">
                        </div>

                        <div class="form-group" id="fuelCurrencyGroup">
                            <label for="fuelCurrency">العملة *</label>
                            <select id="fuelCurrency" class="form-control" onchange="handleFuelCurrencyChange()">
                                <option value="IQD">دينار عراقي (د.ع)</option>
                                <option value="USD">دولار أمريكي ($)</option>
                            </select>
                        </div>

                        <!-- حقل سعر الصرف للدولار -->
                        <div class="form-group" id="fuelExchangeRateGroup" style="display: none;">
                            <label for="fuelExchangeRate">سعر الصرف *</label>
                            <input type="number" id="fuelExchangeRate" class="form-control" step="0.01" min="0">
                            <small class="form-text">السعر الحالي: <span id="fuelCurrentExchangeRate">1500</span> د.ع</small>
                        </div>

                        <div class="form-group">
                            <label for="fuelSupplier">المورد/المستلم</label>
                            <input type="text" id="fuelSupplier" class="form-control" placeholder="اسم المورد أو المستلم">
                        </div>

                        <div class="form-group">
                            <label for="fuelTransactionDate">التاريخ *</label>
                            <input type="datetime-local" id="fuelTransactionDate" class="form-control" required>
                        </div>

                        <div class="form-group">
                            <label for="fuelNotes">ملاحظات</label>
                            <textarea id="fuelNotes" class="form-control" rows="2" placeholder="ملاحظات إضافية"></textarea>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-plus"></i> إضافة معاملة المحروقات
                        </button>
                    </div>
                </form>
            </div>

            <!-- إحصائيات المحروقات -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-gas-pump"></i></div>
                    <div class="stat-number" id="fuelTotalItems">0</div>
                    <div class="stat-label">أنواع المحروقات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-arrow-down" style="color: #28a745;"></i></div>
                    <div class="stat-number" id="fuelIncomingTotal">0</div>
                    <div class="stat-label">إجمالي الوارد</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-arrow-up" style="color: #dc3545;"></i></div>
                    <div class="stat-number" id="fuelOutgoingTotal">0</div>
                    <div class="stat-label">إجمالي الصادر</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-dollar-sign"></i></div>
                    <div class="stat-number" id="fuelTotalValue">0</div>
                    <div class="stat-label">القيمة الإجمالية</div>
                </div>
            </div>

            <!-- جدول معاملات المحروقات -->
            <div class="panel">
                <h3><i class="fas fa-list"></i> معاملات المحروقات</h3>

                <!-- أزرار الفلترة -->
                <div style="margin-bottom: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                    <h4 style="margin-bottom: 10px; color: #2c3e50;">فلترة حسب نوع المعاملة:</h4>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap; margin-bottom: 10px;">
                        <button onclick="filterFuelByType('all')" class="btn filter-btn active" id="fuelFilterAll" style="background: #6c757d; padding: 8px 15px;">
                            <i class="fas fa-list"></i> الكل
                        </button>
                        <button onclick="filterFuelByType('وارد')" class="btn filter-btn" id="fuelFilterIncoming" style="background: #28a745; padding: 8px 15px;">
                            <i class="fas fa-arrow-down"></i> الوارد
                        </button>
                        <button onclick="filterFuelByType('صادر')" class="btn filter-btn" id="fuelFilterOutgoing" style="background: #dc3545; padding: 8px 15px;">
                            <i class="fas fa-arrow-up"></i> الصادر
                        </button>
                        <button onclick="filterFuelByType('مرتجع')" class="btn filter-btn" id="fuelFilterReturned" style="background: #17a2b8; padding: 8px 15px;">
                            <i class="fas fa-undo"></i> المرتجع
                        </button>
                    </div>
                    <span id="fuelFilteredCount" style="color: #7f8c8d; font-size: 0.9rem;"></span>
                </div>

                <!-- أزرار الطباعة والتصدير -->
                <div style="margin-bottom: 15px; text-align: center;">
                    <button class="btn btn-success" onclick="printFuelTransactions()" style="margin: 5px;">
                        <i class="fas fa-print"></i> طباعة معاملات المحروقات
                    </button>
                    <button class="btn btn-warning" onclick="exportFuelToExcel()" style="margin: 5px;">
                        <i class="fas fa-file-excel"></i> تصدير Excel
                    </button>
                </div>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>نوع المحروقات</th>
                                <th>نوع المعاملة</th>
                                <th>الكمية</th>
                                <th>سعر الوحدة</th>
                                <th>العملة</th>
                                <th>سعر الصرف</th>
                                <th>المجموع (د.ع)</th>
                                <th>المجموع ($)</th>
                                <th>المورد/المستلم</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="fuelTransactionsTableBody">
                            <tr>
                                <td colspan="11" style="text-align: center; color: #7f8c8d; padding: 40px;">
                                    <i class="fas fa-gas-pump" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>
                                    لا توجد معاملات محروقات بعد
                                    <br><small>ابدأ بإضافة معاملة محروقات جديدة</small>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- تبويب المخازن -->
        <div id="warehouses" class="tab-content">
            <h2><i class="fas fa-warehouse"></i> إدارة المخازن</h2>

            <!-- نموذج إضافة مخزن جديد -->
            <div class="panel">
                <h3><i class="fas fa-plus"></i> إضافة مخزن جديد</h3>
                <form id="warehouseForm" onsubmit="handleWarehouseSubmit(event)">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                        <div class="form-group">
                            <label for="warehouseName">اسم المخزن *</label>
                            <input type="text" id="warehouseName" class="form-control" required placeholder="مثال: مخزن المواد الخام">
                        </div>

                        <div class="form-group">
                            <label for="warehouseDescription">الوصف</label>
                            <input type="text" id="warehouseDescription" class="form-control" placeholder="وصف المخزن (اختياري)">
                        </div>

                        <div class="form-group">
                            <label for="warehouseLocation">الموقع</label>
                            <input type="text" id="warehouseLocation" class="form-control" placeholder="موقع المخزن (اختياري)">
                        </div>

                        <div class="form-group">
                            <label for="warehouseManager">المسؤول</label>
                            <input type="text" id="warehouseManager" class="form-control" placeholder="اسم المسؤول عن المخزن">
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-plus"></i> إضافة المخزن
                        </button>
                    </div>
                </form>
            </div>

            <!-- قائمة المخازن الحالية -->
            <div class="panel">
                <h3><i class="fas fa-list"></i> المخازن الحالية</h3>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>اسم المخزن</th>
                                <th>الوصف</th>
                                <th>الموقع</th>
                                <th>المسؤول</th>
                                <th>عدد المواد</th>
                                <th>تاريخ الإنشاء</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="warehousesTableBody">
                            <tr>
                                <td colspan="7" style="text-align: center; color: #7f8c8d; padding: 40px;">
                                    <i class="fas fa-warehouse" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>
                                    لا توجد مخازن مضافة بعد
                                    <br><small>ابدأ بإضافة مخزن جديد</small>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- إحصائيات المخازن -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-warehouse"></i></div>
                    <div class="stat-number" id="totalWarehouses">0</div>
                    <div class="stat-label">إجمالي المخازن</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-boxes"></i></div>
                    <div class="stat-number" id="totalWarehouseItems">0</div>
                    <div class="stat-label">إجمالي المواد</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-chart-bar"></i></div>
                    <div class="stat-number" id="activeWarehouses">0</div>
                    <div class="stat-label">المخازن النشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-users"></i></div>
                    <div class="stat-number" id="warehouseManagers">0</div>
                    <div class="stat-label">المسؤولين</div>
                </div>
            </div>
        </div>

        <!-- الإعدادات -->
        <div id="settings" class="tab-content">
            <div class="panel">
                <h2><i class="fas fa-cog"></i> الإعدادات</h2>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 25px;">
                    <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; border: 1px solid #e9ecef;">
                        <h4><i class="fas fa-building"></i> معلومات الشركة</h4>
                        <div class="form-group">
                            <label for="companyName">اسم الشركة</label>
                            <input type="text" id="companyName" class="form-control" value="هيمن كروب">
                        </div>
                        <div class="form-group">
                            <label for="companyAddress">عنوان الشركة</label>
                            <input type="text" id="companyAddress" class="form-control" placeholder="العنوان الكامل">
                        </div>
                        <div class="form-group">
                            <label for="companyPhone">هاتف الشركة</label>
                            <input type="text" id="companyPhone" class="form-control" placeholder="رقم الهاتف">
                        </div>
                        <button class="btn btn-success" onclick="saveCompanyInfo()">
                            <i class="fas fa-save"></i> حفظ معلومات الشركة
                        </button>
                    </div>

                    <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; border: 1px solid #e9ecef;">
                        <h4><i class="fas fa-exchange-alt"></i> إعدادات العملة</h4>
                        <div class="form-group">
                            <label for="exchangeRate">سعر صرف الدولار (د.ع)</label>
                            <input type="number" id="exchangeRate" class="form-control" value="1500" step="0.01" min="0">
                        </div>
                        <button class="btn btn-success" onclick="updateExchangeRate()">
                            <i class="fas fa-sync"></i> تحديث سعر الصرف
                        </button>
                    </div>

                    <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; border: 1px solid #e9ecef;">
                        <h4><i class="fas fa-database"></i> إدارة البيانات</h4>
                        <div style="margin-bottom: 15px;">
                            <button class="btn btn-success" onclick="addSampleData()" style="width: 100%; margin-bottom: 10px;">
                                <i class="fas fa-plus"></i> إضافة بيانات تجريبية
                            </button>
                            <button class="btn" onclick="exportData()" style="width: 100%; margin-bottom: 10px;" id="exportBtn">
                                <i class="fas fa-download"></i> تصدير جميع البيانات
                            </button>
                            <button class="btn btn-danger" onclick="clearAllData()" style="width: 100%;" id="clearDataBtn">
                                <i class="fas fa-trash"></i> مسح جميع البيانات
                            </button>
                        </div>
                    </div>

                    <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; border: 1px solid #e9ecef;">
                        <h4><i class="fas fa-user-shield"></i> إعدادات الصلاحيات</h4>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="canDelete" checked onchange="updatePermissions()">
                                السماح بحذف المعاملات والمواد
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="canEdit" checked onchange="updatePermissions()">
                                السماح بتعديل البيانات
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="canExport" checked onchange="updatePermissions()">
                                السماح بتصدير البيانات
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="canClearData" checked onchange="updatePermissions()">
                                السماح بمسح جميع البيانات
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="canManageSettings" checked onchange="updatePermissions()">
                                السماح بإدارة الإعدادات
                            </label>
                        </div>
                        <button class="btn btn-success" onclick="savePermissions()">
                            <i class="fas fa-save"></i> حفظ إعدادات الصلاحيات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        // نظام إدارة المخزون - هيمن كروب
        let inventorySystem = {
            items: [],
            transactions: [],
            fuelTransactions: [], // معاملات المحروقات
            warehouses: [], // قائمة المخازن
            exchangeRate: 1500,
            companyInfo: {
                name: 'هيمن كروب',
                address: '',
                phone: '',
                email: ''
            },
            settings: {
                defaultCurrency: 'IQD',
                lowStockAlert: true,
                minStockLevel: 10,
                permissions: {
                    canDelete: true,
                    canEdit: true,
                    canExport: true,
                    canClearData: true,
                    canManageSettings: true
                }
            },
            lastBackup: null
        };

        // تحميل البيانات عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', function() {
            try {
                console.log('بدء تحميل النظام...');
                loadData();
                console.log('تم تحميل البيانات');
                updateAllStats();
                console.log('تم تحديث الإحصائيات');
                renderAllTables();
                console.log('تم عرض الجداول');
                setCurrentDates();
                console.log('تم تعيين التواريخ');
                setupEventListeners();
                console.log('تم إعداد مستمعي الأحداث');
                console.log('تم تحميل النظام بنجاح!');
            } catch (error) {
                console.error('خطأ في تحميل النظام:', error);
                showAlert('خطأ في تحميل النظام: ' + error.message, 'danger');
            }
        });

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            try {
                // نموذج المعاملات
                const transactionForm = document.getElementById('transactionForm');
                if (transactionForm) {
                    transactionForm.addEventListener('submit', handleTransactionSubmit);
                    console.log('تم إعداد مستمع نموذج المعاملات');
                } else {
                    console.warn('نموذج المعاملات غير موجود');
                }

                // تطبيق الصلاحيات عند التحميل
                setTimeout(() => {
                    try {
                        updatePermissions();
                        handleCurrencyChange(); // تطبيق حالة العملة الافتراضية
                        console.log('تم تطبيق الصلاحيات والعملة');
                    } catch (error) {
                        console.error('خطأ في تطبيق الصلاحيات:', error);
                    }
                }, 500);
            } catch (error) {
                console.error('خطأ في إعداد مستمعي الأحداث:', error);
            }
        }

        // دالة تحميل البيانات من localStorage
        function loadData() {
            const savedData = localStorage.getItem('professionalInventorySystem');
            if (savedData) {
                try {
                    const parsed = JSON.parse(savedData);
                    inventorySystem = { ...inventorySystem, ...parsed };

                    // تحديث الحقول في الواجهة
                    document.getElementById('companyName').value = inventorySystem.companyInfo.name || 'هيمن كروب';
                    document.getElementById('companyAddress').value = inventorySystem.companyInfo.address || '';
                    document.getElementById('companyPhone').value = inventorySystem.companyInfo.phone || '';
                    document.getElementById('exchangeRate').value = inventorySystem.exchangeRate || 1500;

                } catch (error) {
                    console.error('خطأ في تحميل البيانات:', error);
                    showAlert('خطأ في تحميل البيانات المحفوظة', 'danger');
                }
            }

            // إضافة مخازن افتراضية إذا لم تكن موجودة
            if (!inventorySystem.warehouses || inventorySystem.warehouses.length === 0) {
                initializeDefaultWarehouses();
            }
        }

        function initializeDefaultWarehouses() {
            const defaultWarehouses = [
                {
                    id: Date.now() + 1,
                    name: 'المخزن الرئيسي',
                    description: 'المخزن الرئيسي للمواد الأساسية',
                    location: '',
                    manager: '',
                    itemCount: 0,
                    isActive: true,
                    createdDate: new Date().toISOString()
                },
                {
                    id: Date.now() + 2,
                    name: 'مخزن المواد الخام',
                    description: 'مخزن للمواد الخام والأولية',
                    location: '',
                    manager: '',
                    itemCount: 0,
                    isActive: true,
                    createdDate: new Date().toISOString()
                },
                {
                    id: Date.now() + 3,
                    name: 'مخزن الأدوات',
                    description: 'مخزن للأدوات والمعدات',
                    location: '',
                    manager: '',
                    itemCount: 0,
                    isActive: true,
                    createdDate: new Date().toISOString()
                },
                {
                    id: Date.now() + 4,
                    name: 'مخزن قطع الغيار',
                    description: 'مخزن لقطع الغيار والصيانة',
                    location: '',
                    manager: '',
                    itemCount: 0,
                    isActive: true,
                    createdDate: new Date().toISOString()
                },
                {
                    id: Date.now() + 5,
                    name: 'مخزن التصدير',
                    description: 'مخزن للبضائع المخصصة للتصدير',
                    location: '',
                    manager: '',
                    itemCount: 0,
                    isActive: true,
                    createdDate: new Date().toISOString()
                }
            ];

            inventorySystem.warehouses = defaultWarehouses;
            saveData();
        }

        // دالة حفظ البيانات في localStorage
        function saveData() {
            try {
                localStorage.setItem('professionalInventorySystem', JSON.stringify(inventorySystem));
                inventorySystem.lastBackup = new Date().toISOString();
            } catch (error) {
                console.error('خطأ في حفظ البيانات:', error);
                showAlert('خطأ في حفظ البيانات', 'danger');
            }
        }

        // دالة عرض التبويبات
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            const tabs = document.querySelectorAll('.tab-content');
            tabs.forEach(tab => tab.classList.remove('active'));

            // إزالة active من جميع الأزرار
            const buttons = document.querySelectorAll('.tab-btn');
            buttons.forEach(btn => btn.classList.remove('active'));

            // إظهار التبويب المحدد
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');

            // تحديث البيانات حسب التبويب
            if (tabName === 'dashboard') {
                updateAllStats();
                renderDashboardInventory();
            } else if (tabName === 'inventory') {
                renderInventoryTable();
            } else if (tabName === 'transactions') {
                renderTransactionsTable();
            } else if (tabName === 'budget') {
                updateBudgetStats();
                renderBudgetTable();
            } else if (tabName === 'receipts') {
                updateReceiptTransactionsTable();
            } else if (tabName === 'reports') {
                updateReportFilterCount();
            } else if (tabName === 'fuel') {
                updateFuelStats();
                renderFuelTransactionsTable();
                setFuelCurrentDates();
            } else if (tabName === 'warehouses') {
                renderWarehousesTable();
                updateWarehouseStats();
            }
        }

        // دالة تحديث جميع الإحصائيات
        function updateAllStats() {
            const totalItems = inventorySystem.items.length;
            const lowStockItems = inventorySystem.items.filter(item => item.quantity <= (item.minStock || 10)).length;
            const todayTransactions = inventorySystem.transactions.filter(t => {
                const today = new Date().toDateString();
                const transactionDate = new Date(t.date).toDateString();
                return today === transactionDate;
            }).length;

            // دالة تنسيق الأرقام (إنجليزية بدون أصفار زائدة)
            function formatNumber(num) {
                return parseFloat(num.toFixed(2)).toLocaleString('en-US');
            }

            // تحديث لوحة التحكم - الإحصائيات المبسطة
            document.getElementById('totalItems').textContent = formatNumber(totalItems);
            document.getElementById('lowStockItems').textContent = formatNumber(lowStockItems);
            document.getElementById('todayTransactions').textContent = formatNumber(todayTransactions);

            // تحديث إحصائيات المخزون
            if (document.getElementById('inventoryItemsCount')) {
                document.getElementById('inventoryItemsCount').textContent = formatNumber(totalItems);
                document.getElementById('totalQuantity').textContent = formatNumber(inventorySystem.items.reduce((sum, item) => sum + item.quantity, 0));
                document.getElementById('lowStockCount').textContent = formatNumber(lowStockItems);
                document.getElementById('outOfStockCount').textContent = formatNumber(inventorySystem.items.filter(item => item.quantity === 0).length);
            }

            // تحديث إحصائيات المعاملات
            const incomingTotal = inventorySystem.transactions.filter(t => t.transactionType === 'وارد').length;
            const outgoingTotal = inventorySystem.transactions.filter(t => t.transactionType === 'صادر').length;

            if (document.getElementById('incomingTotal')) {
                document.getElementById('incomingTotal').textContent = formatNumber(incomingTotal);
                document.getElementById('outgoingTotal').textContent = formatNumber(outgoingTotal);
                document.getElementById('netMovement').textContent = formatNumber(incomingTotal - outgoingTotal);
            }

            if (document.getElementById('transactionCount')) {
                document.getElementById('transactionCount').textContent = formatNumber(inventorySystem.transactions.length);
            }
        }

        // دالة معالجة إرسال نموذج المعاملة
        function handleTransactionSubmit(e) {
            e.preventDefault();

            const formData = {
                itemName: document.getElementById('itemName').value.trim(),
                transactionType: document.getElementById('transactionType').value,
                quantity: parseFloat(document.getElementById('quantity').value),
                unit: document.getElementById('unit').value.trim(),
                unitPrice: parseFloat(document.getElementById('unitPrice').value) || 0,
                currency: document.getElementById('currency').value,
                warehouse: document.getElementById('warehouse').value,
                targetWarehouse: document.getElementById('targetWarehouse').value.trim(),
                supplier: document.getElementById('supplier').value.trim(),
                invoiceNumber: document.getElementById('invoiceNumber').value.trim(),
                notes: document.getElementById('notes').value.trim(),
                transactionDate: document.getElementById('transactionDate').value || new Date().toISOString(),
                customExchangeRate: parseFloat(document.getElementById('transactionExchangeRate').value) || null
            };

            // التحقق من البيانات
            if (!formData.itemName || !formData.transactionType || !formData.quantity || !formData.unit || !formData.warehouse) {
                showAlert('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }

            try {
                // تحديد سعر الصرف المستخدم
                let exchangeRateToUse = inventorySystem.exchangeRate;
                if (formData.currency === 'USD' && formData.customExchangeRate) {
                    exchangeRateToUse = formData.customExchangeRate;
                }

                // إنشاء المعاملة
                const transaction = {
                    id: Date.now(),
                    ...formData,
                    totalPrice: formData.quantity * formData.unitPrice,
                    date: formData.transactionDate,
                    exchangeRateAtTime: exchangeRateToUse
                };

                // إضافة المعاملة
                inventorySystem.transactions.push(transaction);

                // تحديث المخزون
                updateInventoryFromTransaction(transaction);

                // حفظ وتحديث
                saveData();
                updateAllStats();
                renderAllTables();

                // تحديث جدول الإيصالات
                updateReceiptTransactionsTable();

                // إعادة تعيين النموذج
                document.getElementById('transactionForm').reset();
                setCurrentDates();

                showAlert('تم إضافة المعاملة بنجاح! يمكنك إنشاء إيصال من تبويب الإيصالات.', 'success');

            } catch (error) {
                console.error('خطأ في إضافة المعاملة:', error);
                showAlert('خطأ في إضافة المعاملة: ' + error.message, 'danger');
            }
        }

        // دالة تحديث المخزون من المعاملة
        function updateInventoryFromTransaction(transaction) {
            let existingItem = inventorySystem.items.find(item =>
                item.name === transaction.itemName && item.warehouse === transaction.warehouse
            );

            if (existingItem) {
                // تحديث المادة الموجودة
                if (transaction.transactionType === 'وارد') {
                    existingItem.quantity += transaction.quantity;
                    if (transaction.unitPrice > 0) {
                        existingItem.unitPrice = transaction.unitPrice;
                        existingItem.currency = transaction.currency;
                        // حفظ سعر الصرف وقت الإدخال
                        existingItem.exchangeRateAtPurchase = transaction.exchangeRateAtTime;
                        existingItem.purchaseDate = transaction.date;
                    }
                } else if (transaction.transactionType === 'صادر') {
                    if (existingItem.quantity < transaction.quantity) {
                        throw new Error('الكمية المطلوبة أكبر من المتوفر في المخزون');
                    }
                    existingItem.quantity -= transaction.quantity;
                } else if (transaction.transactionType === 'تسوية') {
                    existingItem.quantity = transaction.quantity;
                    if (transaction.unitPrice > 0) {
                        existingItem.unitPrice = transaction.unitPrice;
                        existingItem.currency = transaction.currency;
                    }
                } else if (transaction.transactionType === 'تالف') {
                    if (existingItem.quantity < transaction.quantity) {
                        throw new Error('الكمية المطلوبة أكبر من المتوفر في المخزون');
                    }
                    existingItem.quantity -= transaction.quantity;
                } else if (transaction.transactionType === 'مرتجع') {
                    if (existingItem.quantity < transaction.quantity) {
                        throw new Error('الكمية المطلوبة أكبر من المتوفر في المخزون');
                    }
                    existingItem.quantity -= transaction.quantity;
                }
                existingItem.lastUpdated = new Date().toISOString();
            } else {
                // إضافة مادة جديدة (فقط للوارد والتسوية)
                if (transaction.transactionType === 'وارد' || transaction.transactionType === 'تسوية') {
                    const newItem = {
                        id: Date.now(),
                        name: transaction.itemName,
                        quantity: transaction.quantity,
                        unit: transaction.unit,
                        unitPrice: transaction.unitPrice || 0,
                        currency: transaction.currency || 'IQD',
                        exchangeRateAtPurchase: transaction.exchangeRateAtTime,
                        purchaseDate: transaction.date,
                        minStock: inventorySystem.settings.minStockLevel || 10,
                        category: 'عام',
                        warehouse: transaction.warehouse,
                        created: new Date().toISOString(),
                        lastUpdated: new Date().toISOString()
                    };
                    inventorySystem.items.push(newItem);
                } else {
                    throw new Error('لا يمكن إجراء ' + transaction.transactionType + ' لمادة غير موجودة');
                }
            }
        }

        // دوال العرض والجداول
        function renderAllTables() {
            renderDashboardInventory();
            renderInventoryTable();
            renderTransactionsTable();
        }

        function renderDashboardInventory() {
            const tbody = document.getElementById('dashboardInventoryTable');
            if (!tbody) {
                console.warn('عنصر dashboardInventoryTable غير موجود');
                return;
            }

            if (inventorySystem.items.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: #7f8c8d; padding: 40px;"><i class="fas fa-box-open" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>لا توجد مواد في المخزون<br><small>ابدأ بإضافة بيانات تجريبية أو أضف معاملة جديدة</small></td></tr>';
                return;
            }

            tbody.innerHTML = inventorySystem.items.slice(0, 10).map(item => {
                const status = item.quantity === 0 ? 'نفد' : item.quantity <= (item.minStock || 10) ? 'منخفض' : 'متوفر';
                const statusClass = status === 'نفد' ? 'badge-danger' : status === 'منخفض' ? 'badge-warning' : 'badge-success';

                // دالة تنسيق الأرقام (إنجليزية بدون أصفار زائدة)
                function formatNumber(num) {
                    return parseFloat(num.toFixed(2)).toLocaleString('en-US');
                }

                return `
                    <tr>
                        <td>${item.name}</td>
                        <td>${formatNumber(item.quantity)}</td>
                        <td>${item.unit}</td>
                        <td>${item.warehouse}</td>
                        <td><span class="badge ${statusClass}">${status}</span></td>
                        <td>${new Date(item.lastUpdated).toLocaleDateString('ar-EG')}</td>
                    </tr>
                `;
            }).join('');
        }

        function renderInventoryTable() {
            const tbody = document.getElementById('inventoryTableBody');
            if (!tbody) {
                console.warn('عنصر inventoryTableBody غير موجود');
                return;
            }

            if (inventorySystem.items.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: #7f8c8d; padding: 40px;"><i class="fas fa-box-open" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>لا توجد مواد في المخزون<br><small>ابدأ بإضافة معاملة وارد أو أضف بيانات تجريبية</small></td></tr>';
                return;
            }

            tbody.innerHTML = inventorySystem.items.map(item => {
                const status = item.quantity === 0 ? 'نفد' : item.quantity <= (item.minStock || 10) ? 'منخفض' : 'متوفر';
                const statusClass = status === 'نفد' ? 'badge-danger' : status === 'منخفض' ? 'badge-warning' : 'badge-success';

                // تنسيق الأرقام (إنجليزية بدون أصفار زائدة)
                function formatNumber(num) {
                    return parseFloat(num.toFixed(2)).toLocaleString('en-US');
                }

                return `
                    <tr>
                        <td>${item.name}</td>
                        <td>${formatNumber(item.quantity)}</td>
                        <td>${item.unit}</td>
                        <td>${item.warehouse}</td>
                        <td><span class="badge ${statusClass}">${status}</span></td>
                        <td>${new Date(item.lastUpdated).toLocaleDateString('ar-EG')}</td>
                    </tr>
                `;
            }).join('');
        }

        function renderTransactionsTable() {
            const tbody = document.getElementById('transactionsTableBody');
            if (!tbody) {
                console.warn('عنصر transactionsTableBody غير موجود');
                return;
            }

            if (inventorySystem.transactions.length === 0) {
                tbody.innerHTML = '<tr><td colspan="12" style="text-align: center; color: #7f8c8d; padding: 40px;"><i class="fas fa-exchange-alt" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>لا توجد معاملات بعد<br><small>ابدأ بإضافة معاملة جديدة أو أضف بيانات تجريبية</small></td></tr>';
                return;
            }

            tbody.innerHTML = inventorySystem.transactions.slice().reverse().map(transaction => {
                const typeClass = transaction.transactionType === 'وارد' ? 'badge-success' :
                                 transaction.transactionType === 'صادر' ? 'badge-danger' : 'badge-warning';

                // دالة تنسيق الأرقام (إنجليزية بدون أصفار زائدة)
                function formatNumber(num) {
                    return parseFloat(num.toFixed(2)).toLocaleString('en-US');
                }

                // عرض سعر الصرف فقط للمعاملات التي تحتوي على أسعار (وارد وتسوية)
                const showExchangeRate = transaction.transactionType === 'وارد' || transaction.transactionType === 'تسوية' || transaction.transactionType === 'تالف' || transaction.transactionType === 'مرتجع';
                const exchangeRateDisplay = showExchangeRate ? (transaction.exchangeRateAtTime ? formatNumber(transaction.exchangeRateAtTime) : '-') : '-';

                return `
                    <tr>
                        <td>
                            <div>${new Date(transaction.date).toLocaleDateString('ar-EG')}</div>
                            <small style="color: #666;">${new Date(transaction.date).toLocaleTimeString('ar-EG', {hour: '2-digit', minute: '2-digit', hour12: true})}</small>
                        </td>
                        <td>${transaction.itemName}</td>
                        <td><span class="badge ${typeClass}">${transaction.transactionType}</span></td>
                        <td>${formatNumber(transaction.quantity)} ${transaction.unit}</td>
                        <td>${formatNumber(transaction.unitPrice)} ${transaction.currency}</td>
                        <td>${exchangeRateDisplay}</td>
                        <td>${formatNumber(transaction.totalPrice)}</td>
                        <td>${transaction.warehouse}</td>
                        <td>${transaction.targetWarehouse || '-'}</td>
                        <td>${transaction.supplier || '-'}</td>
                        <td>${transaction.invoiceNumber || '-'}</td>
                        <td>
                            <button class="action-btn delete" onclick="deleteTransaction(${transaction.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // دوال مساعدة
        function setCurrentDates() {
            const now = new Date();
            const today = now.toISOString().split('T')[0];
            const currentDateTime = now.toISOString().slice(0, 16);

            if (document.getElementById('dailyReportDate')) {
                document.getElementById('dailyReportDate').value = today;
            }
            if (document.getElementById('transactionDate')) {
                document.getElementById('transactionDate').value = currentDateTime;
            }
        }

        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-circle' : 'info-circle'}"></i> ${message}`;

            alertContainer.appendChild(alert);

            setTimeout(() => {
                alert.remove();
            }, 5000);
        }

        // دوال الإدارة
        function addSampleData() {
            const sampleItems = [
                { name: 'أسمنت عادي', quantity: 150, unit: 'كيس', unitPrice: 8500, currency: 'IQD', warehouse: 'المخزن الرئيسي' },
                { name: 'حديد تسليح', quantity: 75, unit: 'طن', unitPrice: 850, currency: 'USD', warehouse: 'المخزن الرئيسي' },
                { name: 'رمل نظيف', quantity: 300, unit: 'متر مكعب', unitPrice: 15000, currency: 'IQD', warehouse: 'مخزن المواد الخام' },
                { name: 'حصى مغسول', quantity: 200, unit: 'متر مكعب', unitPrice: 20000, currency: 'IQD', warehouse: 'مخزن المواد الخام' },
                { name: 'مفاتيح ربط', quantity: 50, unit: 'قطعة', unitPrice: 15000, currency: 'IQD', warehouse: 'مخزن الأدوات' },
                { name: 'فلاتر زيت', quantity: 100, unit: 'قطعة', unitPrice: 25, currency: 'USD', warehouse: 'مخزن قطع الغيار' }
            ];

            sampleItems.forEach(item => {
                const newItem = {
                    id: Date.now() + Math.random(),
                    ...item,
                    exchangeRateAtPurchase: inventorySystem.exchangeRate,
                    purchaseDate: new Date().toISOString(),
                    minStock: 20,
                    category: 'مواد بناء',
                    created: new Date().toISOString(),
                    lastUpdated: new Date().toISOString()
                };
                inventorySystem.items.push(newItem);

                // إضافة معاملة وارد مع سعر الصرف
                const transaction = {
                    id: Date.now() + Math.random(),
                    itemName: item.name,
                    transactionType: 'وارد',
                    quantity: item.quantity,
                    unit: item.unit,
                    unitPrice: item.unitPrice,
                    currency: item.currency,
                    warehouse: item.warehouse,
                    targetWarehouse: '',
                    supplier: 'مورد تجريبي - ' + item.name.split(' ')[0],
                    invoiceNumber: 'INV-' + Math.floor(Math.random() * 10000),
                    notes: 'بيانات تجريبية للنظام',
                    totalPrice: item.quantity * item.unitPrice,
                    date: new Date().toISOString(),
                    exchangeRateAtTime: inventorySystem.exchangeRate
                };
                inventorySystem.transactions.push(transaction);
            });

            saveData();
            updateAllStats();
            renderAllTables();
            showAlert('تم إضافة البيانات التجريبية بنجاح! تم إضافة ' + sampleItems.length + ' مادة و ' + sampleItems.length + ' معاملة.', 'success');
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟\n\nسيتم حذف:\n- جميع المواد\n- جميع المعاملات\n- جميع الإعدادات\n\nهذا الإجراء لا يمكن التراجع عنه!')) {
                inventorySystem.items = [];
                inventorySystem.transactions = [];
                saveData();
                updateAllStats();
                renderAllTables();
                showAlert('تم مسح جميع البيانات بنجاح!', 'success');
            }
        }

        function exportData() {
            const dataStr = JSON.stringify(inventorySystem, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'inventory-professional-' + new Date().toISOString().split('T')[0] + '.json';
            link.click();
            URL.revokeObjectURL(url);
            showAlert('تم تصدير البيانات بنجاح!', 'success');
        }

        function updateExchangeRate() {
            const newRate = parseFloat(document.getElementById('exchangeRate').value);
            if (newRate > 0) {
                inventorySystem.exchangeRate = newRate;
                saveData();
                updateAllStats();
                showAlert('تم تحديث سعر الصرف إلى ' + newRate + ' د.ع للدولار الواحد', 'success');
            } else {
                showAlert('يرجى إدخال سعر صرف صحيح', 'danger');
            }
        }

        function saveCompanyInfo() {
            inventorySystem.companyInfo = {
                name: document.getElementById('companyName').value,
                address: document.getElementById('companyAddress').value,
                phone: document.getElementById('companyPhone').value
            };
            saveData();
            showAlert('تم حفظ معلومات الشركة بنجاح!', 'success');
        }

        function deleteTransaction(id) {
            if (confirm('هل أنت متأكد من حذف هذه المعاملة؟\n\nتحذير: قد يؤثر هذا على أرصدة المخزون.')) {
                inventorySystem.transactions = inventorySystem.transactions.filter(t => t.id !== id);
                saveData();
                updateAllStats();
                renderTransactionsTable();
                showAlert('تم حذف المعاملة بنجاح!', 'success');
            }
        }

        // دوال التقارير
        function generateDailyReport() {
            const date = document.getElementById('dailyReportDate').value;
            if (!date) {
                showAlert('يرجى اختيار التاريخ', 'danger');
                return;
            }

            const transactions = inventorySystem.transactions.filter(t => {
                const transactionDate = new Date(t.date).toISOString().split('T')[0];
                return transactionDate === date;
            });

            displayReport('التقرير اليومي - ' + new Date(date).toLocaleDateString('ar-EG'), transactions);
            // إظهار أزرار الطباعة والتصدير
            document.getElementById('reportActions').style.display = 'block';
        }

        function generateWarehouseReport() {
            const warehouse = document.getElementById('warehouseReportSelect').value;
            if (!warehouse) {
                showAlert('يرجى اختيار المخزن', 'danger');
                return;
            }

            const transactions = inventorySystem.transactions.filter(t => t.warehouse === warehouse);
            const items = inventorySystem.items.filter(i => i.warehouse === warehouse);

            displayWarehouseReport('تقرير المخزن - ' + warehouse, transactions, items);
            // إظهار أزرار الطباعة والتصدير
            document.getElementById('reportActions').style.display = 'block';
        }

        function displayReport(title, transactions) {
            const resultsDiv = document.getElementById('reportResults');

            if (transactions.length === 0) {
                resultsDiv.innerHTML = `
                    <div class="panel">
                        <h3>${title}</h3>
                        <p style="text-align: center; color: #7f8c8d; padding: 40px;">
                            <i class="fas fa-file-alt" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>
                            لا توجد معاملات في هذه الفترة
                        </p>
                    </div>
                `;
                return;
            }

            const totalIncoming = transactions.filter(t => t.transactionType === 'وارد').reduce((sum, t) => sum + t.totalPrice, 0);
            const totalOutgoing = transactions.filter(t => t.transactionType === 'صادر').reduce((sum, t) => sum + t.totalPrice, 0);

            resultsDiv.innerHTML = `
                <div class="panel">
                    <h3>${title}</h3>
                    <div class="stats-grid" style="grid-template-columns: repeat(3, 1fr); margin: 20px 0;">
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-list"></i></div>
                            <div class="stat-number">${transactions.length}</div>
                            <div class="stat-label">إجمالي المعاملات</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-arrow-up"></i></div>
                            <div class="stat-number">${totalIncoming.toLocaleString()}</div>
                            <div class="stat-label">إجمالي الوارد</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-arrow-down"></i></div>
                            <div class="stat-number">${totalOutgoing.toLocaleString()}</div>
                            <div class="stat-label">إجمالي الصادر</div>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المادة</th>
                                    <th>النوع</th>
                                    <th>الكمية</th>
                                    <th>السعر</th>
                                    <th>سعر الصرف</th>
                                    <th>المجموع</th>
                                    <th>المخزن</th>
                                    <th>المورد/المستلم</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${transactions.map(t => {
                                    const showExchangeRate = t.transactionType === 'وارد' || t.transactionType === 'تسوية';
                                    const exchangeRateDisplay = showExchangeRate ? (t.exchangeRateAtTime || '-') : '-';
                                    return `
                                        <tr>
                                            <td>${new Date(t.date).toLocaleDateString('ar-EG')}</td>
                                            <td>${t.itemName}</td>
                                            <td><span class="badge ${t.transactionType === 'وارد' ? 'badge-success' : t.transactionType === 'صادر' ? 'badge-danger' : 'badge-warning'}">${t.transactionType}</span></td>
                                            <td>${t.quantity} ${t.unit}</td>
                                            <td>${t.unitPrice} ${t.currency}</td>
                                            <td>${exchangeRateDisplay}</td>
                                            <td>${t.totalPrice.toLocaleString()}</td>
                                            <td>${t.warehouse}</td>
                                            <td>${t.supplier || '-'}</td>
                                        </tr>
                                    `;
                                }).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }

        function displayWarehouseReport(title, transactions, items) {
            const resultsDiv = document.getElementById('reportResults');

            const totalValue = items.reduce((sum, item) => {
                const value = item.currency === 'USD' ? item.quantity * item.unitPrice * inventorySystem.exchangeRate : item.quantity * item.unitPrice;
                return sum + value;
            }, 0);

            resultsDiv.innerHTML = `
                <div class="panel">
                    <h3>${title}</h3>
                    <div class="stats-grid" style="grid-template-columns: repeat(3, 1fr); margin: 20px 0;">
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-boxes"></i></div>
                            <div class="stat-number">${items.length}</div>
                            <div class="stat-label">عدد المواد</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-exchange-alt"></i></div>
                            <div class="stat-number">${transactions.length}</div>
                            <div class="stat-label">عدد المعاملات</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-coins"></i></div>
                            <div class="stat-number">${totalValue.toLocaleString()}</div>
                            <div class="stat-label">القيمة الإجمالية (د.ع)</div>
                        </div>
                    </div>

                    <h4><i class="fas fa-warehouse"></i> المواد الحالية في المخزن:</h4>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>المادة</th>
                                    <th>الكمية</th>
                                    <th>الوحدة</th>
                                    <th>السعر</th>
                                    <th>القيمة الإجمالية</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${items.map(item => {
                                    const status = item.quantity === 0 ? 'نفد' : item.quantity <= (item.minStock || 10) ? 'منخفض' : 'متوفر';
                                    const statusClass = status === 'نفد' ? 'badge-danger' : status === 'منخفض' ? 'badge-warning' : 'badge-success';
                                    return `
                                        <tr>
                                            <td>${item.name}</td>
                                            <td>${item.quantity}</td>
                                            <td>${item.unit}</td>
                                            <td>${item.unitPrice} ${item.currency}</td>
                                            <td>${(item.quantity * item.unitPrice).toLocaleString()}</td>
                                            <td><span class="badge ${statusClass}">${status}</span></td>
                                        </tr>
                                    `;
                                }).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }

        function displayFilteredReport(title, transactions) {
            const resultsDiv = document.getElementById('reportResults');

            if (transactions.length === 0) {
                resultsDiv.innerHTML = `
                    <div class="panel">
                        <h3>${title}</h3>
                        <p style="text-align: center; color: #7f8c8d; padding: 40px;">
                            <i class="fas fa-file-alt" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>
                            لا توجد معاملات من النوع المحدد
                        </p>
                    </div>
                `;
                return;
            }

            // حساب الإحصائيات
            const totalIncoming = transactions.filter(t => t.transactionType === 'وارد').reduce((sum, t) => sum + (t.totalPrice || 0), 0);
            const totalOutgoing = transactions.filter(t => t.transactionType === 'صادر').reduce((sum, t) => sum + (t.totalPrice || 0), 0);
            const totalDamaged = transactions.filter(t => t.transactionType === 'تالف').reduce((sum, t) => sum + (t.totalPrice || 0), 0);
            const totalReturned = transactions.filter(t => t.transactionType === 'مرتجع').reduce((sum, t) => sum + (t.totalPrice || 0), 0);

            // إحصائيات حسب النوع المحدد
            let statsCards = '';
            if (currentReportFilter === 'all') {
                statsCards = `
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-arrow-down" style="color: #28a745;"></i></div>
                        <div class="stat-number">${totalIncoming.toLocaleString()}</div>
                        <div class="stat-label">إجمالي الوارد</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-arrow-up" style="color: #dc3545;"></i></div>
                        <div class="stat-number">${totalOutgoing.toLocaleString()}</div>
                        <div class="stat-label">إجمالي الصادر</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-exclamation-triangle" style="color: #ffc107;"></i></div>
                        <div class="stat-number">${totalDamaged.toLocaleString()}</div>
                        <div class="stat-label">إجمالي التالف</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-undo" style="color: #17a2b8;"></i></div>
                        <div class="stat-number">${totalReturned.toLocaleString()}</div>
                        <div class="stat-label">إجمالي المرتجع</div>
                    </div>
                `;
            } else {
                const currentTotal = transactions.reduce((sum, t) => sum + (t.totalPrice || 0), 0);
                const icon = currentReportFilter === 'وارد' ? 'arrow-down' :
                           currentReportFilter === 'صادر' ? 'arrow-up' :
                           currentReportFilter === 'تالف' ? 'exclamation-triangle' : 'undo';
                const color = currentReportFilter === 'وارد' ? '#28a745' :
                            currentReportFilter === 'صادر' ? '#dc3545' :
                            currentReportFilter === 'تالف' ? '#ffc107' : '#17a2b8';

                statsCards = `
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-list"></i></div>
                        <div class="stat-number">${transactions.length}</div>
                        <div class="stat-label">عدد المعاملات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-${icon}" style="color: ${color};"></i></div>
                        <div class="stat-number">${currentTotal.toLocaleString()}</div>
                        <div class="stat-label">إجمالي ${currentReportFilter}</div>
                    </div>
                `;
            }

            resultsDiv.innerHTML = `
                <div class="panel">
                    <h3>${title}</h3>
                    <div class="stats-grid" style="grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); margin: 20px 0;">
                        ${statsCards}
                    </div>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المادة</th>
                                    <th>النوع</th>
                                    <th>الكمية</th>
                                    <th>السعر</th>
                                    <th>سعر الصرف</th>
                                    <th>المجموع</th>
                                    <th>المخزن</th>
                                    <th>المورد/المستلم</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${transactions.map(t => {
                                    const showExchangeRate = t.transactionType === 'وارد' || t.transactionType === 'تسوية';
                                    const exchangeRateDisplay = showExchangeRate ? (t.exchangeRateAtTime || '-') : '-';
                                    const typeClass = t.transactionType === 'وارد' ? 'badge-success' :
                                                    t.transactionType === 'صادر' ? 'badge-danger' :
                                                    t.transactionType === 'تالف' ? 'badge-warning' : 'badge-info';
                                    return `
                                        <tr>
                                            <td>
                                                <div>${new Date(t.date).toLocaleDateString('ar-EG')}</div>
                                                <small style="color: #666;">${new Date(t.date).toLocaleTimeString('ar-EG', {hour: '2-digit', minute: '2-digit', hour12: true})}</small>
                                            </td>
                                            <td>${t.itemName}</td>
                                            <td><span class="badge ${typeClass}">${t.transactionType}</span></td>
                                            <td>${t.quantity} ${t.unit}</td>
                                            <td>${t.unitPrice || '-'} ${t.currency || ''}</td>
                                            <td>${exchangeRateDisplay}</td>
                                            <td>${(t.totalPrice || 0).toLocaleString()}</td>
                                            <td>${t.warehouse}</td>
                                            <td>${t.supplier || '-'}</td>
                                        </tr>
                                    `;
                                }).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        }

        // دالة معالجة تغيير نوع المعاملة
        function handleTransactionTypeChange() {
            const transactionType = document.getElementById('transactionType').value;
            const priceGroup = document.getElementById('priceGroup');
            const currencyGroup = document.getElementById('currencyGroup');
            const supplierLabel = document.getElementById('supplierLabel');
            const supplierInput = document.getElementById('supplier');
            const targetWarehouseGroup = document.getElementById('targetWarehouseGroup');
            const targetWarehouseLabel = document.getElementById('targetWarehouseLabel');

            // إخفاء المخزن المستهدف افتراضياً
            targetWarehouseGroup.style.display = 'none';

            // تحديث تسمية المورد/المستلم حسب نوع المعاملة
            if (transactionType === 'وارد') {
                supplierLabel.textContent = 'المورد';
                supplierInput.placeholder = 'اسم المورد أو الشركة المورّدة';
                // إظهار حقول السعر للوارد
                priceGroup.style.display = 'block';
                currencyGroup.style.display = 'block';
                document.getElementById('unitPrice').required = true;
                // تحديث حقل سعر الصرف حسب العملة
                handleCurrencyChange();
            } else if (transactionType === 'صادر') {
                supplierLabel.textContent = 'المستلم';
                supplierInput.placeholder = 'اسم المستلم أو الجهة المستلمة';
                // إظهار المخزن المستهدف للصادر
                targetWarehouseGroup.style.display = 'block';
                targetWarehouseLabel.textContent = 'المخزن المستهدف (إلى أين يذهب)';
                // إخفاء حقول السعر للصادر
                priceGroup.style.display = 'none';
                currencyGroup.style.display = 'none';
                document.getElementById('unitPrice').required = false;
            } else if (transactionType === 'تسوية') {
                supplierLabel.textContent = 'المسؤول عن التسوية';
                supplierInput.placeholder = 'اسم المسؤول عن عملية التسوية';
                // إظهار حقول السعر للتسوية
                priceGroup.style.display = 'block';
                currencyGroup.style.display = 'block';
                document.getElementById('unitPrice').required = true;
                // تحديث حقل سعر الصرف حسب العملة
                handleCurrencyChange();
            } else if (transactionType === 'تحويل') {
                supplierLabel.textContent = 'المسؤول عن التحويل';
                supplierInput.placeholder = 'اسم المسؤول عن التحويل';
                // إظهار المخزن المستهدف للتحويل
                targetWarehouseGroup.style.display = 'block';
                targetWarehouseLabel.textContent = 'المخزن المستهدف للتحويل';
                // إخفاء حقول السعر للتحويل
                priceGroup.style.display = 'none';
                currencyGroup.style.display = 'none';
                document.getElementById('unitPrice').required = false;
            } else if (transactionType === 'تالف') {
                supplierLabel.textContent = 'سبب التلف';
                supplierInput.placeholder = 'سبب تلف المواد (انتهاء صلاحية، كسر، إلخ)';
                // إظهار حقول السعر للتالف (لحساب الخسارة)
                priceGroup.style.display = 'block';
                currencyGroup.style.display = 'block';
                document.getElementById('unitPrice').required = true;
                handleCurrencyChange();
            } else if (transactionType === 'مرتجع') {
                supplierLabel.textContent = 'المورد المرتجع إليه';
                supplierInput.placeholder = 'اسم المورد الذي سيتم إرجاع المواد إليه';
                // إظهار حقول السعر للمرتجع (لحساب المبلغ المسترد)
                priceGroup.style.display = 'block';
                currencyGroup.style.display = 'block';
                document.getElementById('unitPrice').required = true;
                handleCurrencyChange();
            } else {
                supplierLabel.textContent = 'المورد/المستلم';
                supplierInput.placeholder = 'اسم المورد أو المستلم';
                // إخفاء حقول السعر
                priceGroup.style.display = 'none';
                currencyGroup.style.display = 'none';
                document.getElementById('unitPrice').required = false;
                // إخفاء حقل سعر الصرف
                const exchangeRateGroup = document.getElementById('exchangeRateGroup');
                if (exchangeRateGroup) {
                    exchangeRateGroup.style.display = 'none';
                }
            }
        }

        function resetTransactionForm() {
            document.getElementById('transactionForm').reset();
            setCurrentDates();
            handleTransactionTypeChange();
            handleCurrencyChange();
        }

        // دوال الفلترة (مبسطة)
        function applyFilters() {
            renderTransactionsTable();
        }

        function clearFilters() {
            document.getElementById('filterType').value = '';
            document.getElementById('filterWarehouse').value = '';
            document.getElementById('filterDateFrom').value = '';
            document.getElementById('filterDateTo').value = '';
            document.getElementById('searchItem').value = '';
            applyFilters();
        }

        function exportFilteredData() {
            showAlert('ميزة التصدير المفلتر قيد التطوير', 'warning');
        }

        function showQuickAdd() {
            showAlert('ميزة الإضافة السريعة قيد التطوير', 'warning');
        }

        // دوال الميزانية
        function updateBudgetStats() {
            const dateFrom = document.getElementById('budgetDateFrom').value;
            const dateTo = document.getElementById('budgetDateTo').value;
            const warehouse = document.getElementById('budgetWarehouse').value;

            // فلترة المعاملات حسب المعايير
            let filteredTransactions = inventorySystem.transactions.filter(t => {
                // فلترة حسب النوع (وارد، تالف، مرتجع فقط)
                if (!['وارد', 'تالف', 'مرتجع'].includes(t.transactionType)) return false;

                // فلترة حسب التاريخ
                if (dateFrom && new Date(t.date) < new Date(dateFrom)) return false;
                if (dateTo && new Date(t.date) > new Date(dateTo + 'T23:59:59')) return false;

                // فلترة حسب المخزن
                if (warehouse && t.warehouse !== warehouse) return false;

                return true;
            });

            // حساب المجاميع
            let totalIncomingIQD = 0;
            let totalIncomingUSD = 0;
            let totalOutgoingIQD = 0;
            let totalOutgoingUSD = 0;

            filteredTransactions.forEach(t => {
                const exchangeRate = t.exchangeRateAtTime || inventorySystem.exchangeRate;
                const totalPrice = t.quantity * t.unitPrice;

                if (t.transactionType === 'وارد') {
                    // الوارد (+)
                    if (t.currency === 'USD') {
                        totalIncomingUSD += totalPrice;
                        totalIncomingIQD += totalPrice * exchangeRate;
                    } else {
                        totalIncomingIQD += totalPrice;
                        totalIncomingUSD += totalPrice / exchangeRate;
                    }
                } else if (t.transactionType === 'تالف' || t.transactionType === 'مرتجع') {
                    // التالف والمرتجع (-)
                    if (t.currency === 'USD') {
                        totalOutgoingUSD += totalPrice;
                        totalOutgoingIQD += totalPrice * exchangeRate;
                    } else {
                        totalOutgoingIQD += totalPrice;
                        totalOutgoingUSD += totalPrice / exchangeRate;
                    }
                }
            });

            // فلترة معاملات المحروقات
            let filteredFuelTransactions = inventorySystem.fuelTransactions.filter(t => {
                if (!['وارد', 'مرتجع'].includes(t.transactionType)) return false;
                if (dateFrom && new Date(t.date) < new Date(dateFrom)) return false;
                if (dateTo && new Date(t.date) > new Date(dateTo + 'T23:59:59')) return false;
                return true;
            });

            // حساب معاملات المحروقات
            let totalFuelIncomingIQD = 0;
            let totalFuelIncomingUSD = 0;

            filteredFuelTransactions.forEach(transaction => {
                if (transaction.transactionType === 'وارد') {
                    totalFuelIncomingIQD += transaction.totalIQD || 0;
                    totalFuelIncomingUSD += transaction.totalUSD || 0;
                }
            });

            // حساب الصافي
            const netBudgetIQD = totalIncomingIQD - totalOutgoingIQD;
            const netBudgetUSD = totalIncomingUSD - totalOutgoingUSD;

            // حساب المجموع الكلي (مواد + محروقات)
            const totalCombinedIQD = totalIncomingIQD + totalFuelIncomingIQD;
            const totalCombinedUSD = totalIncomingUSD + totalFuelIncomingUSD;

            // دالة إزالة الأصفار الزائدة وتحويل للأرقام الإنجليزية
            function formatNumber(num) {
                return parseFloat(num.toFixed(2)).toLocaleString('en-US');
            }

            // تحديث الواجهة
            document.getElementById('totalIncomingIQD').textContent = formatNumber(totalIncomingIQD);
            document.getElementById('totalIncomingUSD').textContent = formatNumber(totalIncomingUSD);
            document.getElementById('totalFuelIncomingIQD').textContent = formatNumber(totalFuelIncomingIQD);
            document.getElementById('totalFuelIncomingUSD').textContent = formatNumber(totalFuelIncomingUSD);
            document.getElementById('totalOutgoingIQD').textContent = formatNumber(totalOutgoingIQD);
            document.getElementById('totalOutgoingUSD').textContent = formatNumber(totalOutgoingUSD);
            document.getElementById('netBudgetIQD').textContent = formatNumber(netBudgetIQD);
            document.getElementById('netBudgetUSD').textContent = formatNumber(netBudgetUSD);
            document.getElementById('totalCombinedIQD').textContent = formatNumber(totalCombinedIQD);
            document.getElementById('totalCombinedUSD').textContent = formatNumber(totalCombinedUSD);

            // تحديث الجدول
            renderBudgetTable();
        }

        function renderBudgetTable() {
            const tbody = document.getElementById('budgetTableBody');
            const dateFrom = document.getElementById('budgetDateFrom').value;
            const dateTo = document.getElementById('budgetDateTo').value;
            const warehouse = document.getElementById('budgetWarehouse').value;

            let allTransactions = [];

            // جمع البيانات حسب النوع المحدد
            if (currentBudgetView === 'materials' || currentBudgetView === 'both') {
                // فلترة معاملات المواد
                let filteredMaterialTransactions = inventorySystem.transactions.filter(t => {
                    if (!['وارد', 'تالف', 'مرتجع'].includes(t.transactionType)) return false;
                    if (dateFrom && new Date(t.date) < new Date(dateFrom)) return false;
                    if (dateTo && new Date(t.date) > new Date(dateTo + 'T23:59:59')) return false;
                    if (warehouse && t.warehouse !== warehouse) return false;
                    return true;
                });

                // إضافة نوع البيانات للمعاملات
                filteredMaterialTransactions = filteredMaterialTransactions.map(t => ({...t, dataType: 'مواد'}));
                allTransactions = allTransactions.concat(filteredMaterialTransactions);
            }

            if (currentBudgetView === 'fuel' || currentBudgetView === 'both') {
                // فلترة معاملات المحروقات
                let filteredFuelTransactions = inventorySystem.fuelTransactions.filter(t => {
                    if (!['وارد', 'مرتجع'].includes(t.transactionType)) return false;
                    if (dateFrom && new Date(t.date) < new Date(dateFrom)) return false;
                    if (dateTo && new Date(t.date) > new Date(dateTo + 'T23:59:59')) return false;
                    return true;
                });

                // تحويل معاملات المحروقات لتتوافق مع تنسيق المواد
                filteredFuelTransactions = filteredFuelTransactions.map(t => ({
                    ...t,
                    dataType: 'محروقات',
                    warehouse: 'مخزن المحروقات',
                    currency: t.currency || 'IQD',
                    unitPrice: t.unitPrice || 0,
                    exchangeRateAtTime: t.exchangeRateAtTime || inventorySystem.exchangeRate
                }));

                allTransactions = allTransactions.concat(filteredFuelTransactions);
            }

            if (allTransactions.length === 0) {
                const emptyMessage = currentBudgetView === 'materials' ? 'لا توجد معاملات مواد' :
                                   currentBudgetView === 'fuel' ? 'لا توجد معاملات محروقات' :
                                   'لا توجد معاملات مالية';
                tbody.innerHTML = `<tr><td colspan="12" style="text-align: center; color: #7f8c8d; padding: 40px;"><i class="fas fa-calculator" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>${emptyMessage}<br><small>ابدأ بإضافة معاملات وارد أو مرتجع أو تالف</small></td></tr>`;
                updateBudgetDetailsCount(0);
                return;
            }

            // ترتيب المعاملات حسب التاريخ (الأحدث أولاً)
            allTransactions.sort((a, b) => new Date(b.date) - new Date(a.date));

            tbody.innerHTML = allTransactions.map(transaction => {
                // دالة تنسيق الأرقام (إنجليزية بدون أصفار زائدة)
                function formatNumber(num) {
                    return parseFloat(num.toFixed(2)).toLocaleString('en-US');
                }

                let totalIQD, totalUSD, exchangeRate;

                if (transaction.dataType === 'محروقات') {
                    // للمحروقات: استخدام القيم المحسوبة مسبقاً
                    totalIQD = transaction.totalIQD || 0;
                    totalUSD = transaction.totalUSD || 0;
                    exchangeRate = transaction.exchangeRateAtTime || inventorySystem.exchangeRate;
                } else {
                    // للمواد: حساب القيم
                    exchangeRate = transaction.exchangeRateAtTime || inventorySystem.exchangeRate;
                    const totalPrice = transaction.quantity * transaction.unitPrice;

                    if (transaction.currency === 'USD') {
                        totalUSD = totalPrice;
                        totalIQD = totalPrice * exchangeRate;
                    } else {
                        totalIQD = totalPrice;
                        totalUSD = totalPrice / exchangeRate;
                    }
                }

                const typeClass = transaction.transactionType === 'وارد' ? 'type-success' :
                                 transaction.transactionType === 'تالف' ? 'type-danger' :
                                 transaction.transactionType === 'مرتجع' ? 'type-warning' : 'type-info';

                // لون مختلف للمحروقات
                const dataTypeClass = transaction.dataType === 'محروقات' ? 'style="background-color: #fff3cd;"' : '';

                return `
                    <tr ${dataTypeClass}>
                        <td>${new Date(transaction.date).toLocaleDateString('ar-EG')}</td>
                        <td>
                            ${transaction.itemName}
                            <br><small style="color: #666;"><i class="fas fa-${transaction.dataType === 'محروقات' ? 'gas-pump' : 'boxes'}"></i> ${transaction.dataType}</small>
                        </td>
                        <td><span class="badge ${typeClass}">${transaction.transactionType}</span></td>
                        <td>${formatNumber(transaction.quantity)} ${transaction.unit}</td>
                        <td>${formatNumber(transaction.unitPrice)}</td>
                        <td>${transaction.currency}</td>
                        <td>${formatNumber(exchangeRate)}</td>
                        <td><span class="amount-iqd">${formatNumber(totalIQD)}</span></td>
                        <td><span class="amount-usd">${formatNumber(totalUSD)}</span></td>
                        <td>${transaction.warehouse}</td>
                        <td>${transaction.supplier || '-'}</td>
                    </tr>
                `;
            }).join('');

            updateBudgetDetailsCount(allTransactions.length);
        }

        function showBudgetDetails(type) {
            currentBudgetView = type;

            // تحديث أزرار الفلترة
            document.querySelectorAll('.budget-filter-btn').forEach(btn => btn.classList.remove('active'));

            // تحديث نص زر الطباعة
            const printText = document.getElementById('budgetPrintText');

            if (type === 'materials') {
                document.getElementById('budgetMaterialsBtn').classList.add('active');
                printText.textContent = 'طباعة تفاصيل المواد';
            } else if (type === 'fuel') {
                document.getElementById('budgetFuelBtn').classList.add('active');
                printText.textContent = 'طباعة تفاصيل المحروقات';
            } else if (type === 'both') {
                document.getElementById('budgetBothBtn').classList.add('active');
                printText.textContent = 'طباعة تفاصيل المواد والمحروقات';
            }

            // تحديث الجدول
            renderBudgetTable();
        }

        function updateBudgetDetailsCount(count) {
            const countElement = document.getElementById('budgetDetailsCount');

            let typeText = '';
            if (currentBudgetView === 'materials') {
                typeText = 'معاملات المواد';
            } else if (currentBudgetView === 'fuel') {
                typeText = 'معاملات المحروقات';
            } else {
                typeText = 'إجمالي المعاملات';
            }

            countElement.textContent = `${typeText}: ${count} معاملة`;
        }

        function printBudgetDetails() {
            const dateFrom = document.getElementById('budgetDateFrom').value;
            const dateTo = document.getElementById('budgetDateTo').value;
            const warehouse = document.getElementById('budgetWarehouse').value;

            let allTransactions = [];
            let reportTitle = '';
            let reportIcon = '';

            // جمع البيانات حسب النوع المحدد
            if (currentBudgetView === 'materials') {
                // فلترة معاملات المواد
                let filteredMaterialTransactions = inventorySystem.transactions.filter(t => {
                    if (!['وارد', 'تالف', 'مرتجع'].includes(t.transactionType)) return false;
                    if (dateFrom && new Date(t.date) < new Date(dateFrom)) return false;
                    if (dateTo && new Date(t.date) > new Date(dateTo + 'T23:59:59')) return false;
                    if (warehouse && t.warehouse !== warehouse) return false;
                    return true;
                });

                filteredMaterialTransactions = filteredMaterialTransactions.map(t => ({...t, dataType: 'مواد'}));
                allTransactions = filteredMaterialTransactions;
                reportTitle = 'تقرير تفاصيل المواد';
                reportIcon = 'fas fa-boxes';

            } else if (currentBudgetView === 'fuel') {
                // فلترة معاملات المحروقات
                let filteredFuelTransactions = inventorySystem.fuelTransactions.filter(t => {
                    if (!['وارد', 'مرتجع'].includes(t.transactionType)) return false;
                    if (dateFrom && new Date(t.date) < new Date(dateFrom)) return false;
                    if (dateTo && new Date(t.date) > new Date(dateTo + 'T23:59:59')) return false;
                    return true;
                });

                filteredFuelTransactions = filteredFuelTransactions.map(t => ({
                    ...t,
                    dataType: 'محروقات',
                    warehouse: 'مخزن المحروقات',
                    currency: t.currency || 'IQD',
                    unitPrice: t.unitPrice || 0,
                    exchangeRateAtTime: t.exchangeRateAtTime || inventorySystem.exchangeRate
                }));

                allTransactions = filteredFuelTransactions;
                reportTitle = 'تقرير تفاصيل المحروقات';
                reportIcon = 'fas fa-gas-pump';

            } else if (currentBudgetView === 'both') {
                // جمع المواد والمحروقات معاً
                let filteredMaterialTransactions = inventorySystem.transactions.filter(t => {
                    if (!['وارد', 'تالف', 'مرتجع'].includes(t.transactionType)) return false;
                    if (dateFrom && new Date(t.date) < new Date(dateFrom)) return false;
                    if (dateTo && new Date(t.date) > new Date(dateTo + 'T23:59:59')) return false;
                    if (warehouse && t.warehouse !== warehouse) return false;
                    return true;
                }).map(t => ({...t, dataType: 'مواد'}));

                let filteredFuelTransactions = inventorySystem.fuelTransactions.filter(t => {
                    if (!['وارد', 'مرتجع'].includes(t.transactionType)) return false;
                    if (dateFrom && new Date(t.date) < new Date(dateFrom)) return false;
                    if (dateTo && new Date(t.date) > new Date(dateTo + 'T23:59:59')) return false;
                    return true;
                }).map(t => ({
                    ...t,
                    dataType: 'محروقات',
                    warehouse: 'مخزن المحروقات',
                    currency: t.currency || 'IQD',
                    unitPrice: t.unitPrice || 0,
                    exchangeRateAtTime: t.exchangeRateAtTime || inventorySystem.exchangeRate
                }));

                allTransactions = [...filteredMaterialTransactions, ...filteredFuelTransactions];
                reportTitle = 'تقرير تفاصيل المواد والمحروقات';
                reportIcon = 'fas fa-list';
            }

            if (allTransactions.length === 0) {
                const emptyMessage = currentBudgetView === 'materials' ? 'لا توجد معاملات مواد للطباعة' :
                                   currentBudgetView === 'fuel' ? 'لا توجد معاملات محروقات للطباعة' :
                                   'لا توجد معاملات للطباعة';
                showAlert(emptyMessage, 'warning');
                return;
            }

            // ترتيب المعاملات حسب التاريخ (الأحدث أولاً)
            allTransactions.sort((a, b) => new Date(b.date) - new Date(a.date));

            const currentDate = new Date().toLocaleDateString('ar-EG');
            const currentTime = new Date().toLocaleTimeString('ar-EG', {hour: '2-digit', minute: '2-digit', hour12: true});

            // حساب الإحصائيات
            let totalIncomingIQD = 0;
            let totalIncomingUSD = 0;
            let totalOutgoingIQD = 0;
            let totalOutgoingUSD = 0;

            allTransactions.forEach(transaction => {
                let totalIQD, totalUSD;

                if (transaction.dataType === 'محروقات') {
                    totalIQD = transaction.totalIQD || 0;
                    totalUSD = transaction.totalUSD || 0;
                } else {
                    const exchangeRate = transaction.exchangeRateAtTime || inventorySystem.exchangeRate;
                    const totalPrice = transaction.quantity * transaction.unitPrice;

                    if (transaction.currency === 'USD') {
                        totalUSD = totalPrice;
                        totalIQD = totalPrice * exchangeRate;
                    } else {
                        totalIQD = totalPrice;
                        totalUSD = totalPrice / exchangeRate;
                    }
                }

                if (transaction.transactionType === 'وارد') {
                    totalIncomingIQD += totalIQD;
                    totalIncomingUSD += totalUSD;
                } else {
                    totalOutgoingIQD += totalIQD;
                    totalOutgoingUSD += totalUSD;
                }
            });

            const netIQD = totalIncomingIQD - totalOutgoingIQD;
            const netUSD = totalIncomingUSD - totalOutgoingUSD;

            // دالة تنسيق الأرقام
            function formatNumber(num) {
                return parseFloat(num.toFixed(2)).toLocaleString('en-US');
            }

            const printContent = `
                <div class="print-content">
                    <div class="print-header">
                        <div class="company-section">
                            <h1>هيمن كروب</h1>
                            <p class="company-subtitle">نظام إدارة المخزون</p>
                        </div>

                        <div class="report-info">
                            <h2><i class="${reportIcon}"></i> ${reportTitle}</h2>
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="label">التاريخ:</span>
                                    <span class="value">${currentDate}</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">الوقت:</span>
                                    <span class="value">${currentTime}</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">عدد المعاملات:</span>
                                    <span class="value">${allTransactions.length}</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">النوع:</span>
                                    <span class="value">${currentBudgetView === 'materials' ? 'المواد' :
                                                        currentBudgetView === 'fuel' ? 'المحروقات' : 'المواد والمحروقات'}</span>
                                </div>
                                ${dateFrom ? `
                                <div class="info-item">
                                    <span class="label">من تاريخ:</span>
                                    <span class="value">${new Date(dateFrom).toLocaleDateString('ar-EG')}</span>
                                </div>` : ''}
                                ${dateTo ? `
                                <div class="info-item">
                                    <span class="label">إلى تاريخ:</span>
                                    <span class="value">${new Date(dateTo).toLocaleDateString('ar-EG')}</span>
                                </div>` : ''}
                                ${warehouse ? `
                                <div class="info-item">
                                    <span class="label">المخزن:</span>
                                    <span class="value">${warehouse}</span>
                                </div>` : ''}
                            </div>
                        </div>
                    </div>

                    <div class="summary-section">
                        <h3>ملخص الميزانية</h3>
                        <table class="summary-table">
                            <thead>
                                <tr>
                                    <th>البيان</th>
                                    <th>المبلغ (د.ع)</th>
                                    <th>المبلغ ($)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="income-row">
                                    <td><i class="fas fa-arrow-down"></i> إجمالي الوارد</td>
                                    <td>${formatNumber(totalIncomingIQD)}</td>
                                    <td>${formatNumber(totalIncomingUSD)}</td>
                                </tr>
                                <tr class="expense-row">
                                    <td><i class="fas fa-arrow-up"></i> إجمالي المرتجع والتالف</td>
                                    <td>${formatNumber(totalOutgoingIQD)}</td>
                                    <td>${formatNumber(totalOutgoingUSD)}</td>
                                </tr>
                                <tr class="total-row">
                                    <td><strong><i class="fas fa-calculator"></i> صافي الميزانية</strong></td>
                                    <td><strong>${formatNumber(netIQD)}</strong></td>
                                    <td><strong>${formatNumber(netUSD)}</strong></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="summary-section">
                        <h3>تفاصيل المعاملات</h3>
                        <table class="summary-table">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المادة/المحروقات</th>
                                    <th>النوع</th>
                                    <th>الكمية</th>
                                    <th>سعر الوحدة</th>
                                    <th>العملة</th>
                                    <th>سعر الصرف</th>
                                    <th>المجموع (د.ع)</th>
                                    <th>المجموع ($)</th>
                                    <th>المخزن</th>
                                    <th>المورد</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${allTransactions.map(transaction => {
                                    let totalIQD, totalUSD, exchangeRate;

                                    if (transaction.dataType === 'محروقات') {
                                        totalIQD = transaction.totalIQD || 0;
                                        totalUSD = transaction.totalUSD || 0;
                                        exchangeRate = transaction.exchangeRateAtTime || inventorySystem.exchangeRate;
                                    } else {
                                        exchangeRate = transaction.exchangeRateAtTime || inventorySystem.exchangeRate;
                                        const totalPrice = transaction.quantity * transaction.unitPrice;

                                        if (transaction.currency === 'USD') {
                                            totalUSD = totalPrice;
                                            totalIQD = totalPrice * exchangeRate;
                                        } else {
                                            totalIQD = totalPrice;
                                            totalUSD = totalPrice / exchangeRate;
                                        }
                                    }

                                    const typeClass = transaction.transactionType === 'وارد' ? 'income-row' :
                                                     transaction.transactionType === 'تالف' ? 'expense-row' :
                                                     transaction.transactionType === 'مرتجع' ? 'expense-row' : '';

                                    return `
                                        <tr class="${typeClass}">
                                            <td>${new Date(transaction.date).toLocaleDateString('ar-EG')}</td>
                                            <td>
                                                ${transaction.itemName}
                                                <br><small><i class="fas fa-${transaction.dataType === 'محروقات' ? 'gas-pump' : 'boxes'}"></i> ${transaction.dataType}</small>
                                            </td>
                                            <td>${transaction.transactionType}</td>
                                            <td>${formatNumber(transaction.quantity)} ${transaction.unit}</td>
                                            <td>${formatNumber(transaction.unitPrice)}</td>
                                            <td>${transaction.currency}</td>
                                            <td>${formatNumber(exchangeRate)}</td>
                                            <td>${formatNumber(totalIQD)}</td>
                                            <td>${formatNumber(totalUSD)}</td>
                                            <td>${transaction.warehouse}</td>
                                            <td>${transaction.supplier || '-'}</td>
                                        </tr>
                                    `;
                                }).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;

            // إنشاء نافذة طباعة مؤقتة
            const printWindow = window.open('', '_blank', 'width=800,height=600');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>${reportTitle} - هيمن كروب</title>
                    <style>
                        ${getCommonPrintStyles()}
                        @media print {
                            /* إخفاء URL من أسفل الصفحة */
                            @page {
                                margin: 0.5in;
                                @bottom-left { content: ""; }
                                @bottom-center { content: ""; }
                                @bottom-right { content: ""; }
                                @top-left { content: ""; }
                                @top-center { content: ""; }
                                @top-right { content: ""; }
                            }
                        }
                    </style>
                </head>
                <body>
                    ${printContent}
                </body>
                </html>
            `);

            printWindow.document.close();
            setTimeout(() => {
                printWindow.print();
                printWindow.close();
            }, 500);

            const successMessage = currentBudgetView === 'materials' ? 'تم إرسال تقرير المواد للطباعة!' :
                                   currentBudgetView === 'fuel' ? 'تم إرسال تقرير المحروقات للطباعة!' :
                                   'تم إرسال تقرير المواد والمحروقات للطباعة!';
            showAlert(successMessage, 'success');
        }

        function clearBudgetFilters() {
            document.getElementById('budgetDateFrom').value = '';
            document.getElementById('budgetDateTo').value = '';
            document.getElementById('budgetWarehouse').value = '';
            updateBudgetStats();
        }

        function exportBudgetReport() {
            showAlert('ميزة تصدير تقرير الميزانية قيد التطوير', 'warning');
        }

        // دوال الطباعة والتصدير للمخزون
        function printInventory() {
            const currentDate = new Date().toLocaleDateString('ar-EG');
            const currentTime = new Date().toLocaleTimeString('ar-EG', {hour: '2-digit', minute: '2-digit', hour12: true});

            const printContent = `
                <div class="print-content">
                    <div class="print-header">
                        <div class="company-section">
                            <h1>هيمن كروب</h1>
                            <p class="company-subtitle">نظام إدارة المخزون</p>
                        </div>

                        <div class="report-info">
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="label">التاريخ:</span>
                                    <span class="value">${currentDate}</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">الوقت:</span>
                                    <span class="value">${currentTime}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="summary-section">
                        <h3>جدول المخزون</h3>
                        <table class="summary-table">
                            <thead>
                                <tr>
                                    <th>المادة</th>
                                    <th>الكمية</th>
                                    <th>الوحدة</th>
                                    <th>المخزن</th>
                                    <th>الحالة</th>
                                    <th>آخر تحديث</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${inventorySystem.items.map(item => {
                                    const status = item.quantity === 0 ? 'نفد' : item.quantity <= (item.minStock || 10) ? 'منخفض' : 'متوفر';
                                    const statusClass = status === 'نفد' ? 'status-danger' : status === 'منخفض' ? 'status-warning' : 'status-success';
                                    return `
                                        <tr>
                                            <td>${item.name}</td>
                                            <td>${item.quantity}</td>
                                            <td>${item.unit}</td>
                                            <td>${item.warehouse}</td>
                                            <td><span class="${statusClass}">${status}</span></td>
                                            <td>${new Date(item.lastUpdated).toLocaleDateString('ar-EG')}</td>
                                        </tr>
                                    `;
                                }).join('')}
                            </tbody>
                        </table>
                    </div>

                    <div class="transactions-section">
                        <h3>حركة المخزون الأخيرة</h3>
                        <table class="transactions-table">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المادة</th>
                                    <th>النوع</th>
                                    <th>الكمية</th>
                                    <th>المخزن</th>
                                    <th>المورد/المستلم</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${inventorySystem.transactions.slice(0, 10).reverse().map(transaction => {
                                    const typeClass = transaction.transactionType === 'وارد' ? 'type-success' :
                                                     transaction.transactionType === 'تالف' ? 'type-danger' :
                                                     transaction.transactionType === 'مرتجع' ? 'type-warning' : 'type-info';
                                    return `
                                        <tr>
                                            <td>${new Date(transaction.date).toLocaleDateString('ar-EG')}</td>
                                            <td>${transaction.itemName}</td>
                                            <td><span class="${typeClass}">${transaction.transactionType}</span></td>
                                            <td>${transaction.quantity} ${transaction.unit}</td>
                                            <td>${transaction.warehouse}</td>
                                            <td>${transaction.supplier || '-'}</td>
                                        </tr>
                                    `;
                                }).join('')}
                            </tbody>
                        </table>
                    </div>


                </div>
            `;

            // إنشاء نافذة طباعة مؤقتة
            const printWindow = window.open('', '_blank', 'width=800,height=600');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>تقرير المخزون - هيمن كروب</title>
                    <style>
                        ${getCommonPrintStyles()}
                    </style>
                </head>
                <body>
                    ${printContent}
                </body>
                </html>
            `);

            printWindow.document.close();
            setTimeout(() => {
                printWindow.print();
                printWindow.close();
            }, 500);

            showAlert('تم إرسال تقرير المخزون للطباعة!', 'success');
        }

        function exportInventoryToExcel() {
            showAlert('ميزة تصدير Excel للمخزون قيد التطوير', 'warning');
        }

        // دوال الطباعة والتصدير للتقارير
        function printCurrentReport() {
            const reportResults = document.getElementById('reportResults');
            if (!reportResults.innerHTML.trim()) {
                showAlert('لا يوجد تقرير لطباعته. يرجى إنشاء تقرير أولاً.', 'warning');
                return;
            }

            const currentDate = new Date().toLocaleDateString('ar-EG');
            const currentTime = new Date().toLocaleTimeString('ar-EG', {hour: '2-digit', minute: '2-digit', hour12: true});

            // الحصول على محتوى التقرير
            const reportContent = reportResults.innerHTML;

            const printContent = `
                <div class="print-content">
                    <div class="print-header">
                        <div class="company-section">
                            <h1>هيمن كروب</h1>
                            <p class="company-subtitle">نظام إدارة المخزون</p>
                        </div>

                        <div class="report-info">
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="label">التاريخ:</span>
                                    <span class="value">${currentDate}</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">الوقت:</span>
                                    <span class="value">${currentTime}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="report-content">
                        ${reportContent}
                    </div>
                </div>
            `;

            // إنشاء نافذة طباعة مؤقتة
            const printWindow = window.open('', '_blank', 'width=800,height=600');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>التقرير - هيمن كروب</title>
                    <style>
                        ${getCommonPrintStyles()}

                        .report-content .panel {
                            background: white !important;
                            border: none !important;
                            box-shadow: none !important;
                            margin: 20px 0;
                        }

                        .report-content .table {
                            border-collapse: collapse;
                            width: 100%;
                            margin: 20px 0;
                        }

                        .report-content .table th,
                        .report-content .table td {
                            border: 1px solid #34495e !important;
                            padding: 8px;
                            text-align: center;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        .report-content .table th {
                            background: linear-gradient(135deg, #3498db, #2980b9) !important;
                            color: white !important;
                            font-weight: bold;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        .report-content .badge-success {
                            background-color: #27ae60 !important;
                            color: white !important;
                            padding: 4px 8px;
                            border-radius: 12px;
                            font-size: 11px;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        .report-content .badge-danger {
                            background-color: #e74c3c !important;
                            color: white !important;
                            padding: 4px 8px;
                            border-radius: 12px;
                            font-size: 11px;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        .report-content .badge-warning {
                            background-color: #f39c12 !important;
                            color: white !important;
                            padding: 4px 8px;
                            border-radius: 12px;
                            font-size: 11px;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }
                        @media print {
                            /* إخفاء URL من أسفل الصفحة */
                            @page {
                                margin: 0.5in;
                                @bottom-left { content: ""; }
                                @bottom-center { content: ""; }
                                @bottom-right { content: ""; }
                                @top-left { content: ""; }
                                @top-center { content: ""; }
                                @top-right { content: ""; }
                            }
                        }
                    </style>
                </head>
                <body>
                    ${printContent}
                </body>
                </html>
            `);

            printWindow.document.close();
            setTimeout(() => {
                printWindow.print();
                printWindow.close();
            }, 500);

            showAlert('تم إرسال التقرير للطباعة!', 'success');
        }

        function exportReportToExcel() {
            const reportResults = document.getElementById('reportResults');
            if (!reportResults.innerHTML.trim()) {
                showAlert('لا يوجد تقرير لتصديره. يرجى إنشاء تقرير أولاً.', 'warning');
                return;
            }
            showAlert('ميزة تصدير Excel للتقارير قيد التطوير', 'warning');
        }

        // دالة مساعدة للحصول على أنماط الطباعة المشتركة
        function getCommonPrintStyles() {
            return `
                body {
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background: white;
                    color: black;
                    font-size: 12px;
                    line-height: 1.5;
                }

                .print-header {
                    margin-bottom: 30px;
                    border-bottom: 3px solid #333;
                    padding-bottom: 20px;
                }

                .company-section {
                    text-align: center;
                    margin-bottom: 20px;
                }

                .company-section h1 {
                    margin: 0 0 5px 0;
                    font-size: 24px;
                    font-weight: bold;
                    color: #333;
                }

                .company-subtitle {
                    margin: 0;
                    font-size: 12px;
                    color: #666;
                }

                .report-info h2 {
                    text-align: center;
                    margin: 0 0 15px 0;
                    font-size: 18px;
                    color: #444;
                }

                .info-grid {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 8px;
                    max-width: 500px;
                    margin: 0 auto;
                }

                .info-item {
                    display: flex;
                    justify-content: space-between;
                    padding: 5px 0;
                    border-bottom: 1px dotted #ccc;
                }

                .info-item .label {
                    font-weight: bold;
                    color: #555;
                }

                .info-item .value {
                    color: #333;
                }

                .summary-section {
                    margin: 30px 0;
                }

                .summary-section h3 {
                    text-align: center;
                    margin-bottom: 15px;
                    font-size: 16px;
                    color: #333;
                }

                .summary-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 0 auto;
                    font-size: 11px;
                }

                .summary-table th,
                .summary-table td {
                    border: 1px solid #333;
                    padding: 8px;
                    text-align: center;
                }

                .summary-table th {
                    background-color: #f5f5f5;
                    font-weight: bold;
                }

                .footer-note {
                    margin-top: 30px;
                    text-align: center;
                    font-size: 10px;
                    color: #666;
                }

                .footer-note p {
                    margin: 3px 0;
                }
            `;
        }

        function printBudget() {
            // الحصول على البيانات الحالية
            const dateFrom = document.getElementById('budgetDateFrom').value;
            const dateTo = document.getElementById('budgetDateTo').value;
            const warehouse = document.getElementById('budgetWarehouse').value;

            // الحصول على الإحصائيات الحالية
            const totalIncomingIQD = document.getElementById('totalIncomingIQD').textContent;
            const totalIncomingUSD = document.getElementById('totalIncomingUSD').textContent;
            const totalOutgoingIQD = document.getElementById('totalOutgoingIQD').textContent;
            const totalOutgoingUSD = document.getElementById('totalOutgoingUSD').textContent;
            const netBudgetIQD = document.getElementById('netBudgetIQD').textContent;
            const netBudgetUSD = document.getElementById('netBudgetUSD').textContent;

            // فلترة المعاملات للطباعة
            let filteredTransactions = inventorySystem.transactions.filter(t => {
                if (!['وارد', 'تالف', 'مرتجع'].includes(t.transactionType)) return false;
                if (dateFrom && new Date(t.date) < new Date(dateFrom)) return false;
                if (dateTo && new Date(t.date) > new Date(dateTo + 'T23:59:59')) return false;
                if (warehouse && t.warehouse !== warehouse) return false;
                return true;
            });

            // تحديد الفترة
            let periodText = 'جميع الفترات';
            if (dateFrom && dateTo) {
                periodText = `من ${new Date(dateFrom).toLocaleDateString('ar-EG')} إلى ${new Date(dateTo).toLocaleDateString('ar-EG')}`;
            } else if (dateFrom) {
                periodText = `من ${new Date(dateFrom).toLocaleDateString('ar-EG')}`;
            } else if (dateTo) {
                periodText = `حتى ${new Date(dateTo).toLocaleDateString('ar-EG')}`;
            }

            // تحديد المخزن
            const warehouseText = warehouse || 'جميع المخازن';

            // الحصول على التاريخ والوقت الحالي
            const now = new Date();
            const currentDate = now.toLocaleDateString('ar-EG');
            const currentTime = now.toLocaleTimeString('ar-EG', {hour: '2-digit', minute: '2-digit', hour12: true});

            // إنشاء محتوى الطباعة
            const printContent = `
                <div class="print-content">
                    <div class="print-header">
                        <div class="company-section">
                            <h1>هيمن كروب</h1>
                            <p class="company-subtitle">نظام إدارة المخزون</p>
                        </div>

                        <div class="report-info">
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="label">الفترة:</span>
                                    <span class="value">${periodText}</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">المخزن:</span>
                                    <span class="value">${warehouseText}</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">التاريخ:</span>
                                    <span class="value">${currentDate}</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">الوقت:</span>
                                    <span class="value">${currentTime}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="summary-section">
                        <h3>ملخص الميزانية</h3>
                        <table class="summary-table">
                            <thead>
                                <tr>
                                    <th>البيان</th>
                                    <th>المبلغ (د.ع)</th>
                                    <th>المبلغ ($)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="income-row">
                                    <td><strong>إجمالي الوارد</strong></td>
                                    <td><span class="amount-iqd">${totalIncomingIQD}</span></td>
                                    <td><span class="amount-usd">${totalIncomingUSD}</span></td>
                                </tr>
                                <tr class="expense-row">
                                    <td><strong>إجمالي المرتجع والتالف</strong></td>
                                    <td><span class="amount-iqd">${totalOutgoingIQD}</span></td>
                                    <td><span class="amount-usd">${totalOutgoingUSD}</span></td>
                                </tr>
                                <tr class="total-row">
                                    <td><strong>صافي الميزانية</strong></td>
                                    <td><strong><span class="amount-iqd">${netBudgetIQD}</span></strong></td>
                                    <td><strong><span class="amount-usd">${netBudgetUSD}</span></strong></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="transactions-section">
                        <h3>تفاصيل المعاملات المالية</h3>
                        <table class="transactions-table">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المادة</th>
                                    <th>النوع</th>
                                    <th>الكمية</th>
                                    <th>سعر الوحدة</th>
                                    <th>العملة</th>
                                    <th>سعر الصرف</th>
                                    <th>المجموع (د.ع)</th>
                                    <th>المجموع ($)</th>
                                    <th>المخزن</th>
                                    <th>المورد</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${filteredTransactions.slice().reverse().map(transaction => {
                                    const exchangeRate = transaction.exchangeRateAtTime || inventorySystem.exchangeRate;
                                    const totalPrice = transaction.quantity * transaction.unitPrice;

                                    // حساب المبالغ بالعملتين
                                    let totalIQD, totalUSD;
                                    if (transaction.currency === 'USD') {
                                        totalUSD = totalPrice;
                                        totalIQD = totalPrice * exchangeRate;
                                    } else {
                                        totalIQD = totalPrice;
                                        totalUSD = totalPrice / exchangeRate;
                                    }

                                    const typeClass = transaction.transactionType === 'وارد' ? 'type-success' :
                                                     transaction.transactionType === 'تالف' ? 'type-danger' :
                                                     transaction.transactionType === 'مرتجع' ? 'type-warning' : 'type-info';

                                    return `
                                        <tr>
                                            <td>${new Date(transaction.date).toLocaleDateString('ar-EG')}</td>
                                            <td>${transaction.itemName}</td>
                                            <td><span class="${typeClass}">${transaction.transactionType}</span></td>
                                            <td>${transaction.quantity} ${transaction.unit}</td>
                                            <td>${transaction.unitPrice}</td>
                                            <td>${transaction.currency}</td>
                                            <td>${exchangeRate}</td>
                                            <td><span class="amount-iqd">${Math.round(totalIQD).toLocaleString()}</span></td>
                                            <td><span class="amount-usd">${Math.round(totalUSD).toLocaleString()}</span></td>
                                            <td>${transaction.warehouse}</td>
                                            <td>${transaction.supplier || '-'}</td>
                                        </tr>
                                    `;
                                }).join('')}
                            </tbody>
                        </table>
                    </div>


                </div>
            `;

            // إنشاء نافذة طباعة مؤقتة
            const printWindow = window.open('', '_blank', 'width=800,height=600');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>تقرير الميزانية - هيمن كروب</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            margin: 0;
                            padding: 20px;
                            background: white;
                            color: black;
                            font-size: 12px;
                            line-height: 1.5;
                        }

                        .print-header {
                            margin-bottom: 30px;
                            border-bottom: 4px solid #3498db !important;
                            padding-bottom: 20px;
                            background: linear-gradient(135deg, #ecf0f1, #bdc3c7) !important;
                            padding: 20px;
                            border-radius: 10px;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        .company-section {
                            text-align: center;
                            margin-bottom: 20px;
                        }

                        .company-section h1 {
                            margin: 0 0 5px 0;
                            font-size: 24px;
                            font-weight: bold;
                            color: #2c3e50 !important;
                            background: linear-gradient(135deg, #3498db, #2980b9);
                            -webkit-background-clip: text;
                            -webkit-text-fill-color: transparent;
                            background-clip: text;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        .company-subtitle {
                            margin: 0;
                            font-size: 14px;
                            color: #7f8c8d !important;
                            font-weight: bold;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        .report-info h2 {
                            text-align: center;
                            margin: 0 0 15px 0;
                            font-size: 18px;
                            color: #444;
                        }

                        .info-grid {
                            display: grid;
                            grid-template-columns: 1fr 1fr;
                            gap: 8px;
                            max-width: 500px;
                            margin: 0 auto;
                        }

                        .info-item {
                            display: flex;
                            justify-content: space-between;
                            padding: 5px 0;
                            border-bottom: 1px dotted #ccc;
                        }

                        .info-item .label {
                            font-weight: bold;
                            color: #555;
                        }

                        .info-item .value {
                            color: #333;
                        }

                        .summary-section {
                            margin: 30px 0;
                        }

                        .summary-section h3 {
                            text-align: center;
                            margin-bottom: 15px;
                            font-size: 16px;
                            color: #333;
                        }

                        .summary-table {
                            width: 100%;
                            border-collapse: collapse;
                            margin: 0 auto;
                            max-width: 600px;
                        }

                        .summary-table th,
                        .summary-table td {
                            border: 2px solid #34495e !important;
                            padding: 12px;
                            text-align: center;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        .summary-table th {
                            background: linear-gradient(135deg, #3498db, #2980b9) !important;
                            color: white !important;
                            font-weight: bold;
                            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        .summary-table .total-row {
                            background: linear-gradient(135deg, #27ae60, #229954) !important;
                            color: white !important;
                            font-weight: bold;
                            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        .summary-table tr:nth-child(even) {
                            background-color: #ecf0f1 !important;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        .summary-table tr:nth-child(odd) {
                            background-color: #ffffff !important;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        .summary-table tr:hover {
                            background-color: #d5dbdb !important;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        .footer-note {
                            margin-top: 30px;
                            text-align: center;
                            font-size: 10px;
                            color: #666;
                        }

                        .footer-note p {
                            margin: 3px 0;
                        }

                        /* ألوان الحالات */
                        .status-success {
                            background-color: #27ae60 !important;
                            color: white !important;
                            padding: 4px 8px;
                            border-radius: 12px;
                            font-size: 11px;
                            font-weight: bold;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        .status-warning {
                            background-color: #f39c12 !important;
                            color: white !important;
                            padding: 4px 8px;
                            border-radius: 12px;
                            font-size: 11px;
                            font-weight: bold;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        .status-danger {
                            background-color: #e74c3c !important;
                            color: white !important;
                            padding: 4px 8px;
                            border-radius: 12px;
                            font-size: 11px;
                            font-weight: bold;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        /* ألوان أنواع المعاملات */
                        .type-success {
                            background-color: #27ae60 !important;
                            color: white !important;
                            padding: 4px 8px;
                            border-radius: 12px;
                            font-size: 11px;
                            font-weight: bold;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        .type-danger {
                            background-color: #e74c3c !important;
                            color: white !important;
                            padding: 4px 8px;
                            border-radius: 12px;
                            font-size: 11px;
                            font-weight: bold;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        .type-warning {
                            background-color: #f39c12 !important;
                            color: white !important;
                            padding: 4px 8px;
                            border-radius: 12px;
                            font-size: 11px;
                            font-weight: bold;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        .type-info {
                            background-color: #3498db !important;
                            color: white !important;
                            padding: 4px 8px;
                            border-radius: 12px;
                            font-size: 11px;
                            font-weight: bold;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        /* ألوان الأرقام */
                        .amount-iqd {
                            color: #27ae60 !important;
                            font-weight: bold;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        .amount-usd {
                            color: #3498db !important;
                            font-weight: bold;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        /* ألوان صفوف الميزانية */
                        .income-row {
                            background-color: #d5f4e6 !important;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        .expense-row {
                            background-color: #fadbd8 !important;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        /* جدول المعاملات */
                        .transactions-section {
                            margin-top: 30px;
                        }

                        .transactions-section h3 {
                            text-align: center;
                            margin-bottom: 15px;
                            font-size: 16px;
                            color: #333;
                        }

                        .transactions-table {
                            width: 100%;
                            border-collapse: collapse;
                            margin: 0 auto;
                            font-size: 10px;
                        }

                        .transactions-table th,
                        .transactions-table td {
                            border: 1px solid #34495e !important;
                            padding: 8px;
                            text-align: center;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        .transactions-table th {
                            background: linear-gradient(135deg, #34495e, #2c3e50) !important;
                            color: white !important;
                            font-weight: bold;
                            font-size: 10px;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        .transactions-table tr:nth-child(even) {
                            background-color: #f8f9fa !important;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        .transactions-table tr:nth-child(odd) {
                            background-color: #ffffff !important;
                            -webkit-print-color-adjust: exact;
                            print-color-adjust: exact;
                        }

                        /* تحسين الأرقام في جدول الميزانية */
                        .summary-table .total-row .amount-iqd,
                        .summary-table .total-row .amount-usd {
                            color: white !important;
                            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
                        }

                        @media print {
                            /* إخفاء URL من أسفل الصفحة */
                            @page {
                                margin: 0.5in;
                                @bottom-left { content: ""; }
                                @bottom-center { content: ""; }
                                @bottom-right { content: ""; }
                                @top-left { content: ""; }
                                @top-center { content: ""; }
                                @top-right { content: ""; }
                            }
                        }
                    </style>
                </head>
                <body>
                    ${printContent}
                </body>
                </html>
            `);

            printWindow.document.close();

            // انتظار تحميل المحتوى ثم الطباعة
            setTimeout(() => {
                printWindow.print();
                printWindow.close();
            }, 500);

            showAlert('تم إرسال التقرير للطباعة!', 'success');
        }

        // دالة معالجة تغيير العملة
        function handleCurrencyChange() {
            try {
                const currency = document.getElementById('currency');
                const exchangeRateGroup = document.getElementById('exchangeRateGroup');
                const transactionExchangeRate = document.getElementById('transactionExchangeRate');
                const currentExchangeRateSpan = document.getElementById('currentExchangeRate');

                if (!currency || !exchangeRateGroup || !transactionExchangeRate) {
                    console.warn('بعض عناصر العملة غير موجودة');
                    return;
                }

                const currencyValue = currency.value;

                if (currencyValue === 'USD') {
                    // إظهار حقل سعر الصرف للدولار
                    exchangeRateGroup.style.display = 'block';
                    transactionExchangeRate.required = true;
                    transactionExchangeRate.value = inventorySystem.exchangeRate;
                    if (currentExchangeRateSpan) {
                        currentExchangeRateSpan.textContent = inventorySystem.exchangeRate;
                    }
                } else {
                    // إخفاء حقل سعر الصرف للدينار
                    exchangeRateGroup.style.display = 'none';
                    transactionExchangeRate.required = false;
                    transactionExchangeRate.value = '';
                }
            } catch (error) {
                console.error('خطأ في معالجة تغيير العملة:', error);
            }
        }

        // دوال الصلاحيات
        function updatePermissions() {
            try {
                const permissions = inventorySystem.settings.permissions;

                // تحديث حالة الأزرار حسب الصلاحيات
                const deleteButtons = document.querySelectorAll('.action-btn.delete');
                const exportBtn = document.getElementById('exportBtn');
                const clearDataBtn = document.getElementById('clearDataBtn');

            // تطبيق صلاحية الحذف
            if (!document.getElementById('canDelete').checked) {
                deleteButtons.forEach(btn => {
                    btn.style.display = 'none';
                });
            } else {
                deleteButtons.forEach(btn => {
                    btn.style.display = 'inline-block';
                });
            }

            // تطبيق صلاحية التصدير
            if (!document.getElementById('canExport').checked) {
                if (exportBtn) exportBtn.style.display = 'none';
            } else {
                if (exportBtn) exportBtn.style.display = 'block';
            }

                // تطبيق صلاحية مسح البيانات
                const canClearDataCheck = document.getElementById('canClearData');
                if (canClearDataCheck && !canClearDataCheck.checked) {
                    if (clearDataBtn) clearDataBtn.style.display = 'none';
                } else {
                    if (clearDataBtn) clearDataBtn.style.display = 'block';
                }
            } catch (error) {
                console.error('خطأ في تحديث الصلاحيات:', error);
            }
        }

        function savePermissions() {
            inventorySystem.settings.permissions = {
                canDelete: document.getElementById('canDelete').checked,
                canEdit: document.getElementById('canEdit').checked,
                canExport: document.getElementById('canExport').checked,
                canClearData: document.getElementById('canClearData').checked,
                canManageSettings: document.getElementById('canManageSettings').checked
            };

            saveData();
            updatePermissions();
            showAlert('تم حفظ إعدادات الصلاحيات بنجاح!', 'success');
        }

        function checkPermission(action) {
            const permissions = inventorySystem.settings.permissions;

            switch(action) {
                case 'delete':
                    return permissions.canDelete;
                case 'edit':
                    return permissions.canEdit;
                case 'export':
                    return permissions.canExport;
                case 'clearData':
                    return permissions.canClearData;
                case 'manageSettings':
                    return permissions.canManageSettings;
                default:
                    return false;
            }
        }

        // تحديث دالة حذف المعاملة مع فحص الصلاحيات
        function deleteTransaction(id) {
            if (!checkPermission('delete')) {
                showAlert('ليس لديك صلاحية لحذف المعاملات', 'danger');
                return;
            }

            if (confirm('هل أنت متأكد من حذف هذه المعاملة؟\n\nتحذير: قد يؤثر هذا على أرصدة المخزون.')) {
                inventorySystem.transactions = inventorySystem.transactions.filter(t => t.id !== id);
                saveData();
                updateAllStats();
                renderTransactionsTable();
                showAlert('تم حذف المعاملة بنجاح!', 'success');
            }
        }

        // تحديث دالة مسح البيانات مع فحص الصلاحيات
        function clearAllData() {
            if (!checkPermission('clearData')) {
                showAlert('ليس لديك صلاحية لمسح البيانات', 'danger');
                return;
            }

            if (confirm('هل أنت متأكد من مسح جميع البيانات؟\n\nسيتم حذف:\n- جميع المواد\n- جميع المعاملات\n- جميع الإعدادات\n\nهذا الإجراء لا يمكن التراجع عنه!')) {
                inventorySystem.items = [];
                inventorySystem.transactions = [];
                saveData();
                updateAllStats();
                renderAllTables();
                showAlert('تم مسح جميع البيانات بنجاح!', 'success');
            }
        }

        // تحديث دالة التصدير مع فحص الصلاحيات
        function exportData() {
            if (!checkPermission('export')) {
                showAlert('ليس لديك صلاحية لتصدير البيانات', 'danger');
                return;
            }

            const dataStr = JSON.stringify(inventorySystem, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'inventory-professional-' + new Date().toISOString().split('T')[0] + '.json';
            link.click();
            URL.revokeObjectURL(url);
            showAlert('تم تصدير البيانات بنجاح!', 'success');
        }

        // تحديث دالة تحميل البيانات لتشمل الصلاحيات
        function loadData() {
            const savedData = localStorage.getItem('professionalInventorySystem');
            if (savedData) {
                try {
                    const parsed = JSON.parse(savedData);
                    inventorySystem = { ...inventorySystem, ...parsed };

                    // تحديث الحقول في الواجهة
                    document.getElementById('companyName').value = inventorySystem.companyInfo.name || 'هيمن كروب';
                    document.getElementById('companyAddress').value = inventorySystem.companyInfo.address || '';
                    document.getElementById('companyPhone').value = inventorySystem.companyInfo.phone || '';
                    document.getElementById('exchangeRate').value = inventorySystem.exchangeRate || 1500;

                    // تحديث سعر الصرف الحالي في النموذج
                    if (document.getElementById('currentExchangeRate')) {
                        document.getElementById('currentExchangeRate').textContent = inventorySystem.exchangeRate || 1500;
                    }

                    // تحديث إعدادات الصلاحيات
                    if (inventorySystem.settings.permissions) {
                        document.getElementById('canDelete').checked = inventorySystem.settings.permissions.canDelete;
                        document.getElementById('canEdit').checked = inventorySystem.settings.permissions.canEdit;
                        document.getElementById('canExport').checked = inventorySystem.settings.permissions.canExport;
                        document.getElementById('canClearData').checked = inventorySystem.settings.permissions.canClearData;
                        document.getElementById('canManageSettings').checked = inventorySystem.settings.permissions.canManageSettings;
                    }

                } catch (error) {
                    console.error('خطأ في تحميل البيانات:', error);
                    showAlert('خطأ في تحميل البيانات المحفوظة', 'danger');
                }
            }
        }

        // متغيرات الإيصالات
        let selectedTransactions = [];
        let currentFilter = 'all';

        // متغيرات التقارير
        let currentReportFilter = 'all';

        // متغيرات المحروقات
        let currentFuelFilter = 'all';

        // متغير نوع البيانات في الميزانية
        let currentBudgetView = 'materials';

        // دوال إدارة الإيصالات
        function updateReceiptTransactionsTable() {
            const tbody = document.getElementById('receiptTransactionsTable');
            if (!tbody) return;

            if (inventorySystem.transactions.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; color: #7f8c8d;">لا توجد معاملات</td></tr>';
                return;
            }

            // فلترة المعاملات حسب النوع المحدد
            let filteredTransactions = inventorySystem.transactions
                .filter(t => ['وارد', 'صادر', 'تالف', 'مرتجع'].includes(t.transactionType));

            if (currentFilter !== 'all') {
                filteredTransactions = filteredTransactions.filter(t => t.transactionType === currentFilter);
            }

            const recentTransactions = filteredTransactions.slice(-30).reverse();

            // تحديث عداد المعاملات المفلترة
            const filteredCountElement = document.getElementById('filteredCount');
            if (filteredCountElement) {
                if (currentFilter === 'all') {
                    filteredCountElement.textContent = `إجمالي: ${filteredTransactions.length} معاملة`;
                } else {
                    filteredCountElement.textContent = `${currentFilter}: ${filteredTransactions.length} معاملة`;
                }
            }

            if (recentTransactions.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; color: #7f8c8d;">لا توجد معاملات من الأنواع المطلوبة (وارد، صادر، تالف، مرتجع)<br><small>أضف معاملات من تبويب المعاملات أولاً</small></td></tr>';
                return;
            }



            tbody.innerHTML = recentTransactions.map(transaction => {
                const date = new Date(transaction.date);
                const typeClass = transaction.transactionType === 'وارد' ? 'badge-success' :
                                transaction.transactionType === 'صادر' ? 'badge-danger' :
                                transaction.transactionType === 'تالف' ? 'badge-warning' :
                                transaction.transactionType === 'مرتجع' ? 'badge-info' : 'badge-secondary';

                const isSelected = selectedTransactions.includes(transaction.id);

                // إصلاح مشكلة الكمية
                let quantity = transaction.quantity;
                if (typeof quantity === 'string') {
                    quantity = parseFloat(quantity) || 0;
                } else if (typeof quantity !== 'number') {
                    quantity = 0;
                }

                // دالة تنسيق الأرقام (إنجليزية بدون أصفار زائدة)
                function formatNumber(num) {
                    return parseFloat(num.toFixed(2)).toLocaleString('en-US');
                }

                return `
                    <tr ${isSelected ? 'style="background-color: #e3f2fd;"' : ''}>
                        <td>
                            <input type="checkbox" value="${transaction.id}"
                                   onchange="toggleTransactionSelection(${transaction.id})"
                                   ${isSelected ? 'checked' : ''}>
                        </td>
                        <td>
                            <div>${date.toLocaleDateString('ar-EG')}</div>
                            <small style="color: #666;">${date.toLocaleTimeString('ar-EG', {hour: '2-digit', minute: '2-digit', hour12: true})}</small>
                        </td>
                        <td><span class="badge ${typeClass}">${transaction.transactionType}</span></td>
                        <td><strong>${transaction.itemName}</strong></td>
                        <td>${formatNumber(quantity)} ${transaction.unit}</td>
                        <td>${transaction.supplier || 'غير محدد'}</td>
                        <td>
                            <button onclick="generateReceipt(${transaction.id})" class="btn" style="background: #3498db; padding: 5px 10px; font-size: 0.8rem;">
                                <i class="fas fa-receipt"></i> إيصال فردي
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');

            updateSelectionUI();
        }

        function toggleTransactionSelection(transactionId) {
            const index = selectedTransactions.indexOf(transactionId);
            if (index > -1) {
                selectedTransactions.splice(index, 1);
            } else {
                selectedTransactions.push(transactionId);
            }
            updateSelectionUI();
        }

        function toggleSelectAll() {
            const checkbox = document.getElementById('selectAllCheckbox');
            const recentTransactions = inventorySystem.transactions.slice(-30);

            if (checkbox.checked) {
                selectedTransactions = recentTransactions.map(t => t.id);
            } else {
                selectedTransactions = [];
            }

            updateReceiptTransactionsTable();
        }

        function selectAllTransactions() {
            const recentTransactions = inventorySystem.transactions.slice(-30);
            selectedTransactions = recentTransactions.map(t => t.id);
            document.getElementById('selectAllCheckbox').checked = true;
            updateReceiptTransactionsTable();
        }

        function clearSelection() {
            selectedTransactions = [];
            document.getElementById('selectAllCheckbox').checked = false;
            updateReceiptTransactionsTable();
        }

        function updateSelectionUI() {
            const count = selectedTransactions.length;
            const countElement = document.getElementById('selectedCount');
            const generateBtn = document.getElementById('generateReceiptBtn');

            if (count === 0) {
                countElement.textContent = 'لم يتم تحديد أي معاملة';
                generateBtn.disabled = true;
            } else {
                countElement.textContent = `تم تحديد ${count} معاملة`;
                generateBtn.disabled = false;
            }
        }

        // دالة فلترة المعاملات حسب النوع
        function filterByType(type) {
            currentFilter = type;

            // تحديث أزرار الفلترة
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById('filter' + (type === 'all' ? 'All' :
                type === 'وارد' ? 'Incoming' :
                type === 'صادر' ? 'Outgoing' :
                type === 'تالف' ? 'Damaged' : 'Returned')).classList.add('active');

            // مسح التحديد عند تغيير الفلتر
            clearSelection();

            // تحديث الجدول
            updateReceiptTransactionsTable();
        }

        // دالة إظهار/إخفاء الحقول الإضافية للوارد
        function toggleReceiptFields() {
            const transactionType = document.getElementById('receiptTransactionType').value;
            const extraFields = document.getElementById('receiptExtraFields');

            if (transactionType === 'وارد') {
                extraFields.style.display = 'block';
            } else {
                extraFields.style.display = 'none';
            }
        }

        function toggleReceiptFuelField() {
            const isFuel = document.getElementById('receiptIsFuel').checked;
            const receiptText = document.getElementById('receiptText');

            if (isFuel) {
                receiptText.style.background = '#fff3cd';
                receiptText.style.borderColor = '#ffc107';
            } else {
                receiptText.style.background = 'white';
                receiptText.style.borderColor = '#e9ecef';
            }
        }

        // دوال فلترة التقارير
        function filterReportByType(type) {
            currentReportFilter = type;

            // تحديث أزرار الفلترة
            document.querySelectorAll('#reports .filter-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById('reportFilter' + (type === 'all' ? 'All' :
                type === 'وارد' ? 'Incoming' :
                type === 'صادر' ? 'Outgoing' :
                type === 'تالف' ? 'Damaged' : 'Returned')).classList.add('active');

            // تحديث عداد المعاملات
            updateReportFilterCount();

            // تفعيل زر عرض التقرير
            document.getElementById('generateFilteredReportBtn').disabled = false;
        }

        function updateReportFilterCount() {
            const countElement = document.getElementById('reportFilterCount');
            const generateBtn = document.getElementById('generateFilteredReportBtn');

            let filteredTransactions = inventorySystem.transactions
                .filter(t => ['وارد', 'صادر', 'تالف', 'مرتجع'].includes(t.transactionType));

            if (currentReportFilter !== 'all') {
                filteredTransactions = filteredTransactions.filter(t => t.transactionType === currentReportFilter);
            }

            if (currentReportFilter === 'all') {
                countElement.textContent = `إجمالي: ${filteredTransactions.length} معاملة`;
            } else {
                countElement.textContent = `${currentReportFilter}: ${filteredTransactions.length} معاملة`;
            }

            generateBtn.disabled = filteredTransactions.length === 0;
        }

        function generateFilteredReport() {
            let filteredTransactions = inventorySystem.transactions
                .filter(t => ['وارد', 'صادر', 'تالف', 'مرتجع'].includes(t.transactionType));

            if (currentReportFilter !== 'all') {
                filteredTransactions = filteredTransactions.filter(t => t.transactionType === currentReportFilter);
            }

            if (filteredTransactions.length === 0) {
                showAlert('لا توجد معاملات من النوع المحدد', 'warning');
                return;
            }

            const title = currentReportFilter === 'all' ?
                'تقرير جميع المعاملات' :
                `تقرير معاملات ${currentReportFilter}`;

            displayFilteredReport(title, filteredTransactions);

            // إظهار أزرار الطباعة والتصدير
            document.getElementById('reportActions').style.display = 'block';
        }

        function generateReceipt(transactionId) {
            const transaction = inventorySystem.transactions.find(t => t.id === transactionId);
            if (!transaction) {
                showAlert('المعاملة غير موجودة', 'danger');
                return;
            }

            generateReceiptFromTransactions([transaction]);
        }

        function generateMultipleReceipt() {
            if (selectedTransactions.length === 0) {
                showAlert('يرجى تحديد معاملة واحدة على الأقل', 'warning');
                return;
            }

            const transactions = selectedTransactions.map(id =>
                inventorySystem.transactions.find(t => t.id === id)
            ).filter(t => t !== undefined);

            if (transactions.length === 0) {
                showAlert('لم يتم العثور على المعاملات المحددة', 'danger');
                return;
            }

            generateReceiptFromTransactions(transactions);
        }

        function generateReceiptFromTransactions(transactions) {
            // تحديث تاريخ ووقت الإيصال (نظام 12 ساعة)
            const now = new Date();
            document.getElementById('receiptDate').textContent = now.toLocaleDateString('ar-EG');
            document.getElementById('receiptTime').textContent = now.toLocaleTimeString('ar-EG', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });

            // رقم الإيصال (مجموعة من أرقام المعاملات أو رقم جديد)
            const receiptNumber = transactions.length === 1 ?
                transactions[0].id :
                `R-${Date.now()}`;
            document.getElementById('receiptNumber').textContent = receiptNumber;

            // تحديد النوع والشخص الافتراضي
            const firstTransaction = transactions[0];
            document.getElementById('receiptTransactionType').value = firstTransaction.transactionType;
            document.getElementById('receiptPersonName').value = firstTransaction.supplier || '';
            document.getElementById('receiverName').textContent = firstTransaction.supplier || '';

            // تحديث نص الإيصال
            updateReceiptText();

            // ملء جدول المواد
            const itemsTable = document.getElementById('receiptItemsTable');
            let itemsHTML = '';
            let itemNumber = 1;

            transactions.forEach(transaction => {
                // إصلاح مشكلة الكمية
                let quantity = transaction.quantity;
                if (typeof quantity === 'string') {
                    quantity = parseFloat(quantity) || 0;
                }

                itemsHTML += `
                    <tr>
                        <td style="border: 1px solid #333; padding: 12px; text-align: center;">${itemNumber}</td>
                        <td style="border: 1px solid #333; padding: 12px; text-align: center;">${transaction.itemName}</td>
                        <td style="border: 1px solid #333; padding: 12px; text-align: center;">${quantity.toLocaleString('ar-EG')}</td>
                        <td style="border: 1px solid #333; padding: 12px; text-align: center;">${transaction.unit}</td>
                        <td style="border: 1px solid #333; padding: 12px; text-align: center;">${transaction.notes || '-'}</td>
                    </tr>
                `;
                itemNumber++;
            });

            itemsTable.innerHTML = itemsHTML;

            // إظهار منطقة الإيصال
            document.getElementById('receiptArea').style.display = 'block';

            // التمرير إلى الإيصال
            document.getElementById('receiptArea').scrollIntoView({ behavior: 'smooth' });

            // مسح التحديد بعد إنشاء الإيصال
            clearSelection();
        }

        // دالة تحديث نص الإيصال حسب النوع والشخص
        function updateReceiptText() {
            const transactionType = document.getElementById('receiptTransactionType').value;
            const personName = document.getElementById('receiptPersonName').value.trim();
            const receiptTextElement = document.getElementById('receiptText');
            const receiverNameElement = document.getElementById('receiverName');
            const warehouseKeeperElement = document.getElementById('warehouseKeeperName');

            // تحديث اسم أمين المخزن
            const warehouseKeeperName = document.getElementById('receiptWarehouseKeeper')?.value.trim();
            if (warehouseKeeperName) {
                warehouseKeeperElement.textContent = warehouseKeeperName;
            } else {
                warehouseKeeperElement.textContent = '_______________';
            }

            let receiptText = '';

            // إضافة معلومات السعر للوارد
            let priceInfo = '';
            if (transactionType === 'وارد') {
                const price = document.getElementById('receiptPrice')?.value;
                const currency = document.getElementById('receiptCurrency')?.value;
                if (price && price > 0) {
                    priceInfo = ` بسعر ${parseFloat(price).toLocaleString('ar-EG')} ${currency === 'USD' ? 'دولار' : 'دينار عراقي'}`;
                }
            }

            // التحقق من نوع المحروقات
            const isFuel = document.getElementById('receiptIsFuel')?.checked;
            const itemType = isFuel ? 'المحروقات' : 'المواد';

            if (personName) {
                switch(transactionType) {
                    case 'وارد':
                        receiptText = `استُلمت من السيد <strong>${personName}</strong> ${itemType} التالية${priceInfo}:`;
                        break;
                    case 'صادر':
                        receiptText = `استلم السيد <strong>${personName}</strong> ${itemType} التالية:`;
                        break;
                    case 'تالف':
                        receiptText = `استُلمت ${itemType} التالفة التالية من السيد <strong>${personName}</strong>:`;
                        break;
                    case 'مرتجع':
                        receiptText = `استُلمت ${itemType} المرتجعة التالية من السيد <strong>${personName}</strong>:`;
                        break;
                    default:
                        receiptText = `تم تسليم/استلام ${itemType} التالية مع السيد <strong>${personName}</strong>:`;
                }
                receiverNameElement.textContent = personName;
            } else {
                switch(transactionType) {
                    case 'وارد':
                        receiptText = `استُلمت ${itemType} التالية${priceInfo}:`;
                        break;
                    case 'صادر':
                        receiptText = `تم تسليم ${itemType} التالية:`;
                        break;
                    case 'تالف':
                        receiptText = `استُلمت ${itemType} التالفة التالية:`;
                        break;
                    case 'مرتجع':
                        receiptText = `استُلمت ${itemType} المرتجعة التالية:`;
                        break;
                    default:
                        receiptText = `تم تسليم/استلام ${itemType} التالية:`;
                }
                receiverNameElement.textContent = '_______________';
            }

            receiptTextElement.innerHTML = receiptText;

            // إظهار رسالة تأكيد
            showAlert('تم تحديث نص الإيصال بنجاح!', 'success');
        }

        function groupTransactionsByTypeAndPerson(transactions) {
            const groups = {};

            transactions.forEach(transaction => {
                const key = `${transaction.transactionType}-${transaction.supplier}`;
                if (!groups[key]) {
                    groups[key] = {
                        transactionType: transaction.transactionType,
                        supplier: transaction.supplier,
                        transactions: []
                    };
                }
                groups[key].transactions.push(transaction);
            });

            return Object.values(groups);
        }

        function editReceipt() {
            // تفعيل وضع التعديل
            const receiptText = document.getElementById('receiptText');
            const transactionTypeSelect = document.getElementById('receiptTransactionType');
            const personNameInput = document.getElementById('receiptPersonName');

            // إظهار رسالة توضيحية
            showAlert('يمكنك الآن تعديل نوع المعاملة واسم الشخص من القوائم أعلاه، وسيتم تحديث النص تلقائياً', 'info');

            // تركيز على حقل الاسم
            personNameInput.focus();
            personNameInput.select();
        }

        function printReceipt() {
            const receiptContent = document.getElementById('receiptArea').innerHTML;
            const printWindow = window.open('', '_blank', 'width=800,height=600');

            printWindow.document.write(`
                <!DOCTYPE html>
                <html dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>إيصال - هيمن كروب</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            margin: 20px;
                            direction: rtl;
                            font-size: 14px;
                            line-height: 1.6;
                        }
                        table {
                            width: 100%;
                            border-collapse: collapse;
                            margin: 20px 0;
                        }
                        th, td {
                            border: 1px solid #333;
                            padding: 8px;
                            text-align: center;
                        }
                        .signature-section {
                            margin-top: 40px;
                            display: grid;
                            grid-template-columns: 1fr 1fr;
                            gap: 50px;
                        }
                        /* إخفاء عناصر التحكم عند الطباعة */
                        .receipt-controls,
                        .receipt-actions,
                        button,
                        input,
                        select {
                            display: none !important;
                        }
                        @media print {
                            body { margin: 0; }
                            .receipt-controls,
                            .receipt-actions,
                            button,
                            input,
                            select {
                                display: none !important;
                            }
                            /* إخفاء URL من أسفل الصفحة */
                            @page {
                                margin: 0.5in;
                                @bottom-left { content: ""; }
                                @bottom-center { content: ""; }
                                @bottom-right { content: ""; }
                                @top-left { content: ""; }
                                @top-center { content: ""; }
                                @top-right { content: ""; }
                            }
                        }
                    </style>
                </head>
                <body>
                    ${receiptContent}
                </body>
                </html>
            `);

            printWindow.document.close();
            printWindow.focus();
            printWindow.print();
        }

        function closeReceipt() {
            document.getElementById('receiptArea').style.display = 'none';
        }

        // تحديث جدول الإيصالات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateReceiptTransactionsTable();
            updateFuelStats();
            renderFuelTransactionsTable();
            setFuelCurrentDates();
            renderWarehousesTable();
            updateWarehouseStats();
            updateWarehouseOptions();
        });

        // دوال المحروقات
        function setFuelCurrentDates() {
            const now = new Date();
            const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
            document.getElementById('fuelTransactionDate').value = localDateTime;
            document.getElementById('fuelCurrentExchangeRate').textContent = inventorySystem.exchangeRate;
            document.getElementById('fuelExchangeRate').value = inventorySystem.exchangeRate;
        }

        function handleFuelTypeChange() {
            const transactionType = document.getElementById('fuelTransactionType').value;
            const priceGroup = document.getElementById('fuelPriceGroup');
            const currencyGroup = document.getElementById('fuelCurrencyGroup');
            const exchangeRateGroup = document.getElementById('fuelExchangeRateGroup');

            if (transactionType === 'وارد') {
                priceGroup.style.display = 'block';
                currencyGroup.style.display = 'block';
                document.getElementById('fuelUnitPrice').required = true;
                handleFuelCurrencyChange();
            } else {
                priceGroup.style.display = 'none';
                currencyGroup.style.display = 'none';
                exchangeRateGroup.style.display = 'none';
                document.getElementById('fuelUnitPrice').required = false;
            }
        }

        function handleFuelCurrencyChange() {
            const currency = document.getElementById('fuelCurrency').value;
            const exchangeRateGroup = document.getElementById('fuelExchangeRateGroup');

            if (currency === 'USD') {
                exchangeRateGroup.style.display = 'block';
                document.getElementById('fuelExchangeRate').required = true;
            } else {
                exchangeRateGroup.style.display = 'none';
                document.getElementById('fuelExchangeRate').required = false;
            }
        }

        function handleFuelTransactionSubmit(e) {
            e.preventDefault();

            const formData = {
                itemName: document.getElementById('fuelItemName').value,
                transactionType: document.getElementById('fuelTransactionType').value,
                quantity: parseFloat(document.getElementById('fuelQuantity').value),
                unit: document.getElementById('fuelUnit').value,
                unitPrice: parseFloat(document.getElementById('fuelUnitPrice').value) || 0,
                currency: document.getElementById('fuelCurrency').value,
                supplier: document.getElementById('fuelSupplier').value.trim(),
                notes: document.getElementById('fuelNotes').value.trim(),
                transactionDate: document.getElementById('fuelTransactionDate').value || new Date().toISOString(),
                customExchangeRate: parseFloat(document.getElementById('fuelExchangeRate').value) || null
            };

            // التحقق من البيانات
            if (!formData.itemName || !formData.transactionType || !formData.quantity || !formData.unit) {
                showAlert('يرجى ملء جميع الحقول المطلوبة', 'danger');
                return;
            }

            // التحقق من السعر للوارد
            if (formData.transactionType === 'وارد' && (!formData.unitPrice || formData.unitPrice <= 0)) {
                showAlert('يرجى إدخال سعر صحيح للمعاملة الواردة', 'danger');
                return;
            }

            try {
                // تحديد سعر الصرف المستخدم
                let exchangeRateToUse = inventorySystem.exchangeRate;
                if (formData.currency === 'USD' && formData.customExchangeRate) {
                    exchangeRateToUse = formData.customExchangeRate;
                }

                // حساب المجموع بالدينار والدولار
                const totalIQD = formData.transactionType === 'وارد' ?
                    (formData.currency === 'USD' ?
                        formData.quantity * formData.unitPrice * exchangeRateToUse :
                        formData.quantity * formData.unitPrice) : 0;

                const totalUSD = formData.transactionType === 'وارد' ?
                    (formData.currency === 'USD' ?
                        formData.quantity * formData.unitPrice :
                        formData.quantity * formData.unitPrice / exchangeRateToUse) : 0;

                // إنشاء معاملة المحروقات
                const fuelTransaction = {
                    id: Date.now(),
                    ...formData,
                    totalIQD: totalIQD,
                    totalUSD: totalUSD,
                    date: formData.transactionDate,
                    exchangeRateAtTime: exchangeRateToUse,
                    category: 'محروقات'
                };

                // إضافة المعاملة
                inventorySystem.fuelTransactions.push(fuelTransaction);

                // حفظ وتحديث
                saveData();
                updateFuelStats();
                renderFuelTransactionsTable();

                // إعادة تعيين النموذج
                document.getElementById('fuelTransactionForm').reset();
                setFuelCurrentDates();

                showAlert('تم إضافة معاملة المحروقات بنجاح!', 'success');

            } catch (error) {
                console.error('خطأ في إضافة معاملة المحروقات:', error);
                showAlert('خطأ في إضافة معاملة المحروقات: ' + error.message, 'danger');
            }
        }

        function updateFuelStats() {
            const fuelTypes = [...new Set(inventorySystem.fuelTransactions.map(t => t.itemName))];
            const incomingTransactions = inventorySystem.fuelTransactions.filter(t => t.transactionType === 'وارد');
            const outgoingTransactions = inventorySystem.fuelTransactions.filter(t => t.transactionType === 'صادر');
            const totalValueIQD = incomingTransactions.reduce((sum, t) => sum + (t.totalIQD || 0), 0);

            // دالة تنسيق الأرقام (إنجليزية بدون أصفار زائدة)
            function formatNumber(num) {
                return parseFloat(num.toFixed(2)).toLocaleString('en-US');
            }

            document.getElementById('fuelTotalItems').textContent = formatNumber(fuelTypes.length);
            document.getElementById('fuelIncomingTotal').textContent = formatNumber(incomingTransactions.length);
            document.getElementById('fuelOutgoingTotal').textContent = formatNumber(outgoingTransactions.length);
            document.getElementById('fuelTotalValue').textContent = formatNumber(totalValueIQD) + ' د.ع';
        }

        function renderFuelTransactionsTable() {
            const tbody = document.getElementById('fuelTransactionsTableBody');
            if (!tbody) return;

            let filteredTransactions = inventorySystem.fuelTransactions;

            if (currentFuelFilter !== 'all') {
                filteredTransactions = filteredTransactions.filter(t => t.transactionType === currentFuelFilter);
            }

            if (filteredTransactions.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="11" style="text-align: center; color: #7f8c8d; padding: 40px;">
                            <i class="fas fa-gas-pump" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>
                            ${currentFuelFilter === 'all' ? 'لا توجد معاملات محروقات بعد' : `لا توجد معاملات ${currentFuelFilter}`}
                            <br><small>ابدأ بإضافة معاملة محروقات جديدة</small>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = filteredTransactions.slice().reverse().map(transaction => {
                const date = new Date(transaction.date);
                const typeClass = transaction.transactionType === 'وارد' ? 'badge-success' :
                                transaction.transactionType === 'صادر' ? 'badge-danger' : 'badge-info';

                // دالة تنسيق الأرقام (إنجليزية بدون أصفار زائدة)
                function formatNumber(num) {
                    return parseFloat(num.toFixed(2)).toLocaleString('en-US');
                }

                return `
                    <tr>
                        <td>
                            <div>${date.toLocaleDateString('ar-EG')}</div>
                            <small style="color: #666;">${date.toLocaleTimeString('ar-EG', {hour: '2-digit', minute: '2-digit', hour12: true})}</small>
                        </td>
                        <td><strong>${transaction.itemName}</strong></td>
                        <td><span class="badge ${typeClass}">${transaction.transactionType}</span></td>
                        <td>${formatNumber(transaction.quantity)} ${transaction.unit}</td>
                        <td>${transaction.unitPrice ? formatNumber(transaction.unitPrice) : '-'}</td>
                        <td>${transaction.currency || '-'}</td>
                        <td>${transaction.exchangeRateAtTime ? formatNumber(transaction.exchangeRateAtTime) : '-'}</td>
                        <td>${transaction.totalIQD ? formatNumber(transaction.totalIQD) : '-'}</td>
                        <td>${transaction.totalUSD ? formatNumber(transaction.totalUSD) : '-'}</td>
                        <td>${transaction.supplier || '-'}</td>
                        <td>
                            <button class="action-btn delete" onclick="deleteFuelTransaction(${transaction.id})" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');

            // تحديث عداد المعاملات المفلترة
            updateFuelFilterCount();
        }

        function filterFuelByType(type) {
            currentFuelFilter = type;

            // تحديث أزرار الفلترة
            document.querySelectorAll('#fuel .filter-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById('fuelFilter' + (type === 'all' ? 'All' :
                type === 'وارد' ? 'Incoming' :
                type === 'صادر' ? 'Outgoing' : 'Returned')).classList.add('active');

            // تحديث الجدول
            renderFuelTransactionsTable();
        }

        function updateFuelFilterCount() {
            const countElement = document.getElementById('fuelFilteredCount');

            let filteredTransactions = inventorySystem.fuelTransactions;

            if (currentFuelFilter !== 'all') {
                filteredTransactions = filteredTransactions.filter(t => t.transactionType === currentFuelFilter);
            }

            if (currentFuelFilter === 'all') {
                countElement.textContent = `إجمالي: ${filteredTransactions.length} معاملة`;
            } else {
                countElement.textContent = `${currentFuelFilter}: ${filteredTransactions.length} معاملة`;
            }
        }

        function deleteFuelTransaction(id) {
            if (!confirm('هل أنت متأكد من حذف هذه المعاملة؟')) return;

            inventorySystem.fuelTransactions = inventorySystem.fuelTransactions.filter(t => t.id !== id);
            saveData();
            updateFuelStats();
            renderFuelTransactionsTable();
            showAlert('تم حذف معاملة المحروقات بنجاح', 'success');
        }

        function printFuelTransactions() {
            let filteredTransactions = inventorySystem.fuelTransactions;

            if (currentFuelFilter !== 'all') {
                filteredTransactions = filteredTransactions.filter(t => t.transactionType === currentFuelFilter);
            }

            if (filteredTransactions.length === 0) {
                showAlert('لا توجد معاملات محروقات للطباعة', 'warning');
                return;
            }

            const currentDate = new Date().toLocaleDateString('ar-EG');
            const currentTime = new Date().toLocaleTimeString('ar-EG', {hour: '2-digit', minute: '2-digit', hour12: true});

            const filterText = currentFuelFilter === 'all' ? 'جميع المعاملات' : `معاملات ${currentFuelFilter}`;

            const printContent = `
                <div class="print-content">
                    <div class="print-header">
                        <div class="company-section">
                            <h1>هيمن كروب</h1>
                            <p class="company-subtitle">إدارة المحروقات</p>
                        </div>

                        <div class="report-info">
                            <h2>تقرير ${filterText}</h2>
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="label">التاريخ:</span>
                                    <span class="value">${currentDate}</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">الوقت:</span>
                                    <span class="value">${currentTime}</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">عدد المعاملات:</span>
                                    <span class="value">${filteredTransactions.length}</span>
                                </div>
                                <div class="info-item">
                                    <span class="label">النوع:</span>
                                    <span class="value">${filterText}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="summary-section">
                        <h3>جدول معاملات المحروقات</h3>
                        <table class="summary-table">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>نوع المحروقات</th>
                                    <th>النوع</th>
                                    <th>الكمية</th>
                                    <th>سعر الوحدة</th>
                                    <th>العملة</th>
                                    <th>سعر الصرف</th>
                                    <th>المجموع (د.ع)</th>
                                    <th>المجموع ($)</th>
                                    <th>المورد/المستلم</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${filteredTransactions.slice().reverse().map(transaction => {
                                    const date = new Date(transaction.date);
                                    return `
                                        <tr>
                                            <td>${date.toLocaleDateString('ar-EG')}</td>
                                            <td>${transaction.itemName}</td>
                                            <td>${transaction.transactionType}</td>
                                            <td>${transaction.quantity.toLocaleString('ar-EG')} ${transaction.unit}</td>
                                            <td>${transaction.unitPrice ? transaction.unitPrice.toLocaleString('ar-EG') : '-'}</td>
                                            <td>${transaction.currency || '-'}</td>
                                            <td>${transaction.exchangeRateAtTime ? transaction.exchangeRateAtTime.toLocaleString('ar-EG') : '-'}</td>
                                            <td>${transaction.totalIQD ? transaction.totalIQD.toLocaleString('ar-EG') : '-'}</td>
                                            <td>${transaction.totalUSD ? transaction.totalUSD.toLocaleString('ar-EG') : '-'}</td>
                                            <td>${transaction.supplier || '-'}</td>
                                        </tr>
                                    `;
                                }).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;

            // إنشاء نافذة طباعة مؤقتة
            const printWindow = window.open('', '_blank', 'width=800,height=600');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>تقرير المحروقات - هيمن كروب</title>
                    <style>
                        ${getCommonPrintStyles()}
                        @media print {
                            /* إخفاء URL من أسفل الصفحة */
                            @page {
                                margin: 0.5in;
                                @bottom-left { content: ""; }
                                @bottom-center { content: ""; }
                                @bottom-right { content: ""; }
                                @top-left { content: ""; }
                                @top-center { content: ""; }
                                @top-right { content: ""; }
                            }
                        }
                    </style>
                </head>
                <body>
                    ${printContent}
                </body>
                </html>
            `);

            printWindow.document.close();
            setTimeout(() => {
                printWindow.print();
                printWindow.close();
            }, 500);

            showAlert('تم إرسال تقرير المحروقات للطباعة!', 'success');
        }

        function exportFuelToExcel() {
            showAlert('ميزة تصدير Excel للمحروقات قيد التطوير', 'warning');
        }

        // دوال إدارة المخازن
        function handleWarehouseSubmit(e) {
            e.preventDefault();

            const formData = {
                name: document.getElementById('warehouseName').value.trim(),
                description: document.getElementById('warehouseDescription').value.trim(),
                location: document.getElementById('warehouseLocation').value.trim(),
                manager: document.getElementById('warehouseManager').value.trim()
            };

            // التحقق من البيانات
            if (!formData.name) {
                showAlert('يرجى إدخال اسم المخزن', 'danger');
                return;
            }

            // التحقق من عدم تكرار الاسم
            if (inventorySystem.warehouses.some(w => w.name === formData.name)) {
                showAlert('اسم المخزن موجود مسبقاً', 'danger');
                return;
            }

            try {
                // إنشاء مخزن جديد
                const newWarehouse = {
                    id: Date.now(),
                    ...formData,
                    itemCount: 0,
                    isActive: true,
                    createdDate: new Date().toISOString()
                };

                // إضافة المخزن
                inventorySystem.warehouses.push(newWarehouse);

                // حفظ وتحديث
                saveData();
                updateWarehouseOptions();
                renderWarehousesTable();
                updateWarehouseStats();

                // إعادة تعيين النموذج
                document.getElementById('warehouseForm').reset();

                showAlert('تم إضافة المخزن بنجاح!', 'success');

            } catch (error) {
                console.error('خطأ في إضافة المخزن:', error);
                showAlert('خطأ في إضافة المخزن: ' + error.message, 'danger');
            }
        }

        function renderWarehousesTable() {
            const tbody = document.getElementById('warehousesTableBody');
            if (!tbody) return;

            if (inventorySystem.warehouses.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; color: #7f8c8d; padding: 40px;">
                            <i class="fas fa-warehouse" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>
                            لا توجد مخازن مضافة بعد
                            <br><small>ابدأ بإضافة مخزن جديد</small>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = inventorySystem.warehouses.map(warehouse => {
                // حساب عدد المواد في هذا المخزن
                const itemCount = inventorySystem.items.filter(item => item.warehouse === warehouse.name).length;

                // دالة تنسيق الأرقام
                function formatNumber(num) {
                    return parseFloat(num.toFixed(2)).toLocaleString('en-US');
                }

                return `
                    <tr>
                        <td><strong>${warehouse.name}</strong></td>
                        <td>${warehouse.description || '-'}</td>
                        <td>${warehouse.location || '-'}</td>
                        <td>${warehouse.manager || '-'}</td>
                        <td>${formatNumber(itemCount)}</td>
                        <td>${new Date(warehouse.createdDate).toLocaleDateString('ar-EG')}</td>
                        <td>
                            <button class="action-btn edit" onclick="editWarehouse(${warehouse.id})" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="action-btn delete" onclick="deleteWarehouse(${warehouse.id})" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function updateWarehouseStats() {
            const totalWarehouses = inventorySystem.warehouses.length;
            const activeWarehouses = inventorySystem.warehouses.filter(w => w.isActive).length;
            const totalItems = inventorySystem.items.length;
            const uniqueManagers = [...new Set(inventorySystem.warehouses.map(w => w.manager).filter(m => m))].length;

            // دالة تنسيق الأرقام
            function formatNumber(num) {
                return parseFloat(num.toFixed(2)).toLocaleString('en-US');
            }

            document.getElementById('totalWarehouses').textContent = formatNumber(totalWarehouses);
            document.getElementById('totalWarehouseItems').textContent = formatNumber(totalItems);
            document.getElementById('activeWarehouses').textContent = formatNumber(activeWarehouses);
            document.getElementById('warehouseManagers').textContent = formatNumber(uniqueManagers);
        }

        function updateWarehouseOptions() {
            // تحديث جميع قوائم المخازن في النظام
            const warehouseSelects = [
                'warehouse',
                'targetWarehouse',
                'filterWarehouse',
                'budgetWarehouse',
                'warehouseReportSelect'
            ];

            warehouseSelects.forEach(selectId => {
                const select = document.getElementById(selectId);
                if (select) {
                    // حفظ القيمة المحددة حالياً
                    const currentValue = select.value;

                    // مسح الخيارات الحالية (عدا الخيار الأول)
                    while (select.children.length > 1) {
                        select.removeChild(select.lastChild);
                    }

                    // إضافة المخازن الجديدة
                    inventorySystem.warehouses.forEach(warehouse => {
                        const option = document.createElement('option');
                        option.value = warehouse.name;
                        option.textContent = warehouse.name;
                        select.appendChild(option);
                    });

                    // استعادة القيمة المحددة إذا كانت لا تزال موجودة
                    if (currentValue && inventorySystem.warehouses.some(w => w.name === currentValue)) {
                        select.value = currentValue;
                    }
                }
            });
        }

        function deleteWarehouse(id) {
            const warehouse = inventorySystem.warehouses.find(w => w.id === id);
            if (!warehouse) return;

            // التحقق من وجود مواد في المخزن
            const itemsInWarehouse = inventorySystem.items.filter(item => item.warehouse === warehouse.name);
            if (itemsInWarehouse.length > 0) {
                showAlert(`لا يمكن حذف المخزن "${warehouse.name}" لأنه يحتوي على ${itemsInWarehouse.length} مادة`, 'danger');
                return;
            }

            if (!confirm(`هل أنت متأكد من حذف المخزن "${warehouse.name}"؟`)) return;

            inventorySystem.warehouses = inventorySystem.warehouses.filter(w => w.id !== id);
            saveData();
            updateWarehouseOptions();
            renderWarehousesTable();
            updateWarehouseStats();
            showAlert('تم حذف المخزن بنجاح', 'success');
        }

        function editWarehouse(id) {
            const warehouse = inventorySystem.warehouses.find(w => w.id === id);
            if (!warehouse) return;

            // ملء النموذج بالبيانات الحالية
            document.getElementById('warehouseName').value = warehouse.name;
            document.getElementById('warehouseDescription').value = warehouse.description || '';
            document.getElementById('warehouseLocation').value = warehouse.location || '';
            document.getElementById('warehouseManager').value = warehouse.manager || '';

            // تغيير النموذج لوضع التعديل
            const form = document.getElementById('warehouseForm');
            form.onsubmit = function(e) {
                e.preventDefault();
                updateWarehouse(id);
            };

            // تغيير نص الزر
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-save"></i> حفظ التعديل';
            submitBtn.className = 'btn btn-warning';

            // إضافة زر إلغاء
            if (!form.querySelector('.cancel-btn')) {
                const cancelBtn = document.createElement('button');
                cancelBtn.type = 'button';
                cancelBtn.className = 'btn btn-secondary cancel-btn';
                cancelBtn.innerHTML = '<i class="fas fa-times"></i> إلغاء';
                cancelBtn.onclick = cancelWarehouseEdit;
                submitBtn.parentNode.appendChild(cancelBtn);
            }

            showAlert('يمكنك الآن تعديل بيانات المخزن', 'info');
        }

        function updateWarehouse(id) {
            const warehouse = inventorySystem.warehouses.find(w => w.id === id);
            if (!warehouse) return;

            const formData = {
                name: document.getElementById('warehouseName').value.trim(),
                description: document.getElementById('warehouseDescription').value.trim(),
                location: document.getElementById('warehouseLocation').value.trim(),
                manager: document.getElementById('warehouseManager').value.trim()
            };

            // التحقق من البيانات
            if (!formData.name) {
                showAlert('يرجى إدخال اسم المخزن', 'danger');
                return;
            }

            // التحقق من عدم تكرار الاسم (عدا المخزن الحالي)
            if (inventorySystem.warehouses.some(w => w.name === formData.name && w.id !== id)) {
                showAlert('اسم المخزن موجود مسبقاً', 'danger');
                return;
            }

            // إذا تم تغيير اسم المخزن، تحديث جميع المواد والمعاملات
            if (warehouse.name !== formData.name) {
                // تحديث المواد
                inventorySystem.items.forEach(item => {
                    if (item.warehouse === warehouse.name) {
                        item.warehouse = formData.name;
                    }
                });

                // تحديث المعاملات
                inventorySystem.transactions.forEach(transaction => {
                    if (transaction.warehouse === warehouse.name) {
                        transaction.warehouse = formData.name;
                    }
                    if (transaction.targetWarehouse === warehouse.name) {
                        transaction.targetWarehouse = formData.name;
                    }
                });
            }

            // تحديث بيانات المخزن
            Object.assign(warehouse, formData);

            // حفظ وتحديث
            saveData();
            updateWarehouseOptions();
            renderWarehousesTable();
            updateWarehouseStats();
            cancelWarehouseEdit();

            showAlert('تم تحديث المخزن بنجاح!', 'success');
        }

        function cancelWarehouseEdit() {
            const form = document.getElementById('warehouseForm');

            // إعادة تعيين النموذج
            form.reset();
            form.onsubmit = handleWarehouseSubmit;

            // إعادة تعيين الزر
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-plus"></i> إضافة المخزن';
            submitBtn.className = 'btn btn-success';

            // إزالة زر الإلغاء
            const cancelBtn = form.querySelector('.cancel-btn');
            if (cancelBtn) {
                cancelBtn.remove();
            }
        }
    </script>
</body>
</html>
