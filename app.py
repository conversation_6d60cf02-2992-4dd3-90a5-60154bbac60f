#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المخزون - هيمن كروب
تطبيق سطح المكتب
"""

import tkinter as tk
from tkinter import ttk, messagebox
import webbrowser
import http.server
import socketserver
import threading
import os
import sys
import socket
from pathlib import Path

class InventoryApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("نظام إدارة المخزون - هيمن كروب")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)
        
        # تعيين الأيقونة إذا كانت متوفرة
        try:
            if os.path.exists("assets/icon.ico"):
                self.root.iconbitmap("assets/icon.ico")
        except:
            pass
            
        self.server = None
        self.server_thread = None
        self.port = self.find_free_port()
        
        self.setup_ui()
        self.start_server()
        
    def find_free_port(self):
        """البحث عن منفذ متاح"""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('', 0))
            s.listen(1)
            port = s.getsockname()[1]
        return port
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تكوين الشبكة
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # العنوان
        title_label = ttk.Label(main_frame, text="نظام إدارة المخزون", 
                               font=("Arial", 24, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))
        
        subtitle_label = ttk.Label(main_frame, text="هيمن كروب", 
                                  font=("Arial", 16))
        subtitle_label.grid(row=1, column=0, columnspan=2, pady=(0, 30))
        
        # معلومات الخادم
        server_frame = ttk.LabelFrame(main_frame, text="معلومات الخادم", padding="10")
        server_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        
        ttk.Label(server_frame, text="العنوان:").grid(row=0, column=0, sticky=tk.W)
        self.url_var = tk.StringVar(value=f"http://localhost:{self.port}")
        ttk.Label(server_frame, textvariable=self.url_var, 
                 foreground="blue").grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        ttk.Label(server_frame, text="الحالة:").grid(row=1, column=0, sticky=tk.W)
        self.status_var = tk.StringVar(value="جاري التشغيل...")
        self.status_label = ttk.Label(server_frame, textvariable=self.status_var)
        self.status_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        # الأزرار
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=20)
        
        self.open_btn = ttk.Button(button_frame, text="فتح النظام", 
                                  command=self.open_browser, state="disabled")
        self.open_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="إعادة تشغيل الخادم", 
                  command=self.restart_server).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="خروج", 
                  command=self.quit_app).pack(side=tk.LEFT)
        
        # منطقة المعلومات
        info_frame = ttk.LabelFrame(main_frame, text="معلومات النظام", padding="10")
        info_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 20))
        
        info_text = """الميزات المتوفرة:
• إدارة مخزون شاملة
• نظام مالي متقدم مع دعم العملتين
• إدارة المحروقات منفصلة
• نظام الإيصالات والطباعة
• تقارير مفصلة (يومية، أسبوعية، شهرية)
• إدارة المخازن المتعددة
• ميزانية ذكية مع فصل المواد والمحروقات

تعليمات الاستخدام:
1. اضغط "فتح النظام" لبدء العمل
2. استخدم F11 للعرض بملء الشاشة
3. البيانات محفوظة تلقائياً في المتصفح"""
        
        info_label = ttk.Label(info_frame, text=info_text, justify=tk.LEFT)
        info_label.pack(anchor=tk.W)
        
        main_frame.rowconfigure(4, weight=1)
        
        # معالج إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.quit_app)
        
    def start_server(self):
        """تشغيل خادم HTTP"""
        try:
            # تغيير المجلد إلى مجلد التطبيق
            os.chdir(os.path.dirname(os.path.abspath(__file__)))
            
            # إنشاء خادم HTTP
            handler = http.server.SimpleHTTPRequestHandler
            self.server = socketserver.TCPServer(("", self.port), handler)
            
            # تشغيل الخادم في thread منفصل
            self.server_thread = threading.Thread(target=self.server.serve_forever, daemon=True)
            self.server_thread.start()
            
            self.status_var.set("يعمل")
            self.status_label.configure(foreground="green")
            self.open_btn.configure(state="normal")
            
        except Exception as e:
            self.status_var.set(f"خطأ: {str(e)}")
            self.status_label.configure(foreground="red")
            messagebox.showerror("خطأ", f"فشل في تشغيل الخادم:\n{str(e)}")
            
    def restart_server(self):
        """إعادة تشغيل الخادم"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            
        self.port = self.find_free_port()
        self.url_var.set(f"http://localhost:{self.port}")
        self.status_var.set("جاري إعادة التشغيل...")
        self.status_label.configure(foreground="orange")
        self.open_btn.configure(state="disabled")
        
        # تأخير قصير ثم إعادة التشغيل
        self.root.after(1000, self.start_server)
        
    def open_browser(self):
        """فتح النظام في المتصفح"""
        try:
            url = f"http://localhost:{self.port}/index-professional.html"
            webbrowser.open(url)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح المتصفح:\n{str(e)}")
            
    def quit_app(self):
        """إغلاق التطبيق"""
        if messagebox.askokcancel("تأكيد الخروج", "هل تريد إغلاق نظام إدارة المخزون؟"):
            if self.server:
                self.server.shutdown()
                self.server.server_close()
            self.root.quit()
            
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    try:
        app = InventoryApp()
        app.run()
    except Exception as e:
        messagebox.showerror("خطأ فادح", f"فشل في تشغيل التطبيق:\n{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
