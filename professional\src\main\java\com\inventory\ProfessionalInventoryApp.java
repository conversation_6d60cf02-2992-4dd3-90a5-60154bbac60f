package com.inventory;

import com.inventory.gui.LoginDialog;
import com.inventory.gui.MainFrame;
import com.inventory.models.User;

import javax.swing.*;
import java.awt.*;

/**
 * التطبيق الرئيسي - نظام إدارة المخزون الاحترافي
 */
public class ProfessionalInventoryApp {
    
    public static void main(String[] args) {
        // تعيين خصائص النظام
        System.setProperty("file.encoding", "UTF-8");
        System.setProperty("awt.useSystemAAFontSettings", "on");
        System.setProperty("swing.aatext", "true");
        
        // تعيين Look and Feel
        setupLookAndFeel();
        
        // تشغيل التطبيق في Event Dispatch Thread
        SwingUtilities.invokeLater(() -> {
            try {
                startApplication();
            } catch (Exception e) {
                e.printStackTrace();
                showErrorDialog("خطأ في تشغيل التطبيق", e.getMessage());
            }
        });
    }
    
    private static void setupLookAndFeel() {
        try {
            // محاولة استخدام FlatLaf إذا كان متوفراً
            try {
                Class.forName("com.formdev.flatlaf.FlatLightLaf");
                UIManager.setLookAndFeel("com.formdev.flatlaf.FlatLightLaf");
                System.out.println("تم تطبيق FlatLaf Look and Feel");
            } catch (ClassNotFoundException e) {
                // استخدام النظام الافتراضي (تم تعطيله لتجنب مشاكل التوافق)
                System.out.println("تم تطبيق Look and Feel الافتراضي");
            }
            
            // تخصيص الألوان والخطوط
            customizeUIDefaults();
            
        } catch (Exception e) {
            System.err.println("فشل في تعيين Look and Feel: " + e.getMessage());
        }
    }
    
    private static void customizeUIDefaults() {
        // تعيين الخط الافتراضي
        Font defaultFont = new Font("Segoe UI", Font.PLAIN, 14);
        Font boldFont = new Font("Segoe UI", Font.BOLD, 14);
        
        UIManager.put("defaultFont", defaultFont);
        UIManager.put("Label.font", defaultFont);
        UIManager.put("Button.font", defaultFont);
        UIManager.put("TextField.font", defaultFont);
        UIManager.put("TextArea.font", defaultFont);
        UIManager.put("ComboBox.font", defaultFont);
        UIManager.put("Table.font", defaultFont);
        UIManager.put("TableHeader.font", boldFont);
        UIManager.put("Menu.font", defaultFont);
        UIManager.put("MenuItem.font", defaultFont);
        UIManager.put("TabbedPane.font", defaultFont);
        
        // تخصيص الألوان
        UIManager.put("Panel.background", Color.WHITE);
        UIManager.put("Button.background", new Color(245, 245, 245));
        UIManager.put("TextField.background", Color.WHITE);
        UIManager.put("TextArea.background", Color.WHITE);
        UIManager.put("Table.background", Color.WHITE);
        UIManager.put("Table.alternateRowColor", new Color(248, 248, 248));
        
        // ألوان التركيز
        UIManager.put("Button.select", new Color(33, 150, 243));
        UIManager.put("TextField.selectionBackground", new Color(33, 150, 243));
        UIManager.put("TextArea.selectionBackground", new Color(33, 150, 243));
        
        // حدود مخصصة
        UIManager.put("TextField.border", BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(200, 200, 200)),
            BorderFactory.createEmptyBorder(5, 8, 5, 8)
        ));
        
        UIManager.put("Button.border", BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(200, 200, 200)),
            BorderFactory.createEmptyBorder(8, 16, 8, 16)
        ));
    }
    
    private static void startApplication() {
        // عرض شاشة البداية
        showSplashScreen();
        
        // عرض نافذة تسجيل الدخول
        User authenticatedUser = LoginDialog.showLoginDialog(null);
        
        if (authenticatedUser != null) {
            // إنشاء وعرض الواجهة الرئيسية
            MainFrame mainFrame = new MainFrame(authenticatedUser);
            mainFrame.setVisible(true);
            
            System.out.println("تم تسجيل دخول المستخدم: " + authenticatedUser.getFullName());
        } else {
            System.out.println("تم إلغاء تسجيل الدخول");
            System.exit(0);
        }
    }
    
    private static void showSplashScreen() {
        JWindow splash = new JWindow();
        splash.setSize(400, 300);
        splash.setLocationRelativeTo(null);
        
        JPanel panel = new JPanel();
        panel.setLayout(new BoxLayout(panel, BoxLayout.Y_AXIS));
        panel.setBackground(new Color(33, 150, 243));
        panel.setBorder(BorderFactory.createEmptyBorder(50, 50, 50, 50));
        
        // شعار التطبيق
        JLabel logoLabel = new JLabel("📦");
        logoLabel.setFont(new Font("Segoe UI Emoji", Font.PLAIN, 48));
        logoLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        logoLabel.setForeground(Color.WHITE);
        
        // اسم التطبيق
        JLabel titleLabel = new JLabel("نظام إدارة المخزون الاحترافي");
        titleLabel.setFont(new Font("Segoe UI", Font.BOLD, 20));
        titleLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        titleLabel.setForeground(Color.WHITE);
        
        // الإصدار
        JLabel versionLabel = new JLabel("الإصدار 2.0");
        versionLabel.setFont(new Font("Segoe UI", Font.PLAIN, 14));
        versionLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        versionLabel.setForeground(new Color(200, 230, 255));
        
        // شريط التحميل
        JProgressBar progressBar = new JProgressBar();
        progressBar.setIndeterminate(true);
        progressBar.setStringPainted(true);
        progressBar.setString("جاري التحميل...");
        progressBar.setFont(new Font("Segoe UI", Font.PLAIN, 12));
        progressBar.setMaximumSize(new Dimension(300, 25));
        progressBar.setAlignmentX(Component.CENTER_ALIGNMENT);
        
        panel.add(logoLabel);
        panel.add(Box.createVerticalStrut(20));
        panel.add(titleLabel);
        panel.add(Box.createVerticalStrut(10));
        panel.add(versionLabel);
        panel.add(Box.createVerticalStrut(40));
        panel.add(progressBar);
        
        splash.add(panel);
        splash.setVisible(true);
        
        // إخفاء الشاشة بعد 3 ثوان
        Timer timer = new Timer(3000, e -> splash.dispose());
        timer.setRepeats(false);
        timer.start();
        
        // انتظار حتى يتم إخفاء الشاشة
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    private static void showErrorDialog(String title, String message) {
        JOptionPane.showMessageDialog(
            null,
            message,
            title,
            JOptionPane.ERROR_MESSAGE
        );
    }
    
    /**
     * معلومات التطبيق
     */
    public static class AppInfo {
        public static final String NAME = "نظام إدارة المخزون الاحترافي";
        public static final String VERSION = "2.0";
        public static final String AUTHOR = "فريق التطوير";
        public static final String COPYRIGHT = "© 2024 جميع الحقوق محفوظة";
        public static final String DESCRIPTION = "نظام شامل لإدارة المخزون مع ميزات احترافية متقدمة";
        
        public static String getFullInfo() {
            return String.format("""
                %s
                الإصدار: %s
                المطور: %s
                %s
                
                %s
                """, NAME, VERSION, AUTHOR, COPYRIGHT, DESCRIPTION);
        }
    }
    
    /**
     * إعدادات التطبيق
     */
    public static class AppSettings {
        public static final String DATABASE_NAME = "professional_inventory.db";
        public static final String BACKUP_FOLDER = "backups";
        public static final String REPORTS_FOLDER = "reports";
        public static final String TEMP_FOLDER = "temp";
        
        // إعدادات الواجهة
        public static final Color PRIMARY_COLOR = new Color(33, 150, 243);
        public static final Color SUCCESS_COLOR = new Color(76, 175, 80);
        public static final Color WARNING_COLOR = new Color(255, 152, 0);
        public static final Color ERROR_COLOR = new Color(244, 67, 54);
        public static final Color INFO_COLOR = new Color(33, 150, 243);
        
        // إعدادات الأمان
        public static final int PASSWORD_MIN_LENGTH = 6;
        public static final int SESSION_TIMEOUT_MINUTES = 60;
        public static final int MAX_LOGIN_ATTEMPTS = 3;
        
        // إعدادات النسخ الاحتياطي
        public static final boolean AUTO_BACKUP_ENABLED = true;
        public static final int BACKUP_INTERVAL_HOURS = 24;
        public static final int MAX_BACKUP_FILES = 30;
    }
}
