package models;

/**
 * نموذج المادة في المخزون
 */
public class Item {
    private int id;
    private String name;
    private String unit;
    private double currentStock;
    private double unitPrice;
    private String notes;
    
    // Constructor
    public Item() {}
    
    public Item(String name, String unit, double unitPrice) {
        this.name = name;
        this.unit = unit;
        this.unitPrice = unitPrice;
        this.currentStock = 0.0;
    }
    
    public Item(int id, String name, String unit, double currentStock, double unitPrice, String notes) {
        this.id = id;
        this.name = name;
        this.unit = unit;
        this.currentStock = currentStock;
        this.unitPrice = unitPrice;
        this.notes = notes;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getUnit() {
        return unit;
    }
    
    public void setUnit(String unit) {
        this.unit = unit;
    }
    
    public double getCurrentStock() {
        return currentStock;
    }
    
    public void setCurrentStock(double currentStock) {
        this.currentStock = currentStock;
    }
    
    public double getUnitPrice() {
        return unitPrice;
    }
    
    public void setUnitPrice(double unitPrice) {
        this.unitPrice = unitPrice;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    @Override
    public String toString() {
        return name + " (" + unit + ")";
    }
}
