package gui;

import database.DatabaseManager;
import models.Item;
import models.Transaction;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.util.List;

/**
 * الواجهة الرسومية الرئيسية لنظام إدارة المخزون
 */
public class InventoryGUI extends JFrame {
    private DatabaseManager dbManager;
    private JTabbedPane tabbedPane;
    
    // مكونات تبويب الإدخال
    private JTextField itemNameField;
    private JTextField quantityField;
    private JTextField unitField;
    private JComboBox<String> transactionTypeCombo;
    private JTextField projectField;
    private JTextField supplierReceiverField;
    private JTextField unitPriceField;
    private JTextArea notesArea;
    
    // مكونات تبويب المخزون
    private JTable inventoryTable;
    private DefaultTableModel inventoryTableModel;
    
    // مكونات تبويب المعاملات
    private JTable transactionsTable;
    private DefaultTableModel transactionsTableModel;
    
    // مكونات البحث
    private JTextField searchField;
    private JTable searchResultsTable;
    private DefaultTableModel searchTableModel;
    
    public InventoryGUI() {
        dbManager = new DatabaseManager();
        initializeGUI();
        loadData();
    }
    
    private void initializeGUI() {
        setTitle("نظام إدارة المخزون");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1000, 700);
        setLocationRelativeTo(null);
        
        // تعيين الخط العربي
        Font arabicFont = new Font("Arial Unicode MS", Font.PLAIN, 14);
        UIManager.put("Label.font", arabicFont);
        UIManager.put("Button.font", arabicFont);
        UIManager.put("TextField.font", arabicFont);
        UIManager.put("ComboBox.font", arabicFont);
        UIManager.put("Table.font", arabicFont);
        UIManager.put("TableHeader.font", arabicFont);
        
        // إنشاء التبويبات
        tabbedPane = new JTabbedPane();
        tabbedPane.setFont(arabicFont);
        
        // تبويب الإدخال
        JPanel inputPanel = createInputPanel();
        tabbedPane.addTab("إدخال البيانات", inputPanel);
        
        // تبويب المخزون
        JPanel inventoryPanel = createInventoryPanel();
        tabbedPane.addTab("المخزون الحالي", inventoryPanel);
        
        // تبويب المعاملات
        JPanel transactionsPanel = createTransactionsPanel();
        tabbedPane.addTab("سجل المعاملات", transactionsPanel);
        
        // تبويب البحث
        JPanel searchPanel = createSearchPanel();
        tabbedPane.addTab("البحث", searchPanel);
        
        add(tabbedPane);
    }
    
    private JPanel createInputPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.EAST;
        
        Font arabicFont = new Font("Arial Unicode MS", Font.PLAIN, 14);
        
        // اسم المادة
        gbc.gridx = 1; gbc.gridy = 0;
        panel.add(new JLabel("اسم المادة:"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        itemNameField = new JTextField(20);
        itemNameField.setFont(arabicFont);
        panel.add(itemNameField, gbc);
        
        // الكمية
        gbc.gridx = 1; gbc.gridy = 1; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("الكمية:"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        quantityField = new JTextField(20);
        quantityField.setFont(arabicFont);
        panel.add(quantityField, gbc);
        
        // الوحدة
        gbc.gridx = 1; gbc.gridy = 2; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("الوحدة:"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        unitField = new JTextField(20);
        unitField.setFont(arabicFont);
        panel.add(unitField, gbc);
        
        // نوع الحركة
        gbc.gridx = 1; gbc.gridy = 3; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("نوع الحركة:"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        transactionTypeCombo = new JComboBox<>(new String[]{"وارد", "صادر"});
        transactionTypeCombo.setFont(arabicFont);
        panel.add(transactionTypeCombo, gbc);
        
        // المشروع
        gbc.gridx = 1; gbc.gridy = 4; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("المشروع:"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        projectField = new JTextField(20);
        projectField.setFont(arabicFont);
        panel.add(projectField, gbc);
        
        // المورد/المستلم
        gbc.gridx = 1; gbc.gridy = 5; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("المورد/المستلم:"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        supplierReceiverField = new JTextField(20);
        supplierReceiverField.setFont(arabicFont);
        panel.add(supplierReceiverField, gbc);
        
        // السعر
        gbc.gridx = 1; gbc.gridy = 6; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("السعر (دينار):"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        unitPriceField = new JTextField(20);
        unitPriceField.setFont(arabicFont);
        panel.add(unitPriceField, gbc);
        
        // الملاحظات
        gbc.gridx = 1; gbc.gridy = 7; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("ملاحظات:"), gbc);
        gbc.gridx = 0; gbc.fill = GridBagConstraints.BOTH;
        notesArea = new JTextArea(3, 20);
        notesArea.setFont(arabicFont);
        JScrollPane notesScroll = new JScrollPane(notesArea);
        panel.add(notesScroll, gbc);
        
        // زر الحفظ
        gbc.gridx = 0; gbc.gridy = 8; gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.NONE; gbc.anchor = GridBagConstraints.CENTER;
        JButton saveButton = new JButton("حفظ");
        saveButton.setFont(arabicFont);
        saveButton.addActionListener(new SaveButtonListener());
        panel.add(saveButton, gbc);
        
        return panel;
    }
    
    private JPanel createInventoryPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        
        // إنشاء الجدول
        String[] columns = {"اسم المادة", "الوحدة", "الكمية المتاحة", "سعر الوحدة", "القيمة الإجمالية", "ملاحظات"};
        inventoryTableModel = new DefaultTableModel(columns, 0);
        inventoryTable = new JTable(inventoryTableModel);
        inventoryTable.setFont(new Font("Arial Unicode MS", Font.PLAIN, 12));
        inventoryTable.getTableHeader().setFont(new Font("Arial Unicode MS", Font.BOLD, 12));
        
        JScrollPane scrollPane = new JScrollPane(inventoryTable);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        // زر التحديث
        JButton refreshButton = new JButton("تحديث");
        refreshButton.setFont(new Font("Arial Unicode MS", Font.PLAIN, 14));
        refreshButton.addActionListener(e -> loadInventoryData());
        panel.add(refreshButton, BorderLayout.SOUTH);
        
        return panel;
    }
    
    private JPanel createTransactionsPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        
        // إنشاء الجدول
        String[] columns = {"التاريخ", "اسم المادة", "الكمية", "الوحدة", "نوع الحركة", "المشروع", "المورد/المستلم", "سعر الوحدة", "القيمة الإجمالية"};
        transactionsTableModel = new DefaultTableModel(columns, 0);
        transactionsTable = new JTable(transactionsTableModel);
        transactionsTable.setFont(new Font("Arial Unicode MS", Font.PLAIN, 12));
        transactionsTable.getTableHeader().setFont(new Font("Arial Unicode MS", Font.BOLD, 12));
        
        JScrollPane scrollPane = new JScrollPane(transactionsTable);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        // زر التحديث
        JButton refreshButton = new JButton("تحديث");
        refreshButton.setFont(new Font("Arial Unicode MS", Font.PLAIN, 14));
        refreshButton.addActionListener(e -> loadTransactionsData());
        panel.add(refreshButton, BorderLayout.SOUTH);
        
        return panel;
    }
    
    private JPanel createSearchPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        
        // لوحة البحث
        JPanel searchInputPanel = new JPanel(new FlowLayout());
        searchInputPanel.add(new JLabel("البحث:"));
        searchField = new JTextField(20);
        searchField.setFont(new Font("Arial Unicode MS", Font.PLAIN, 14));
        searchInputPanel.add(searchField);
        
        JButton searchButton = new JButton("بحث");
        searchButton.setFont(new Font("Arial Unicode MS", Font.PLAIN, 14));
        searchButton.addActionListener(e -> performSearch());
        searchInputPanel.add(searchButton);
        
        panel.add(searchInputPanel, BorderLayout.NORTH);
        
        // جدول النتائج
        String[] columns = {"التاريخ", "اسم المادة", "الكمية", "نوع الحركة", "المشروع", "المورد/المستلم", "القيمة الإجمالية"};
        searchTableModel = new DefaultTableModel(columns, 0);
        searchResultsTable = new JTable(searchTableModel);
        searchResultsTable.setFont(new Font("Arial Unicode MS", Font.PLAIN, 12));
        searchResultsTable.getTableHeader().setFont(new Font("Arial Unicode MS", Font.BOLD, 12));
        
        JScrollPane scrollPane = new JScrollPane(searchResultsTable);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    private void loadData() {
        loadInventoryData();
        loadTransactionsData();
    }
    
    private void loadInventoryData() {
        inventoryTableModel.setRowCount(0);
        List<Item> items = dbManager.getAllItems();
        
        for (Item item : items) {
            Object[] row = {
                item.getName(),
                item.getUnit(),
                item.getCurrentStock(),
                item.getUnitPrice(),
                item.getCurrentStock() * item.getUnitPrice(),
                item.getNotes()
            };
            inventoryTableModel.addRow(row);
        }
    }
    
    private void loadTransactionsData() {
        transactionsTableModel.setRowCount(0);
        List<Transaction> transactions = dbManager.getAllTransactions();
        
        for (Transaction transaction : transactions) {
            Object[] row = {
                transaction.getFormattedDate(),
                transaction.getItemName(),
                transaction.getQuantity(),
                transaction.getUnit(),
                transaction.getTransactionType(),
                transaction.getProject(),
                transaction.getSupplierOrReceiver(),
                transaction.getUnitPrice(),
                transaction.getTotalPrice()
            };
            transactionsTableModel.addRow(row);
        }
    }
    
    private void performSearch() {
        String searchTerm = searchField.getText().trim();
        if (searchTerm.isEmpty()) {
            JOptionPane.showMessageDialog(this, "يرجى إدخال كلمة البحث", "تنبيه", JOptionPane.WARNING_MESSAGE);
            return;
        }
        
        searchTableModel.setRowCount(0);
        List<Transaction> results = dbManager.searchTransactions(searchTerm);
        
        for (Transaction transaction : results) {
            Object[] row = {
                transaction.getFormattedDate(),
                transaction.getItemName(),
                transaction.getQuantity(),
                transaction.getTransactionType(),
                transaction.getProject(),
                transaction.getSupplierOrReceiver(),
                transaction.getTotalPrice()
            };
            searchTableModel.addRow(row);
        }
        
        if (results.isEmpty()) {
            JOptionPane.showMessageDialog(this, "لم يتم العثور على نتائج", "نتيجة البحث", JOptionPane.INFORMATION_MESSAGE);
        }
    }
    
    private class SaveButtonListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            try {
                // التحقق من صحة البيانات
                String itemName = itemNameField.getText().trim();
                String quantityText = quantityField.getText().trim();
                String unit = unitField.getText().trim();
                String project = projectField.getText().trim();
                String supplierReceiver = supplierReceiverField.getText().trim();
                String priceText = unitPriceField.getText().trim();
                String notes = notesArea.getText().trim();
                
                if (itemName.isEmpty() || quantityText.isEmpty() || unit.isEmpty()) {
                    JOptionPane.showMessageDialog(InventoryGUI.this, 
                        "يرجى ملء الحقول المطلوبة: اسم المادة، الكمية، الوحدة", 
                        "خطأ في البيانات", JOptionPane.ERROR_MESSAGE);
                    return;
                }
                
                double quantity = Double.parseDouble(quantityText);
                double unitPrice = priceText.isEmpty() ? 0.0 : Double.parseDouble(priceText);
                String transactionType = (String) transactionTypeCombo.getSelectedItem();
                
                // إنشاء المعاملة
                Transaction transaction = new Transaction(itemName, quantity, unit, transactionType,
                    project, supplierReceiver, unitPrice, notes);
                
                // حفظ المعاملة
                if (dbManager.addTransaction(transaction)) {
                    // إضافة المادة إلى قاعدة البيانات إذا لم تكن موجودة
                    Item item = new Item(itemName, unit, unitPrice);
                    item.setNotes(notes);
                    dbManager.addItem(item); // سيتم تجاهلها إذا كانت موجودة بالفعل
                    
                    JOptionPane.showMessageDialog(InventoryGUI.this, 
                        "تم حفظ البيانات بنجاح", "نجح الحفظ", JOptionPane.INFORMATION_MESSAGE);
                    
                    // مسح الحقول
                    clearInputFields();
                    
                    // تحديث البيانات
                    loadData();
                } else {
                    JOptionPane.showMessageDialog(InventoryGUI.this, 
                        "فشل في حفظ البيانات", "خطأ", JOptionPane.ERROR_MESSAGE);
                }
                
            } catch (NumberFormatException ex) {
                JOptionPane.showMessageDialog(InventoryGUI.this, 
                    "يرجى إدخال أرقام صحيحة في حقول الكمية والسعر", 
                    "خطأ في البيانات", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    private void clearInputFields() {
        itemNameField.setText("");
        quantityField.setText("");
        unitField.setText("");
        projectField.setText("");
        supplierReceiverField.setText("");
        unitPriceField.setText("");
        notesArea.setText("");
        transactionTypeCombo.setSelectedIndex(0);
    }
    
    @Override
    public void dispose() {
        dbManager.closeConnection();
        super.dispose();
    }
}
