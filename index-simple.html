<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المخزون - هيمن كروب</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid #dee2e6;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #007bff;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }
        
        .btn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
        
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .system-status {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 5px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-warehouse"></i> نظام إدارة المخزون</h1>
            <p>هيمن كروب - النسخة المبسطة للاختبار</p>
        </div>
        
        <div class="content">
            <div class="system-status">
                <h3><i class="fas fa-info-circle"></i> حالة النظام</h3>
                <p id="systemStatus">جاري التحقق من حالة النظام...</p>
            </div>
            
            <div id="alertContainer"></div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-boxes"></i></div>
                    <div class="stat-number" id="totalItems">0</div>
                    <div class="stat-label">إجمالي المواد</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-coins"></i></div>
                    <div class="stat-number" id="totalValue">0</div>
                    <div class="stat-label">القيمة الإجمالية (د.ع)</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-dollar-sign"></i></div>
                    <div class="stat-number" id="totalValueUSD">0</div>
                    <div class="stat-label">المجموع دولار</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-exchange-alt"></i></div>
                    <div class="stat-number" id="exchangeRateDisplay">1500</div>
                    <div class="stat-label">سعر الدولار</div>
                </div>
            </div>
            
            <div class="actions">
                <button class="btn btn-success" onclick="testSystem()">
                    <i class="fas fa-play"></i> اختبار النظام
                </button>
                
                <button class="btn" onclick="addSampleData()">
                    <i class="fas fa-plus"></i> إضافة بيانات تجريبية
                </button>
                
                <button class="btn btn-warning" onclick="openFullSystem()">
                    <i class="fas fa-external-link-alt"></i> فتح النظام الكامل
                </button>
                
                <button class="btn btn-danger" onclick="clearAllData()">
                    <i class="fas fa-trash"></i> مسح البيانات
                </button>
            </div>
        </div>
    </div>

    <script>
        // متغير النظام
        let inventoryManager = null;
        
        // دالة عرض الرسائل
        function showAlert(message, type = 'success') {
            const alertContainer = document.getElementById('alertContainer');
            const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            
            alertContainer.innerHTML = `
                <div class="alert ${alertClass}">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
                    ${message}
                </div>
            `;
            
            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }
        
        // اختبار النظام
        function testSystem() {
            console.log('🧪 بدء اختبار النظام...');
            
            try {
                if (typeof InventoryManager === 'undefined') {
                    throw new Error('InventoryManager غير محمل');
                }
                
                if (!inventoryManager) {
                    inventoryManager = new InventoryManager();
                }
                
                showAlert('✅ النظام يعمل بشكل طبيعي!', 'success');
                updateSystemStatus('✅ النظام محمل ويعمل بشكل طبيعي');
                
            } catch (error) {
                console.error('❌ خطأ في النظام:', error);
                showAlert('❌ خطأ في النظام: ' + error.message, 'danger');
                updateSystemStatus('❌ يوجد خطأ في النظام: ' + error.message);
            }
        }
        
        // تحديث حالة النظام
        function updateSystemStatus(message) {
            document.getElementById('systemStatus').innerHTML = message;
        }
        
        // فتح النظام الكامل
        function openFullSystem() {
            window.open('index.html', '_blank');
        }
        
        // مسح البيانات
        function clearAllData() {
            if (confirm('هل أنت متأكد من حذف جميع البيانات؟')) {
                localStorage.clear();
                location.reload();
            }
        }
        
        // تحميل النظام عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تحميل النظام المبسط...');
            updateSystemStatus('جاري تحميل النظام...');
            
            // تحميل ملف النظام الرئيسي
            const script = document.createElement('script');
            script.src = 'app.js';
            script.onload = function() {
                console.log('✅ تم تحميل app.js بنجاح');
                testSystem();
            };
            script.onerror = function() {
                console.error('❌ فشل في تحميل app.js');
                updateSystemStatus('❌ فشل في تحميل النظام الرئيسي');
                showAlert('❌ فشل في تحميل النظام الرئيسي', 'danger');
            };
            document.head.appendChild(script);
        });
    </script>
</body>
</html>
