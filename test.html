<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .alert {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار نظام إدارة المخزون</h1>
        
        <div id="alertContainer"></div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalItems">0</div>
                <div class="stat-label">إجمالي المواد</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalValue">0</div>
                <div class="stat-label">القيمة الإجمالية (د.ع)</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalValueUSD">0</div>
                <div class="stat-label">المجموع دولار</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="exchangeRateDisplay">1500</div>
                <div class="stat-label">سعر الدولار</div>
            </div>
        </div>
        
        <div style="margin: 20px 0;">
            <button class="btn" onclick="testSystem()">🧪 اختبار النظام</button>
            <button class="btn" onclick="addTestData()">📝 إضافة بيانات تجريبية</button>
            <button class="btn" onclick="clearData()">🗑️ مسح البيانات</button>
            <button class="btn" onclick="openMainSystem()">🚀 فتح النظام الرئيسي</button>
        </div>
        
        <div id="testResults"></div>
    </div>

    <script>
        // اختبار بسيط للنظام
        class SimpleInventoryTest {
            constructor() {
                this.items = JSON.parse(localStorage.getItem('inventory_items')) || [];
                this.transactions = JSON.parse(localStorage.getItem('inventory_transactions')) || [];
                this.exchangeRate = 1500;
                this.updateStats();
            }
            
            updateStats() {
                document.getElementById('totalItems').textContent = this.items.length;
                
                let totalValueIQD = 0;
                let totalValueUSD = 0;
                
                this.items.forEach(item => {
                    const itemValue = item.quantity * (item.unitPrice || 0);
                    if (item.currency === 'USD') {
                        totalValueUSD += itemValue;
                        totalValueIQD += (itemValue * this.exchangeRate);
                    } else {
                        totalValueIQD += itemValue;
                    }
                });
                
                document.getElementById('totalValue').textContent = totalValueIQD.toLocaleString('ar-EG');
                document.getElementById('totalValueUSD').textContent = totalValueUSD.toLocaleString('ar-EG');
                document.getElementById('exchangeRateDisplay').textContent = this.exchangeRate.toLocaleString('ar-EG');
            }
            
            showAlert(message, type) {
                const alertContainer = document.getElementById('alertContainer');
                const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
                
                alertContainer.innerHTML = `
                    <div class="alert ${alertClass}">
                        ${message}
                    </div>
                `;
                
                setTimeout(() => {
                    alertContainer.innerHTML = '';
                }, 3000);
            }
            
            addTestItem() {
                const testItem = {
                    id: Date.now(),
                    name: 'مادة اختبار',
                    quantity: 100,
                    unit: 'قطعة',
                    unitPrice: 1000,
                    currency: 'IQD',
                    minStock: 10,
                    category: 'اختبار',
                    warehouse: 'مخزن اختبار',
                    created: new Date().toISOString(),
                    lastUpdated: new Date().toISOString()
                };
                
                this.items.push(testItem);
                localStorage.setItem('inventory_items', JSON.stringify(this.items));
                this.updateStats();
                this.showAlert('تم إضافة مادة اختبار بنجاح!', 'success');
            }
            
            clearAllData() {
                localStorage.clear();
                this.items = [];
                this.transactions = [];
                this.updateStats();
                this.showAlert('تم مسح جميع البيانات!', 'success');
            }
        }
        
        let testSystem = null;
        
        document.addEventListener('DOMContentLoaded', () => {
            testSystem = new SimpleInventoryTest();
        });
        
        function testSystem() {
            if (testSystem) {
                testSystem.showAlert('النظام يعمل بشكل طبيعي! ✅', 'success');
                
                const results = document.getElementById('testResults');
                results.innerHTML = `
                    <div style="background: #e7f3ff; padding: 15px; border-radius: 8px; margin-top: 15px;">
                        <h3>🔍 نتائج الاختبار:</h3>
                        <ul>
                            <li>✅ تحميل JavaScript: نجح</li>
                            <li>✅ قراءة localStorage: نجح</li>
                            <li>✅ تحديث الإحصائيات: نجح</li>
                            <li>✅ عرض الرسائل: نجح</li>
                            <li>✅ النظام جاهز للاستخدام!</li>
                        </ul>
                        <p><strong>عدد المواد المحفوظة:</strong> ${testSystem.items.length}</p>
                        <p><strong>عدد المعاملات المحفوظة:</strong> ${testSystem.transactions.length}</p>
                    </div>
                `;
            } else {
                alert('خطأ: النظام لم يتم تحميله بشكل صحيح!');
            }
        }
        
        function addTestData() {
            if (testSystem) {
                testSystem.addTestItem();
            }
        }
        
        function clearData() {
            if (confirm('هل أنت متأكد من حذف جميع البيانات؟')) {
                if (testSystem) {
                    testSystem.clearAllData();
                }
            }
        }
        
        function openMainSystem() {
            window.open('index.html', '_blank');
        }
    </script>
</body>
</html>
