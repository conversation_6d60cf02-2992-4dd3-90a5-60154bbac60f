@echo off
chcp 65001 >nul
title إضافة أيقونة مخصصة
color 0A

echo.
echo ========================================
echo    إضافة أيقونة مخصصة للتطبيق
echo ========================================
echo.

REM إنشاء مجلد assets إذا لم يكن موجود
if not exist "assets" mkdir "assets"

echo 🎨 إضافة أيقونة مخصصة...
echo.

REM إنشاء ملف أيقونة بسيط (نص)
echo هذا ملف للأيقونة > "assets\icon-info.txt"
echo ======================== >> "assets\icon-info.txt"
echo. >> "assets\icon-info.txt"
echo لإضافة أيقونة مخصصة: >> "assets\icon-info.txt"
echo. >> "assets\icon-info.txt"
echo 1. ضع ملف icon.ico في مجلد assets >> "assets\icon-info.txt"
echo 2. تأكد من أن الحجم 256x256 أو أصغر >> "assets\icon-info.txt"
echo 3. أعد تشغيل "اعمل-كل-شي.bat" >> "assets\icon-info.txt"
echo. >> "assets\icon-info.txt"
echo مواقع مفيدة لإنشاء أيقونات: >> "assets\icon-info.txt"
echo - https://www.icoconverter.com >> "assets\icon-info.txt"
echo - https://convertio.co/png-ico >> "assets\icon-info.txt"
echo. >> "assets\icon-info.txt"
echo هيمن كروب - 2024 >> "assets\icon-info.txt"

REM إنشاء أيقونة افتراضية بسيطة (إذا لم تكن موجودة)
if not exist "assets\icon.ico" (
    echo 📁 إنشاء أيقونة افتراضية...
    
    REM محاولة إنشاء أيقونة بسيطة باستخدام PowerShell
    powershell -Command "& {
        Add-Type -AssemblyName System.Drawing
        $bitmap = New-Object System.Drawing.Bitmap(64, 64)
        $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
        $graphics.Clear([System.Drawing.Color]::Blue)
        $font = New-Object System.Drawing.Font('Arial', 20, [System.Drawing.FontStyle]::Bold)
        $brush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
        $graphics.DrawString('HG', $font, $brush, 10, 15)
        $graphics.Dispose()
        $bitmap.Save('assets\icon.png', [System.Drawing.Imaging.ImageFormat]::Png)
        $bitmap.Dispose()
    }" 2>nul
    
    if exist "assets\icon.png" (
        echo ✅ تم إنشاء أيقونة افتراضية
    ) else (
        echo ⚠️ لم يتم إنشاء أيقونة - ستستخدم الافتراضية
    )
)

echo.
echo ✅ تم إعداد مجلد الأيقونات
echo.
echo 📁 فتح مجلد assets...
start explorer "assets"

echo.
echo 💡 نصائح:
echo - ضع ملف icon.ico في مجلد assets
echo - أعد تشغيل "اعمل-كل-شي.bat" لتطبيق الأيقونة
echo.
pause
