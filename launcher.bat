@echo off
title نظام إدارة المخزون - هيمن كروب
color 0A

:start
cls
echo.
echo ========================================
echo    نظام إدارة المخزون - هيمن كروب
echo ========================================
echo.
echo اختر طريقة التشغيل:
echo.
echo [1] تشغيل التطبيق كـ Electron App
echo [2] بناء ملف تنفيذي (.exe)
echo [3] فتح في المتصفح فقط
echo [4] تشغيل خادم محلي
echo [5] خروج
echo.
set /p choice="اختر رقم (1-5): "

if "%choice%"=="1" goto run_electron
if "%choice%"=="2" goto build_exe
if "%choice%"=="3" goto open_browser
if "%choice%"=="4" goto run_server
if "%choice%"=="5" goto exit
goto invalid

:run_electron
echo.
echo تشغيل التطبيق كـ Electron App...
echo هذا يوفر تجربة تطبيق سطح مكتب كاملة
echo.
call run.bat
goto end

:build_exe
echo.
echo بناء ملف تنفيذي...
echo سيتم إنشاء ملف .exe قابل للتوزيع
echo.
call build.bat
goto end

:open_browser
echo.
echo فتح في المتصفح...
start index-professional.html
echo تم فتح النظام في المتصفح الافتراضي
goto end

:run_server
echo.
echo تشغيل خادم محلي...
echo سيتم تشغيل خادم HTTP محلي
echo.
python -m http.server 8080 2>nul || (
    echo Python غير متوفر، استخدام طريقة أخرى...
    start index-professional.html
)
goto end

:invalid
echo.
echo اختيار غير صحيح! يرجى اختيار رقم من 1 إلى 5.
echo.
pause
goto start

:exit
echo.
echo شكراً لاستخدام نظام إدارة المخزون!
echo هيمن كروب - 2024
timeout /t 2 >nul
exit

:end
echo.
echo اضغط أي مفتاح للعودة للقائمة الرئيسية...
pause >nul
goto start
