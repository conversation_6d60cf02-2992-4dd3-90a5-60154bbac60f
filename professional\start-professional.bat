@echo off
title نظام إدارة المخزون الاحترافي v2.0
color 0B

echo.
echo     ██████╗ ██████╗  ██████╗ ███████╗███████╗███████╗██╗ ██████╗ ███╗   ██╗ █████╗ ██╗     
echo     ██╔══██╗██╔══██╗██╔═══██╗██╔════╝██╔════╝██╔════╝██║██╔═══██╗████╗  ██║██╔══██╗██║     
echo     ██████╔╝██████╔╝██║   ██║█████╗  █████╗  ███████╗██║██║   ██║██╔██╗ ██║███████║██║     
echo     ██╔═══╝ ██╔══██╗██║   ██║██╔══╝  ██╔══╝  ╚════██║██║██║   ██║██║╚██╗██║██╔══██║██║     
echo     ██║     ██║  ██║╚██████╔╝██║     ███████╗███████║██║╚██████╔╝██║ ╚████║██║  ██║███████╗
echo     ╚═╝     ╚═╝  ╚═╝ ╚═════╝ ╚═╝     ╚══════╝╚══════╝╚═╝ ╚═════╝ ╚═╝  ╚═══╝╚═╝  ╚═╝╚══════╝
echo.
echo                           نظام إدارة المخزون الاحترافي v2.0
echo                                  Professional Inventory System
echo.
echo ========================================================================================================
echo.

REM التحقق من وجود Java
echo 🔍 التحقق من متطلبات النظام...
java -version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Java غير مثبت أو غير موجود في PATH
    echo.
    echo 💡 يرجى تثبيت Java 8 أو أحدث من:
    echo    https://www.oracle.com/java/technologies/downloads/
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Java متوفر
)

REM التحقق من وجود الملفات المجمعة
if not exist "out\com\inventory\ProfessionalInventoryApp.class" (
    echo.
    echo 🔨 الملفات غير مجمعة، جاري التجميع التلقائي...
    call compile-professional.bat
    
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ فشل في التجميع التلقائي
        pause
        exit /b 1
    )
) else (
    echo ✅ الملفات مجمعة ومتوفرة
)

echo.
echo 🚀 تشغيل النظام الاحترافي...
echo.
echo 📋 معلومات تسجيل الدخول الافتراضية:
echo    👤 اسم المستخدم: admin
echo    🔐 كلمة المرور: admin123
echo.
echo ⏳ جاري تحميل النظام...

REM تشغيل التطبيق مع معالجة الأخطاء
java --enable-native-access=ALL-UNNAMED -Dfile.encoding=UTF-8 -Dsun.java2d.uiScale=1.0 -cp "out;lib/*" com.inventory.ProfessionalInventoryApp 2>error.log

REM في حالة فشل التشغيل المتقدم
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ⚠️ فشل التشغيل بالوضع المتقدم، جاري المحاولة بالوضع البسيط...
    java -Dfile.encoding=UTF-8 -cp "out;lib/*" com.inventory.ProfessionalInventoryApp 2>>error.log
    
    if %ERRORLEVEL% NEQ 0 (
        echo.
        echo ❌ فشل في تشغيل النظام
        echo.
        echo 🔍 تحقق من:
        echo    • وجود جميع ملفات المكتبات في مجلد lib
        echo    • صحة ملف قاعدة البيانات
        echo    • عدم تشغيل نسخة أخرى من النظام
        echo.
        if exist error.log (
            echo 📄 تفاصيل الخطأ محفوظة في ملف error.log
        )
        pause
        exit /b 1
    )
)

echo.
echo ✅ تم إغلاق النظام بنجاح
echo.
echo 💡 نصائح:
echo    • يمكنك تغيير كلمة مرور المدير من داخل النظام
echo    • جميع البيانات محفوظة في ملف professional_inventory.db
echo    • لا تحذف مجلد lib أو ملفات قاعدة البيانات
echo.
pause
