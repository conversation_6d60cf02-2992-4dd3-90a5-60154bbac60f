<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .alert {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار بسيط للنظام</h1>
        
        <div class="status">
            <h3>حالة النظام:</h3>
            <p id="systemStatus">جاري التحقق...</p>
        </div>
        
        <div id="alertContainer"></div>
        
        <div style="text-align: center;">
            <button class="btn" onclick="testBasic()">🧪 اختبار أساسي</button>
            <button class="btn" onclick="testLocalStorage()">💾 اختبار التخزين</button>
            <button class="btn" onclick="testMainSystem()">🚀 اختبار النظام الرئيسي</button>
            <button class="btn" onclick="clearAllData()">🗑️ مسح البيانات</button>
        </div>
        
        <div id="testResults" style="margin-top: 20px;"></div>
    </div>

    <script>
        // اختبار بسيط جداً
        function showAlert(message, type = 'success') {
            const alertContainer = document.getElementById('alertContainer');
            const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            
            alertContainer.innerHTML = `
                <div class="alert ${alertClass}">
                    ${message}
                </div>
            `;
            
            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 3000);
        }
        
        function testBasic() {
            console.log('🧪 بدء الاختبار الأساسي...');
            showAlert('✅ الاختبار الأساسي نجح! JavaScript يعمل بشكل طبيعي.', 'success');
            
            document.getElementById('testResults').innerHTML = `
                <div style="background: #e7f3ff; padding: 15px; border-radius: 8px;">
                    <h4>✅ نتائج الاختبار الأساسي:</h4>
                    <ul>
                        <li>✅ تحميل JavaScript: نجح</li>
                        <li>✅ الأزرار تعمل: نجح</li>
                        <li>✅ عرض الرسائل: نجح</li>
                        <li>✅ تحديث DOM: نجح</li>
                    </ul>
                </div>
            `;
        }
        
        function testLocalStorage() {
            try {
                // اختبار الكتابة
                localStorage.setItem('test_key', 'test_value');
                
                // اختبار القراءة
                const value = localStorage.getItem('test_key');
                
                if (value === 'test_value') {
                    showAlert('✅ localStorage يعمل بشكل طبيعي!', 'success');
                    
                    // عرض البيانات الموجودة
                    const items = JSON.parse(localStorage.getItem('inventory_items')) || [];
                    const transactions = JSON.parse(localStorage.getItem('inventory_transactions')) || [];
                    
                    document.getElementById('testResults').innerHTML = `
                        <div style="background: #e7f3ff; padding: 15px; border-radius: 8px;">
                            <h4>💾 حالة التخزين:</h4>
                            <ul>
                                <li>✅ localStorage متاح: نعم</li>
                                <li>📦 عدد المواد المحفوظة: ${items.length}</li>
                                <li>📋 عدد المعاملات المحفوظة: ${transactions.length}</li>
                            </ul>
                        </div>
                    `;
                } else {
                    throw new Error('فشل في قراءة البيانات');
                }
                
                // تنظيف
                localStorage.removeItem('test_key');
                
            } catch (error) {
                showAlert('❌ خطأ في localStorage: ' + error.message, 'danger');
            }
        }
        
        function testMainSystem() {
            try {
                // محاولة تحميل النظام الرئيسي
                const script = document.createElement('script');
                script.src = 'app.js';
                script.onload = function() {
                    showAlert('✅ تم تحميل النظام الرئيسي بنجاح!', 'success');
                    setTimeout(() => {
                        window.open('index.html', '_blank');
                    }, 1000);
                };
                script.onerror = function() {
                    showAlert('❌ فشل في تحميل النظام الرئيسي!', 'danger');
                };
                document.head.appendChild(script);
                
            } catch (error) {
                showAlert('❌ خطأ في اختبار النظام الرئيسي: ' + error.message, 'danger');
            }
        }
        
        function clearAllData() {
            if (confirm('هل أنت متأكد من حذف جميع البيانات؟')) {
                try {
                    localStorage.clear();
                    showAlert('✅ تم مسح جميع البيانات!', 'success');
                    
                    document.getElementById('testResults').innerHTML = `
                        <div style="background: #fff3cd; padding: 15px; border-radius: 8px;">
                            <h4>🗑️ تم مسح البيانات:</h4>
                            <p>تم حذف جميع البيانات المحفوظة. يمكنك الآن البدء من جديد.</p>
                        </div>
                    `;
                } catch (error) {
                    showAlert('❌ خطأ في مسح البيانات: ' + error.message, 'danger');
                }
            }
        }
        
        // فحص حالة النظام عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تم تحميل صفحة الاختبار');
            
            const statusEl = document.getElementById('systemStatus');
            
            try {
                // فحص localStorage
                const storageTest = localStorage.getItem('test') || 'متاح';
                
                // فحص البيانات الموجودة
                const items = JSON.parse(localStorage.getItem('inventory_items')) || [];
                const transactions = JSON.parse(localStorage.getItem('inventory_transactions')) || [];
                
                statusEl.innerHTML = `
                    <strong style="color: green;">✅ النظام يعمل بشكل طبيعي</strong><br>
                    📦 المواد المحفوظة: ${items.length}<br>
                    📋 المعاملات المحفوظة: ${transactions.length}<br>
                    💾 التخزين المحلي: متاح
                `;
                
            } catch (error) {
                statusEl.innerHTML = `
                    <strong style="color: red;">❌ يوجد مشكلة في النظام</strong><br>
                    خطأ: ${error.message}
                `;
            }
        });
    </script>
</body>
</html>
