package com.inventory.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * أدوات تشفير كلمات المرور - نظام إدارة المخزون الاحترافي
 */
public class PasswordUtils {
    private static final String ALGORITHM = "SHA-256";
    private static final int SALT_LENGTH = 16;
    
    /**
     * تشفير كلمة المرور مع Salt
     */
    public static String hashPassword(String password) {
        try {
            // إنشاء salt عشوائي
            SecureRandom random = new SecureRandom();
            byte[] salt = new byte[SALT_LENGTH];
            random.nextBytes(salt);
            
            // تشفير كلمة المرور
            MessageDigest md = MessageDigest.getInstance(ALGORITHM);
            md.update(salt);
            byte[] hashedPassword = md.digest(password.getBytes());
            
            // دمج Salt مع كلمة المرور المشفرة
            byte[] combined = new byte[salt.length + hashedPassword.length];
            System.arraycopy(salt, 0, combined, 0, salt.length);
            System.arraycopy(hashedPassword, 0, combined, salt.length, hashedPassword.length);
            
            return Base64.getEncoder().encodeToString(combined);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("خطأ في تشفير كلمة المرور", e);
        }
    }
    
    /**
     * التحقق من كلمة المرور
     */
    public static boolean verifyPassword(String password, String hashedPassword) {
        try {
            byte[] combined = Base64.getDecoder().decode(hashedPassword);
            
            // استخراج Salt
            byte[] salt = new byte[SALT_LENGTH];
            System.arraycopy(combined, 0, salt, 0, SALT_LENGTH);
            
            // استخراج كلمة المرور المشفرة
            byte[] hash = new byte[combined.length - SALT_LENGTH];
            System.arraycopy(combined, SALT_LENGTH, hash, 0, hash.length);
            
            // تشفير كلمة المرور المدخلة بنفس Salt
            MessageDigest md = MessageDigest.getInstance(ALGORITHM);
            md.update(salt);
            byte[] testHash = md.digest(password.getBytes());
            
            // مقارنة النتائج
            return MessageDigest.isEqual(hash, testHash);
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * إنشاء كلمة مرور عشوائية
     */
    public static String generateRandomPassword(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
        SecureRandom random = new SecureRandom();
        StringBuilder password = new StringBuilder();
        
        for (int i = 0; i < length; i++) {
            password.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        return password.toString();
    }
    
    /**
     * التحقق من قوة كلمة المرور
     */
    public static PasswordStrength checkPasswordStrength(String password) {
        if (password == null || password.length() < 4) {
            return PasswordStrength.VERY_WEAK;
        }
        
        int score = 0;
        
        // الطول
        if (password.length() >= 8) score++;
        if (password.length() >= 12) score++;
        
        // الأحرف الكبيرة
        if (password.matches(".*[A-Z].*")) score++;
        
        // الأحرف الصغيرة
        if (password.matches(".*[a-z].*")) score++;
        
        // الأرقام
        if (password.matches(".*[0-9].*")) score++;
        
        // الرموز الخاصة
        if (password.matches(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*")) score++;
        
        switch (score) {
            case 0:
            case 1:
                return PasswordStrength.VERY_WEAK;
            case 2:
                return PasswordStrength.WEAK;
            case 3:
                return PasswordStrength.MEDIUM;
            case 4:
                return PasswordStrength.STRONG;
            case 5:
            case 6:
                return PasswordStrength.VERY_STRONG;
            default:
                return PasswordStrength.WEAK;
        }
    }
    
    public enum PasswordStrength {
        VERY_WEAK("ضعيف جداً", "#F44336"),
        WEAK("ضعيف", "#FF9800"),
        MEDIUM("متوسط", "#FFC107"),
        STRONG("قوي", "#4CAF50"),
        VERY_STRONG("قوي جداً", "#2E7D32");
        
        private final String arabicName;
        private final String color;
        
        PasswordStrength(String arabicName, String color) {
            this.arabicName = arabicName;
            this.color = color;
        }
        
        public String getArabicName() {
            return arabicName;
        }
        
        public String getColor() {
            return color;
        }
    }
}
