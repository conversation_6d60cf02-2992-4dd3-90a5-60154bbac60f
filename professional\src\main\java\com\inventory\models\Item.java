package com.inventory.models;

import java.time.LocalDateTime;

/**
 * نموذج المادة المحسن - نظام إدارة المخزون الاحترافي
 */
public class Item {
    private int id;
    private String name;
    private String description;
    private String barcode;
    private String unit;
    private int categoryId;
    private Category category;
    private double currentStock;
    private double minStock; // الحد الأدنى للتنبيه
    private double maxStock; // الحد الأقصى
    private double unitPrice;
    private double lastPurchasePrice;
    private String location; // موقع التخزين
    private String imageUrl;
    private boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String notes;
    
    // Constructors
    public Item() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.isActive = true;
        this.currentStock = 0.0;
        this.minStock = 0.0;
        this.maxStock = 0.0;
    }
    
    public Item(String name, String unit, int categoryId, double unitPrice) {
        this();
        this.name = name;
        this.unit = unit;
        this.categoryId = categoryId;
        this.unitPrice = unitPrice;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
        this.updatedAt = LocalDateTime.now();
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
        this.updatedAt = LocalDateTime.now();
    }
    
    public String getBarcode() {
        return barcode;
    }
    
    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }
    
    public String getUnit() {
        return unit;
    }
    
    public void setUnit(String unit) {
        this.unit = unit;
    }
    
    public int getCategoryId() {
        return categoryId;
    }
    
    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }
    
    public Category getCategory() {
        return category;
    }
    
    public void setCategory(Category category) {
        this.category = category;
        if (category != null) {
            this.categoryId = category.getId();
        }
    }
    
    public double getCurrentStock() {
        return currentStock;
    }
    
    public void setCurrentStock(double currentStock) {
        this.currentStock = currentStock;
        this.updatedAt = LocalDateTime.now();
    }
    
    public double getMinStock() {
        return minStock;
    }
    
    public void setMinStock(double minStock) {
        this.minStock = minStock;
    }
    
    public double getMaxStock() {
        return maxStock;
    }
    
    public void setMaxStock(double maxStock) {
        this.maxStock = maxStock;
    }
    
    public double getUnitPrice() {
        return unitPrice;
    }
    
    public void setUnitPrice(double unitPrice) {
        this.unitPrice = unitPrice;
        this.updatedAt = LocalDateTime.now();
    }
    
    public double getLastPurchasePrice() {
        return lastPurchasePrice;
    }
    
    public void setLastPurchasePrice(double lastPurchasePrice) {
        this.lastPurchasePrice = lastPurchasePrice;
    }
    
    public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
    }
    
    public String getImageUrl() {
        return imageUrl;
    }
    
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    
    public boolean isActive() {
        return isActive;
    }
    
    public void setActive(boolean active) {
        isActive = active;
        this.updatedAt = LocalDateTime.now();
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
        this.updatedAt = LocalDateTime.now();
    }
    
    // Helper methods
    public boolean isLowStock() {
        return currentStock <= minStock && minStock > 0;
    }
    
    public boolean isOverStock() {
        return maxStock > 0 && currentStock > maxStock;
    }
    
    public double getTotalValue() {
        return currentStock * unitPrice;
    }
    
    public String getStockStatus() {
        if (isLowStock()) {
            return "مخزون منخفض";
        } else if (isOverStock()) {
            return "مخزون زائد";
        } else {
            return "طبيعي";
        }
    }
    
    @Override
    public String toString() {
        return name + " (" + unit + ")";
    }
}
