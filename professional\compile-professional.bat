@echo off
echo ========================================
echo   تجميع نظام إدارة المخزون الاحترافي
echo ========================================
echo.

REM التحقق من وجود Java
java -version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Java غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Java 8 أو أحدث
    pause
    exit /b 1
)

REM إنشاء المجلدات المطلوبة
echo 📁 إنشاء المجلدات...
if not exist "lib" mkdir lib
if not exist "out" mkdir out
if not exist "resources" mkdir resources

REM تحميل المكتبات المطلوبة إذا لم تكن موجودة
echo 📦 التحقق من المكتبات المطلوبة...

if not exist "lib\sqlite-jdbc-3.44.1.0.jar" (
    echo تحميل SQLite JDBC...
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/xerial/sqlite-jdbc/3.44.1.0/sqlite-jdbc-3.44.1.0.jar' -OutFile 'lib\sqlite-jdbc-3.44.1.0.jar'"
)

if not exist "lib\slf4j-api-1.7.36.jar" (
    echo تحميل SLF4J API...
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar' -OutFile 'lib\slf4j-api-1.7.36.jar'"
)

if not exist "lib\slf4j-simple-1.7.36.jar" (
    echo تحميل SLF4J Simple...
    powershell -Command "Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar' -OutFile 'lib\slf4j-simple-1.7.36.jar'"
)

REM محاولة تحميل FlatLaf للواجهة الحديثة
if not exist "lib\flatlaf-3.2.5.jar" (
    echo تحميل FlatLaf للواجهة الحديثة...
    powershell -Command "try { Invoke-WebRequest -Uri 'https://repo1.maven.org/maven2/com/formdev/flatlaf/3.2.5/flatlaf-3.2.5.jar' -OutFile 'lib\flatlaf-3.2.5.jar' } catch { Write-Host 'فشل تحميل FlatLaf - سيتم استخدام الواجهة الافتراضية' }"
)

echo.
echo 🔨 تجميع الكود المصدري...

REM تجميع جميع ملفات Java
javac -d out -cp "lib/*" -encoding UTF-8 src/main/java/com/inventory/models/*.java src/main/java/com/inventory/utils/*.java src/main/java/com/inventory/database/*.java src/main/java/com/inventory/gui/*.java src/main/java/com/inventory/*.java

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم التجميع بنجاح!
    echo.
    echo 📋 ملفات التشغيل:
    echo   • run-professional.bat - تشغيل النظام الاحترافي
    echo   • run-professional-simple.bat - تشغيل بدون خيارات متقدمة
    echo.
    echo 🔐 معلومات تسجيل الدخول الافتراضية:
    echo   اسم المستخدم: admin
    echo   كلمة المرور: admin123
    echo.
    echo 💡 نصائح:
    echo   • تأكد من وجود اتصال بالإنترنت لتحميل المكتبات
    echo   • يمكنك تغيير كلمة مرور المدير من داخل النظام
    echo   • جميع البيانات محفوظة في ملف professional_inventory.db
) else (
    echo.
    echo ❌ فشل في التجميع!
    echo تحقق من:
    echo   • وجود Java بإصدار 8 أو أحدث
    echo   • صحة ملفات الكود المصدري
    echo   • وجود المكتبات في مجلد lib
)

echo.
pause
