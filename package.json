{"name": "himen-inventory-system", "version": "1.0.0", "description": "نظام إدارة المخزون - هيمن كروب", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "build-win": "electron-builder --win", "dist": "npm run build"}, "author": "<PERSON><PERSON><PERSON><PERSON> كروب", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "build": {"appId": "com.himengroup.inventory", "productName": "نظام إدارة المخزون - هيمن كروب", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules", "!dist"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "نظام إدارة المخزون"}}}